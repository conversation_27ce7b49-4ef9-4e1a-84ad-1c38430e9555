CREATE OR REPLACE FUNCTION public.tms_get_technician_names_fr_cost_breakdown(srvc_req_id_ integer)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
declare
	resp_data_ text;
begin
	     SELECT string_agg(users_.name, ', ')
		  FROM cl_tx_srvc_req as srvc_req_,
		    jsonb_array_elements(srvc_req_.profit_and_loss_data -> 'technician_data_fr_cost_breakdown') AS tech_data
		 inner join  cl_tx_users as users_
		    ON users_.usr_id::text = tech_data ->> 'user_id'
		 WHERE srvc_req_.db_id = srvc_req_id_
		 into resp_data_; 
	return resp_data_;
END;
$function$
;
