{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Github\\\\wify_tms_v2\\\\frontend\\\\react-app1\\\\src\\\\routes\\\\setup\\\\srvc-req\\\\sp-fields\\\\SpConfigEditor.js\";\nimport React, { Component } from 'react';\nimport { Modal, Form, Input, Button, Collapse, Tabs, Alert } from 'antd';\nimport FormBuilder from 'antd-form-builder';\nimport http_utils from '../../../../util/http_utils';\nimport CircularProgress from '../../../../components/CircularProgress';\nimport FormPreviewMeta from '../../../../components/wify-utils/FieldCreator/FormPreviewMeta';\nimport { ZoomInOutlined } from '@ant-design/icons';\nimport { Link } from 'react-router-dom';\nimport LineItemManagementModule from '../service-types/project-components/LineItemManagementModule';\nimport PricingMaster from '../service-types/PricingMaster';\nimport TimePickerWidget from '../../../../components/wify-utils/TimePickerWidget';\nimport { BILLING_TYPE, PROJECT_BASED_SERVICE_TYPE, RATING_TYPE, getOptionsFrNatureOfServiceType, handleClearSelect, validateLambdaArn } from '../../../../util/helpers';\nimport { decodeFieldsMetaFrmJson, decodeFileSectionsFrmJson } from '../../../../components/wify-utils/FieldCreator/helpers';\nimport SpecificFieldsWiseNotificationModule from '../service-types/project-components/SpecificFieldsWiseNotificationModule';\nimport BillingDiscountingConfigModule from '../service-types/project-components/BillingDiscountingConfigModule';\nimport { addDefaultKeysForTabularView, getDefaultColumnsForTabularView, getRatingFormMeta, getSrvcReqStaticAndCustomPossibleFields } from '../../../services/helpers';\nimport { getSrvcReqStaticAndCustomPossibleFieldsFilter } from '../../../services/helpers';\nimport UserSelectorWidget from '../../../../components/wify-utils/UserSelectorWidget';\nimport { getUsersInfoMeta } from '../../../users/helper';\nimport ConfigHelpers from '../../../../util/ConfigHelpers';\nimport { SP_USER_EXCLUDED_FIELDS } from '../../../../util/constants';\nimport InputTable from '../../../../components/WIFY/subtasks/InputTable';\nimport ProfitLossConfig from './ProfitLossConfig.tsx';\nimport ProjectPLConfig from './ProjectPLConfig.tsx';\nimport InlineCheckbox from '../../../../components/WIFY/InlineCheckbox.tsx';\nconst protoUrl = '/setup/sp-custom-fields/proto';\nconst submitUrl = '/setup/sp-custom-fields';\nconst {\n  TextArea\n} = Input;\nconst startOfDay = '12:00AM';\nconst endOfDay = '11:45PM';\nconst DailyUpdateModes = [{\n  value: 'lenient_mode',\n  label: 'Lenient mode(Progress is autofilled till the previous date and no restriction in progress slider movement)'\n}, {\n  value: 'strict_mode',\n  label: 'Strict mode(Progress is autofilled till the previous date and cannot be modified out of permissible range)'\n}];\nclass SpConfigEditor extends Component {\n  constructor(_props) {\n    super(_props);\n    this.initState = {\n      render_helper: false,\n      visible: false,\n      isFormSubmitting: false,\n      viewData: undefined,\n      isLoadingViewData: false,\n      editMode: this.props.editMode,\n      error: '',\n      editModeForceRefreshDone: false,\n      spSrvcTypeSpecificFieldsNotification: undefined,\n      profitLossTab: false,\n      revenuecol: []\n    };\n    this.state = this.initState;\n    this.handleOk = () => {\n      this.setState({\n        visible: false,\n        isFormSubmitting: false\n      });\n      this.updateClosureToParent();\n    };\n    this.handleCancel = () => {\n      this.setState({\n        visible: false\n      });\n      this.updateClosureToParent();\n    };\n    this.submitForm = data => {\n      this.setState({\n        isFormSubmitting: true\n      });\n      var params = data;\n      if (data === null || data === void 0 ? void 0 : data.srvc_type_revenue_column_meta) {\n        const srvc_type_revenue_column_meta = JSON.parse(data === null || data === void 0 ? void 0 : data.srvc_type_revenue_column_meta);\n        if (srvc_type_revenue_column_meta) {\n          srvc_type_revenue_column_meta.forEach(singleObj => {\n            var _this$state$viewData, _this$state$viewData$, _this$state$viewData$2, _this$state$viewData2, _this$state$viewData3, _this$state$viewData4, _this$state$viewData5, _this$state$viewData6, _this$state$viewData7;\n            console.log('yeti singleObj', singleObj);\n            if (((_this$state$viewData = this.state.viewData) === null || _this$state$viewData === void 0 ? void 0 : (_this$state$viewData$ = _this$state$viewData.form_data) === null || _this$state$viewData$ === void 0 ? void 0 : (_this$state$viewData$2 = _this$state$viewData$.form_data) === null || _this$state$viewData$2 === void 0 ? void 0 : _this$state$viewData$2[`sku_${singleObj.row_id}`]) && !(data === null || data === void 0 ? void 0 : data[`sku_${singleObj.row_id}`])) {\n              params[`sku_${singleObj.row_id}`] = (singleObj === null || singleObj === void 0 ? void 0 : singleObj.sku) || '';\n            }\n            if (((_this$state$viewData2 = this.state.viewData) === null || _this$state$viewData2 === void 0 ? void 0 : (_this$state$viewData3 = _this$state$viewData2.form_data) === null || _this$state$viewData3 === void 0 ? void 0 : (_this$state$viewData4 = _this$state$viewData3.form_data) === null || _this$state$viewData4 === void 0 ? void 0 : _this$state$viewData4[`item_name_${singleObj.row_id}`]) && !(data === null || data === void 0 ? void 0 : data[`item_name_${singleObj.row_id}`])) {\n              params[`item_name_${singleObj.row_id}`] = (singleObj === null || singleObj === void 0 ? void 0 : singleObj.item_name) || '';\n            }\n            if (((_this$state$viewData5 = this.state.viewData) === null || _this$state$viewData5 === void 0 ? void 0 : (_this$state$viewData6 = _this$state$viewData5.form_data) === null || _this$state$viewData6 === void 0 ? void 0 : (_this$state$viewData7 = _this$state$viewData6.form_data) === null || _this$state$viewData7 === void 0 ? void 0 : _this$state$viewData7[`qty_${singleObj.row_id}`]) && !(data === null || data === void 0 ? void 0 : data[`qty_${singleObj.row_id}`])) {\n              params[`qty_${singleObj.row_id}`] = (singleObj === null || singleObj === void 0 ? void 0 : singleObj.qty) || '';\n            }\n          });\n        }\n      }\n      if (data === null || data === void 0 ? void 0 : data.revenue_column_meta) {\n        const revenue_column_meta = JSON.parse(data === null || data === void 0 ? void 0 : data.revenue_column_meta);\n        if (revenue_column_meta) {\n          revenue_column_meta.forEach(singleObj => {\n            var _this$state$viewData8, _this$state$viewData9, _this$state$viewData0, _this$state$viewData1, _this$state$viewData10, _this$state$viewData11, _this$state$viewData12, _this$state$viewData13, _this$state$viewData14;\n            if (((_this$state$viewData8 = this.state.viewData) === null || _this$state$viewData8 === void 0 ? void 0 : (_this$state$viewData9 = _this$state$viewData8.form_data) === null || _this$state$viewData9 === void 0 ? void 0 : (_this$state$viewData0 = _this$state$viewData9.form_data) === null || _this$state$viewData0 === void 0 ? void 0 : _this$state$viewData0[`sku_${singleObj.row_id}`]) && !(data === null || data === void 0 ? void 0 : data[`sku_${singleObj.row_id}`])) {\n              params[`sku_${singleObj.row_id}`] = '';\n            }\n            if (((_this$state$viewData1 = this.state.viewData) === null || _this$state$viewData1 === void 0 ? void 0 : (_this$state$viewData10 = _this$state$viewData1.form_data) === null || _this$state$viewData10 === void 0 ? void 0 : (_this$state$viewData11 = _this$state$viewData10.form_data) === null || _this$state$viewData11 === void 0 ? void 0 : _this$state$viewData11[`item_name_${singleObj.row_id}`]) && !(data === null || data === void 0 ? void 0 : data[`item_name_${singleObj.row_id}`])) {\n              params[`item_name_${singleObj.row_id}`] = '';\n            }\n            if (((_this$state$viewData12 = this.state.viewData) === null || _this$state$viewData12 === void 0 ? void 0 : (_this$state$viewData13 = _this$state$viewData12.form_data) === null || _this$state$viewData13 === void 0 ? void 0 : (_this$state$viewData14 = _this$state$viewData13.form_data) === null || _this$state$viewData14 === void 0 ? void 0 : _this$state$viewData14[`qty_${singleObj.row_id}`]) && !(data === null || data === void 0 ? void 0 : data[`qty_${singleObj.row_id}`])) {\n              params[`qty_${singleObj.row_id}`] = '';\n            }\n          });\n        }\n      }\n      const onComplete = resp => {\n        this.setState({\n          isFormSubmitting: false,\n          error: '',\n          visible: false\n        });\n        this.tellParentToRefreshList(resp.entry_id);\n        this.updateClosureToParent();\n      };\n      const onError = error => {\n        // compare statuses here\n        this.setState({\n          isFormSubmitting: false,\n          error: http_utils.decodeErrorToMessage(error)\n        });\n      };\n      if (this.state.editMode) {\n        http_utils.performPutCall(submitUrl + '/' + this.props.editorItem.id, params, onComplete, onError);\n      } else {\n        http_utils.performPostCall(submitUrl, params, onComplete, onError);\n      }\n    };\n    this.getMeta = () => {\n      var _this$formRef, _this$formRef$current, _this$formRef2, _this$formRef2$curren, _this$state$viewData15;\n      const initialValues = this.getInitialValues();\n      let FieldsInitialValues = initialValues;\n      if ((initialValues === null || initialValues === void 0 ? void 0 : initialValues.sp_cust_fields_json) == undefined || (initialValues === null || initialValues === void 0 ? void 0 : initialValues.sp_cust_fields_json) == '') {\n        FieldsInitialValues = {};\n      }\n      var customFields = (_this$formRef = this.formRef) === null || _this$formRef === void 0 ? void 0 : (_this$formRef$current = _this$formRef.current) === null || _this$formRef$current === void 0 ? void 0 : _this$formRef$current.getFieldValue('sp_cust_fields_json');\n      if (customFields == undefined) {\n        customFields = initialValues === null || initialValues === void 0 ? void 0 : initialValues.sp_cust_fields_json;\n      }\n      const isCustFieldDynamicEnabled = (_this$formRef2 = this.formRef) === null || _this$formRef2 === void 0 ? void 0 : (_this$formRef2$curren = _this$formRef2.current) === null || _this$formRef2$curren === void 0 ? void 0 : _this$formRef2$curren.getFieldValue('is_custom_fields_dynamic');\n      const meta = {\n        columns: 1,\n        formItemLayout: null,\n        initialValues: FieldsInitialValues,\n        fields: [{\n          key: 'vertical_title',\n          label: 'Vertical title(Unique)',\n          required: true,\n          rules: [{\n            max: 50\n          }]\n        }, {\n          key: 'vertical_desc',\n          label: 'Vertical description',\n          widget: 'textarea'\n        }, {\n          label: /*#__PURE__*/React.createElement(\"span\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 28\n            }\n          }, \"Nature of requests \"),\n          key: 'vertical_nature',\n          widget: 'radio-group',\n          required: true,\n          onChange: () => {\n            this.formRef.current.setFieldsValue({\n              srvc_type_id: []\n            });\n            this.refresh();\n          },\n          // widgetProps:{\n          //     defaultValue:'task_based'\n          // },\n          options: getOptionsFrNatureOfServiceType()\n        }, {\n          key: 'srvc_type_id',\n          label: 'Select organization name',\n          widget: 'select',\n          widgetProps: {\n            mode: 'multiple',\n            optionFilterProp: 'children',\n            onChange: () => this.refresh()\n          },\n          options: this.getOrgListBasedOnNature(),\n          rules: [{\n            required: true,\n            message: 'Please select organization name'\n          }],\n          placeholder: 'Please select org name'\n        }, {\n          label: 'Enter custom fields json',\n          key: 'sp_cust_fields_json',\n          widget: 'textarea',\n          placeholder: 'Please paste the JSON here',\n          rules: [{\n            required: true,\n            message: 'Please enter custom fields json'\n          }],\n          onChange: event => {\n            this.setState({\n              render_helper: !this.state.render_helper\n            });\n          }\n        }, {\n          key: 'link_to_field_creator',\n          render: () => {\n            return /*#__PURE__*/React.createElement(\"div\", {\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 29\n              }\n            }, customFields != '' && /*#__PURE__*/React.createElement(\"div\", {\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 585,\n                columnNumber: 37\n              }\n            }, /*#__PURE__*/React.createElement(Button, {\n              onClick: e => this.handleShowFieldsPreviewClick(customFields),\n              icon: /*#__PURE__*/React.createElement(ZoomInOutlined, {\n                __self: this,\n                __source: {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 51\n                }\n              }),\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 586,\n                columnNumber: 41\n              }\n            }, \"Form preview\")), /*#__PURE__*/React.createElement(Link, {\n              to: '/fields-creator?edit=' + encodeURIComponent(customFields),\n              target: \"_blank\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 33\n              }\n            }, \"Open field creator \", '-->'));\n          }\n        }, {\n          key: `is_custom_fields_dynamic`,\n          label: /*#__PURE__*/React.createElement(\"div\", {\n            className: \"gx-mt-2 \",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 25\n            }\n          }, \"Are custom fields dynamic?\"),\n          widget: 'checkbox',\n          onChange: () => this.refresh()\n        }, ...(isCustFieldDynamicEnabled ? [{\n          key: `sp_cust_fields_dynamic_form_lambda_arn`,\n          label: `Enter custom fields lambda arn`,\n          placeholder: 'Lambda ARN',\n          required: true,\n          widgetProps: {\n            allowClear: true,\n            onChange: value => {\n              handleClearSelect(value, this.formRef, 'sp_cust_fields_dynamic_form_lambda_arn');\n            }\n          },\n          rules: [{\n            validator: validateLambdaArn,\n            message: 'Please enter a valid Lambda ARN'\n          }]\n        }] : []), {\n          label: 'Service provider category field',\n          key: `srvc_prvdr_category_field`,\n          widget: 'select',\n          options: this.getPossibleSpecificFields([{\n            widget: 'select'\n          }]),\n          widgetProps: {\n            allowClear: true,\n            onChange: value => {\n              handleClearSelect(value, this.formRef, 'srvc_prvdr_category_field');\n            }\n          }\n        }, {\n          label: 'Specific location groups (Leave empty for all)',\n          key: `specific_loc_grps`,\n          widget: 'select',\n          options: ((_this$state$viewData15 = this.state.viewData) === null || _this$state$viewData15 === void 0 ? void 0 : _this$state$viewData15.location_grp_list) || [],\n          tooltip: 'If you want to restrict location groups that will be tagged to requests for this vertical',\n          widgetProps: {\n            mode: 'multiple',\n            optionFilterProp: 'children',\n            allowClear: true,\n            onChange: value => {\n              handleClearSelect(value, this.formRef, 'specific_loc_grps');\n            }\n          }\n        },\n        // ...this.getMetaFrLineItemConfiguration(),\n        ...this.getMetaFrSearchableCustomFields()]\n      };\n      return meta;\n    };\n    this.checkIfPresentInEssentialField = (singleField, essentialFields) => {\n      for (let essentialField of essentialFields) {\n        if (singleField.widget == essentialField.widget && singleField.widgetProps.mode != 'multiple') {\n          return true;\n        }\n      }\n    };\n    //Create a new component for Billing to be done later\n    this.getBillingformMeta = initialValues => {\n      var _this$state$viewData16, _this$state$viewData17, _this$state$viewData18, _this$state$viewData19, _this$state$viewData20;\n      const meta = {\n        formItemLayout: null,\n        initialValues: initialValues,\n        fields: [{\n          label: 'Enable Billing',\n          key: 'srvc_type_enable_billing',\n          widget: 'checkbox'\n        }, {\n          key: 'srvc_type_billing_type',\n          label: 'Billing Type',\n          widget: 'select',\n          widgetProps: {\n            mode: 'single'\n          },\n          options: BILLING_TYPE\n        }, {\n          key: 'srvc_type_categorize_specific_fields_for_billing',\n          label: 'Categorize specific fields for billing section',\n          widget: 'select',\n          widgetProps: {\n            mode: 'multiple',\n            showSearch: true,\n            optionFilterProp: 'children'\n          },\n          options: this.getPossibleSpecificFields() || []\n        }, {\n          key: 'srvc_type_who_can_lock_srvc_req_for_billing',\n          label: 'Who can lock service request for billing',\n          widget: 'select',\n          widgetProps: {\n            mode: 'multiple',\n            showSearch: true,\n            optionFilterProp: 'children'\n          },\n          options: ((_this$state$viewData16 = this.state.viewData) === null || _this$state$viewData16 === void 0 ? void 0 : _this$state$viewData16.role_list) || []\n        }, {\n          key: 'srvc_type_who_can_sync_srvc_req_prc',\n          label: 'Who can sync service request price',\n          widget: 'select',\n          widgetProps: {\n            mode: 'multiple',\n            showSearch: true,\n            optionFilterProp: 'children'\n          },\n          options: ((_this$state$viewData17 = this.state.viewData) === null || _this$state$viewData17 === void 0 ? void 0 : _this$state$viewData17.role_list) || []\n        }, {\n          key: 'srvc_type_who_can_send_req_for_billing',\n          label: 'Who can send a Request for billing',\n          widget: 'select',\n          widgetProps: {\n            mode: 'multiple',\n            showSearch: true,\n            optionFilterProp: 'children'\n          },\n          options: ((_this$state$viewData18 = this.state.viewData) === null || _this$state$viewData18 === void 0 ? void 0 : _this$state$viewData18.role_list) || []\n        }, {\n          key: 'srvc_type_who_will_get_notified_for_billing',\n          label: 'Who will get notified for billing',\n          widget: 'select',\n          widgetProps: {\n            mode: 'multiple',\n            showSearch: true,\n            optionFilterProp: 'children'\n          },\n          options: ((_this$state$viewData19 = this.state.viewData) === null || _this$state$viewData19 === void 0 ? void 0 : _this$state$viewData19.role_list) || []\n        }, {\n          key: 'hide_sp_billing_tab_from_specific_roles',\n          label: 'Who will not be able to see the billing tab',\n          widget: 'select',\n          widgetProps: {\n            mode: 'multiple',\n            showSearch: true,\n            optionFilterProp: 'children'\n          },\n          options: ((_this$state$viewData20 = this.state.viewData) === null || _this$state$viewData20 === void 0 ? void 0 : _this$state$viewData20.role_list) || []\n        }]\n      };\n      return meta;\n    };\n    this.getDiscountingBillingformMeta = initialValues => {\n      var _this$state$viewData21;\n      const meta = {\n        formItemLayout: null,\n        initialValues: initialValues,\n        fields: [{\n          label: 'Enable Discounting',\n          key: 'srvc_type_enable_billing_discounting',\n          widget: 'checkbox'\n        }, {\n          label: /*#__PURE__*/React.createElement(\"span\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1089,\n              columnNumber: 25\n            }\n          }, ' ', \"When discount approval status changes, notify\", ' '),\n          key: `srvc_type_discount_approval_status_changes_notify`,\n          widget: 'select',\n          options: ((_this$state$viewData21 = this.state.viewData) === null || _this$state$viewData21 === void 0 ? void 0 : _this$state$viewData21.role_list) || [],\n          widgetProps: {\n            allowClear: true,\n            mode: 'multiple'\n          }\n        }, {\n          key: `srvc_type_discounting_billing_master`,\n          render: () => {\n            var _this$state$viewData22;\n            return /*#__PURE__*/React.createElement(BillingDiscountingConfigModule, {\n              onChange: newObj => {\n                this.formRef.current.setFieldsValue({\n                  srvc_type_billing_discounting_rule_config: JSON.stringify(newObj)\n                });\n                this.refresh();\n              },\n              currValue: this.getBillingDiscountConfig(),\n              authorities_list: ((_this$state$viewData22 = this.state.viewData) === null || _this$state$viewData22 === void 0 ? void 0 : _this$state$viewData22.role_list) || [],\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1106,\n                columnNumber: 29\n              }\n            });\n          }\n        }]\n      };\n      return meta;\n    };\n    this.getAdditionalBillingformMeta = initialValues => {\n      const meta = {\n        formItemLayout: null,\n        initialValues: initialValues,\n        fields: [{\n          label: 'Enable Additional Billing',\n          key: 'srvc_type_enable_additional_billing',\n          widget: 'checkbox'\n        }, {\n          key: `srvc_type_additional_billing_master`,\n          render: () => {\n            return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(LineItemManagementModule, {\n              onChange: newObj => {\n                this.formRef.current.setFieldsValue({\n                  srvc_type_additional_billing_config: JSON.stringify(newObj)\n                });\n                this.refresh();\n              },\n              currValue: this.getAdditionalBillingJson(),\n              hideBtn: true,\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1142,\n                columnNumber: 33\n              }\n            }));\n          }\n        }]\n      };\n      return meta;\n    };\n    this.getAuthoritiesList = () => {\n      var _this$formRef3, _this$formRef3$curren, _this$state$viewData23;\n      let authoritiesList = [];\n      let authority_id = (_this$formRef3 = this.formRef) === null || _this$formRef3 === void 0 ? void 0 : (_this$formRef3$curren = _this$formRef3.current) === null || _this$formRef3$curren === void 0 ? void 0 : _this$formRef3$curren.getFieldValue('authority_id');\n      let roleList = (_this$state$viewData23 = this.state.viewData) === null || _this$state$viewData23 === void 0 ? void 0 : _this$state$viewData23.role_list;\n      if (authority_id && authority_id.length > 0) {\n        authority_id.forEach(singleAuthority => {\n          var _roleList$filter;\n          let filteredAuthorityList = (_roleList$filter = roleList.filter(singleRole => singleRole.value == singleAuthority)) === null || _roleList$filter === void 0 ? void 0 : _roleList$filter[0];\n          if (filteredAuthorityList) {\n            authoritiesList.push(filteredAuthorityList);\n          }\n        });\n      }\n      return authoritiesList;\n    };\n    this.getSpecificFieldsWiseNotificationFieldsMeta = initialValues => {\n      const meta = {\n        formItemLayout: null,\n        initialValues: initialValues,\n        fields: [{\n          label: 'Service type specific fields wise notification',\n          key: 'srvc_type_specific_fields_wise_notification',\n          formItemProps: {\n            style: {\n              display: 'none'\n            }\n          }\n        }, {\n          key: `sp_srvc_type_fields_wise_notification`,\n          render: () => {\n            var _this$formRef4, _this$formRef4$curren, _this$state$viewData24, _this$formRef5, _this$formRef5$curren;\n            return /*#__PURE__*/React.createElement(SpecificFieldsWiseNotificationModule, {\n              srvc_type_id: (_this$formRef4 = this.formRef) === null || _this$formRef4 === void 0 ? void 0 : (_this$formRef4$curren = _this$formRef4.current) === null || _this$formRef4$curren === void 0 ? void 0 : _this$formRef4$curren.getFieldValue('srvc_type_id'),\n              authorities_list: this.getAuthoritiesList(),\n              specific_fields: (_this$state$viewData24 = this.state.viewData) === null || _this$state$viewData24 === void 0 ? void 0 : _this$state$viewData24.brand_wise_specific_fields,\n              initialValue: this.getSpecificFieldsNotificationJson(),\n              onChange: newObj => {\n                // console.log(\"newObj\",newObj);\n                this.formRef.current.setFieldsValue({\n                  srvc_type_specific_fields_wise_notification: JSON.stringify(newObj)\n                });\n                this.refresh();\n              },\n              isSrvcPrvdrTab: true,\n              sp_specific_fields: (_this$formRef5 = this.formRef) === null || _this$formRef5 === void 0 ? void 0 : (_this$formRef5$curren = _this$formRef5.current) === null || _this$formRef5$curren === void 0 ? void 0 : _this$formRef5$curren.getFieldValue('sp_cust_fields_json'),\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1342,\n                columnNumber: 29\n              }\n            });\n          }\n        }]\n      };\n      return meta;\n    };\n    this.getDeploymentConfigMeta = initialValues => {\n      var _this$formRef$current2, _this$state$viewData25, _this$formRef6, _this$formRef6$curren, _this$state$viewData26, _this$state$viewData27, _this$state$viewData29, _this$state$viewData30, _this$state$viewData31, _this$formRef7, _this$formRef7$curren;\n      if (this.state.editMode && !this.state.editModeForceRefreshDone) {\n        // refreshing state to get form ref\n        this.setState({\n          editModeForceRefreshDone: true\n        });\n        return;\n      }\n      const startTimeFrEndTime = (_this$formRef$current2 = this.formRef.current) === null || _this$formRef$current2 === void 0 ? void 0 : _this$formRef$current2.getFieldValue('deployment_time_slot_lower_limit');\n      let role_vs_subtask_fields = [];\n      let possible_roles = ((_this$state$viewData25 = this.state.viewData) === null || _this$state$viewData25 === void 0 ? void 0 : _this$state$viewData25.role_list) || [];\n      var deployment_roles = ((_this$formRef6 = this.formRef) === null || _this$formRef6 === void 0 ? void 0 : (_this$formRef6$curren = _this$formRef6.current) === null || _this$formRef6$curren === void 0 ? void 0 : _this$formRef6$curren.getFieldValue('deployment_possible_roles')) || ((_this$state$viewData26 = this.state.viewData) === null || _this$state$viewData26 === void 0 ? void 0 : (_this$state$viewData27 = _this$state$viewData26.form_data) === null || _this$state$viewData27 === void 0 ? void 0 : _this$state$viewData27.deployment_possible_roles);\n      if (deployment_roles) {\n        deployment_roles.forEach(singleDeploymentRoleId => {\n          var _this$state$viewData28;\n          let role_details = possible_roles.filter(singleRole => singleRole.value == singleDeploymentRoleId)[0] || {};\n          role_vs_subtask_fields.push({\n            key: `sbtsk_fr_${singleDeploymentRoleId}`,\n            label: `Select subtask for ${role_details.label}`,\n            required: true,\n            widget: 'select',\n            options: ((_this$state$viewData28 = this.state.viewData) === null || _this$state$viewData28 === void 0 ? void 0 : _this$state$viewData28.applicable_subtasks_list) || []\n          });\n        });\n      }\n      if (initialValues && initialValues['deployment_time_slot_lower_limit'] == undefined) {\n        initialValues['deployment_time_slot_lower_limit'] = startOfDay;\n        initialValues['deployment_time_slot_upper_limit'] = endOfDay;\n      }\n      const meta = {\n        columns: 1,\n        formItemLayout: null,\n        initialValues: initialValues,\n        fields: [{\n          key: 'sp_deployment_who_can_edit',\n          label: 'Who can edit deployment ?',\n          widget: 'select',\n          widgetProps: {\n            mode: 'multiple',\n            allowClear: true,\n            showSearch: true\n          },\n          options: (_this$state$viewData29 = this.state.viewData) === null || _this$state$viewData29 === void 0 ? void 0 : _this$state$viewData29.role_list\n        }, {\n          key: 'deployment_time_slot_lower_limit',\n          label: /*#__PURE__*/React.createElement(\"span\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1538,\n              columnNumber: 25\n            }\n          }, /*#__PURE__*/React.createElement(\"i\", {\n            className: \"icon icon-timepicker gx-mr-2\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1539,\n              columnNumber: 29\n            }\n          }), \"Start Time Slot (Lower Limit)\"),\n          widget: TimePickerWidget,\n          widgetProps: {\n            beginLimit: startOfDay,\n            endLimit: endOfDay,\n            step: 15,\n            onChange: e => {\n              this.refresh();\n            }\n          }\n        }, {\n          key: 'deployment_time_slot_upper_limit',\n          label: /*#__PURE__*/React.createElement(\"span\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1556,\n              columnNumber: 25\n            }\n          }, /*#__PURE__*/React.createElement(\"i\", {\n            className: \"icon icon-timepicker gx-mr-2\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1557,\n              columnNumber: 29\n            }\n          }), \"End Time Slot (Upper Limit)\"),\n          widget: TimePickerWidget,\n          widgetProps: {\n            beginLimit: startTimeFrEndTime || startOfDay,\n            endLimit: endOfDay,\n            step: 15\n          }\n        }, {\n          key: `who_cannot_download_site_level_attendance`,\n          label: 'Who cannot download site level attendance ',\n          widget: 'select',\n          placeholder: 'Select roles',\n          onChange: () => this.refresh(),\n          widgetProps: {\n            mode: 'multiple',\n            allowClear: true,\n            showSearch: true,\n            optionFilterProp: 'children'\n          },\n          options: ((_this$state$viewData30 = this.state.viewData) === null || _this$state$viewData30 === void 0 ? void 0 : _this$state$viewData30.role_list) || []\n        }, {\n          key: `deployment_possible_roles`,\n          label: 'Please select roles to deploy',\n          widget: 'select',\n          placeholder: 'Select roles',\n          onChange: () => this.refresh(),\n          widgetProps: {\n            mode: 'multiple',\n            allowClear: true,\n            showSearch: true,\n            optionFilterProp: 'children'\n          },\n          options: ((_this$state$viewData31 = this.state.viewData) === null || _this$state$viewData31 === void 0 ? void 0 : _this$state$viewData31.role_list) || []\n        }, {\n          key: 'show_role_indicator_on_calendar',\n          label: 'Show role indicator on calendar',\n          widget: 'checkbox',\n          onChange: () => this.refresh()\n        }, {\n          key: 'show_deployed_role_on_calendar',\n          widget: 'radio-group',\n          options: [{\n            value: 'show_deployed_role',\n            label: 'Show deployed role'\n          }, {\n            value: 'show_role_code',\n            label: 'Show role code'\n          }],\n          initialValue: 'show_deployed_role',\n          formItemProps: {\n            style: {\n              display: ((_this$formRef7 = this.formRef) === null || _this$formRef7 === void 0 ? void 0 : (_this$formRef7$curren = _this$formRef7.current) === null || _this$formRef7$curren === void 0 ? void 0 : _this$formRef7$curren.getFieldValue('show_role_indicator_on_calendar')) ? 'block' : 'none'\n            }\n          }\n        }, ...role_vs_subtask_fields]\n      };\n      return meta;\n    };\n    this.getDailyUpdatesMeta = initialValues => {\n      var _this$state$viewData32, _this$formRef8, _this$formRef8$curren, _this$formRef9, _this$formRef9$curren, _this$formRef0, _this$formRef0$curren, _this$state$viewData33, _this$state$viewData34;\n      let possible_roles = ((_this$state$viewData32 = this.state.viewData) === null || _this$state$viewData32 === void 0 ? void 0 : _this$state$viewData32.role_list) || [];\n      var daily_update_issue_fields = (_this$formRef8 = this.formRef) === null || _this$formRef8 === void 0 ? void 0 : (_this$formRef8$curren = _this$formRef8.current) === null || _this$formRef8$curren === void 0 ? void 0 : _this$formRef8$curren.getFieldValue('daily_update_issue_form_fields');\n      if (daily_update_issue_fields == undefined) {\n        daily_update_issue_fields = initialValues === null || initialValues === void 0 ? void 0 : initialValues.daily_update_issue_form_fields;\n      }\n      var sp_daily_update_form_fields = (_this$formRef9 = this.formRef) === null || _this$formRef9 === void 0 ? void 0 : (_this$formRef9$curren = _this$formRef9.current) === null || _this$formRef9$curren === void 0 ? void 0 : _this$formRef9$curren.getFieldValue('sp_daily_update_form_fields');\n      if (sp_daily_update_form_fields == undefined) {\n        sp_daily_update_form_fields = initialValues === null || initialValues === void 0 ? void 0 : initialValues.sp_daily_update_form_fields;\n      }\n      const isTrackingAllLineItemWiseProgress = ((_this$formRef0 = this.formRef) === null || _this$formRef0 === void 0 ? void 0 : (_this$formRef0$curren = _this$formRef0.current) === null || _this$formRef0$curren === void 0 ? void 0 : _this$formRef0$curren.getFieldValue('daily_update_track_line_item_progress')) || ((_this$state$viewData33 = this.state.viewData) === null || _this$state$viewData33 === void 0 ? void 0 : (_this$state$viewData34 = _this$state$viewData33.form_data) === null || _this$state$viewData34 === void 0 ? void 0 : _this$state$viewData34.daily_update_track_line_item_progress);\n      const meta = {\n        columns: 1,\n        formItemLayout: null,\n        initialValues: initialValues,\n        fields: [{\n          render: () => /*#__PURE__*/React.createElement(\"h3\", {\n            className: \"gx-mb-2\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1658,\n              columnNumber: 25\n            }\n          }, \"Configure daily updates form\")\n        }, {\n          key: 'daily_update_who_can_edit',\n          label: 'Who can edit daily updates ?',\n          widget: 'select',\n          widgetProps: {\n            mode: 'multiple'\n          },\n          options: possible_roles\n        }, {\n          key: 'who_cannot_download_sp_daily_updates',\n          label: 'Who cannot download daily updates',\n          widget: 'select',\n          widgetProps: {\n            mode: 'multiple',\n            allowClear: true,\n            showSearch: true,\n            optionFilterProp: 'children'\n          },\n          options: possible_roles\n        }, {\n          label: 'Daily update form fields',\n          key: 'sp_daily_update_form_fields',\n          widget: 'textarea',\n          placeholder: 'Please paste the JSON here',\n          onChange: event => {\n            this.forceUpdate();\n          }\n        }, {\n          render: () => {\n            return /*#__PURE__*/React.createElement(\"div\", {\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1696,\n                columnNumber: 29\n              }\n            }, sp_daily_update_form_fields != '' && /*#__PURE__*/React.createElement(\"div\", {\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1698,\n                columnNumber: 37\n              }\n            }, /*#__PURE__*/React.createElement(Button, {\n              onClick: e => this.handleShowFieldsPreviewClick(sp_daily_update_form_fields),\n              icon: /*#__PURE__*/React.createElement(ZoomInOutlined, {\n                __self: this,\n                __source: {\n                  fileName: _jsxFileName,\n                  lineNumber: 1705,\n                  columnNumber: 51\n                }\n              }),\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1699,\n                columnNumber: 41\n              }\n            }, \"Form preview\")), /*#__PURE__*/React.createElement(Link, {\n              to: '/fields-creator?edit=' + encodeURIComponent(sp_daily_update_form_fields),\n              target: \"_blank\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1711,\n                columnNumber: 33\n              }\n            }, \"Open field creator \", '-->'));\n          }\n        }, {\n          render: () => /*#__PURE__*/React.createElement(\"hr\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1727,\n              columnNumber: 35\n            }\n          })\n        }, {\n          key: `daily_update_will_have_issues`,\n          label: 'Will have issues?',\n          widget: 'radio-group',\n          options: ['Yes', 'No']\n        }, {\n          label: 'Issue form fields',\n          key: 'daily_update_issue_form_fields',\n          widget: 'textarea',\n          placeholder: 'Please paste the JSON here',\n          onChange: event => {\n            this.forceUpdate();\n          }\n        }, {\n          render: () => {\n            return /*#__PURE__*/React.createElement(\"div\", {\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1747,\n                columnNumber: 29\n              }\n            }, daily_update_issue_fields != '' && /*#__PURE__*/React.createElement(\"div\", {\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1749,\n                columnNumber: 37\n              }\n            }, /*#__PURE__*/React.createElement(Button, {\n              onClick: e => this.handleShowFieldsPreviewClick(daily_update_issue_fields),\n              icon: /*#__PURE__*/React.createElement(ZoomInOutlined, {\n                __self: this,\n                __source: {\n                  fileName: _jsxFileName,\n                  lineNumber: 1756,\n                  columnNumber: 51\n                }\n              }),\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1750,\n                columnNumber: 41\n              }\n            }, \"Form preview\")), /*#__PURE__*/React.createElement(Link, {\n              to: '/fields-creator?edit=' + encodeURIComponent(daily_update_issue_fields),\n              target: \"_blank\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1762,\n                columnNumber: 33\n              }\n            }, \"Open field creator \", '-->'));\n          }\n        }, {\n          render: () => /*#__PURE__*/React.createElement(\"hr\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1778,\n              columnNumber: 35\n            }\n          })\n        }, {\n          key: 'daily_progress_update_mode',\n          label: 'Daily progress update mode',\n          widget: 'select',\n          options: DailyUpdateModes\n        }, {\n          key: 'daily_update_track_line_item_progress',\n          label: 'Track line item wise progress & assignment?',\n          tooltip: 'Updater will be able to update line item wise progress and cummulative progress will be calculated automatically',\n          widget: 'radio-group',\n          options: ['Yes', 'No'],\n          onChange: () => {\n            this.refresh();\n          }\n        }, ...(isTrackingAllLineItemWiseProgress === 'Yes' ? [{\n          key: 'show_line_item_by_selection',\n          widget: props => /*#__PURE__*/React.createElement(InlineCheckbox, {\n            label: \"Show line items by selection\",\n            tooltip: \"If enabled, line item progress will be shown only for the selected line items.\",\n            value: props.value,\n            onChange: e => props.onChange(e.target.checked),\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1802,\n              columnNumber: 35\n            }\n          })\n        }] : []), {\n          key: 'daily_update_dynamic_line_item_wise_files',\n          label: 'Enable line item wise photos/videos ?',\n          tooltip: 'The updater will be able to upload photo videos with respect to each line item (in addition to a general photo section)',\n          widget: 'radio-group',\n          options: ['Yes', 'No']\n        }]\n      };\n      return meta;\n    };\n    this.getSelectedSpAuthoritiesRoleList = () => {\n      var _this$formRef1, _this$formRef1$curren;\n      var seletcedRoleIds = (_this$formRef1 = this.formRef) === null || _this$formRef1 === void 0 ? void 0 : (_this$formRef1$curren = _this$formRef1.current) === null || _this$formRef1$curren === void 0 ? void 0 : _this$formRef1$curren.getFieldValue('authority_id');\n      let selectedRoleData = [];\n      if (seletcedRoleIds) {\n        seletcedRoleIds.forEach(singleRole => {\n          var _this$state$viewData35;\n          const roleData = (_this$state$viewData35 = this.state.viewData) === null || _this$state$viewData35 === void 0 ? void 0 : _this$state$viewData35.role_list.find(singleAuthority => singleAuthority.value === singleRole);\n          if (roleData) {\n            selectedRoleData.push({\n              key: roleData.value,\n              label: roleData.label,\n              value: roleData.value\n            });\n          }\n        });\n      }\n      return selectedRoleData;\n    };\n    this.getSpAuthoritiesMeta = () => {\n      var _this$state$viewData36, _this$state$viewData37, _this$formRef10, _this$formRef10$curre, _this$formRef11, _this$formRef11$curre;\n      const initialValues = this.state.editMode ? (_this$state$viewData36 = this.state.viewData) === null || _this$state$viewData36 === void 0 ? void 0 : _this$state$viewData36.form_data : {};\n      let roleWiseSpecificFields = [];\n      let enableLocGrpFilteration = [];\n      let selected_srvc_prvdr_roles_authorities = [];\n      let authorities_role_list = (_this$state$viewData37 = this.state.viewData) === null || _this$state$viewData37 === void 0 ? void 0 : _this$state$viewData37.role_list;\n      var seletcedRoleIds = (_this$formRef10 = this.formRef) === null || _this$formRef10 === void 0 ? void 0 : (_this$formRef10$curre = _this$formRef10.current) === null || _this$formRef10$curre === void 0 ? void 0 : _this$formRef10$curre.getFieldValue('authority_id');\n      var isFilterationEnable = (_this$formRef11 = this.formRef) === null || _this$formRef11 === void 0 ? void 0 : (_this$formRef11$curre = _this$formRef11.current) === null || _this$formRef11$curre === void 0 ? void 0 : _this$formRef11$curre.getFieldValue('enable_location_group_based_filteration');\n      if (seletcedRoleIds) {\n        seletcedRoleIds.forEach(singleRole => {\n          var _authorities_role_lis, _authorities_role_lis2;\n          let seletcedRoleTitle = authorities_role_list === null || authorities_role_list === void 0 ? void 0 : (_authorities_role_lis = authorities_role_list.filter(singleAuthority => singleAuthority.value == singleRole)) === null || _authorities_role_lis === void 0 ? void 0 : (_authorities_role_lis2 = _authorities_role_lis[0]) === null || _authorities_role_lis2 === void 0 ? void 0 : _authorities_role_lis2.label;\n          let dummyObj = {\n            label: seletcedRoleTitle,\n            key: singleRole + '_enable_cross_visibility_of_authorities',\n            widget: 'checkbox'\n          };\n          selected_srvc_prvdr_roles_authorities.push(dummyObj);\n          let roleWiseSpecificFieldsobj = {\n            label: seletcedRoleTitle + ' Specific Fields (only ' + seletcedRoleTitle + ' of the selected service request can edit these fields)',\n            key: 'srvc_authority_' + singleRole + '_specific_fields',\n            widget: 'select',\n            options: this.getVerticalOrSelectedSrvcTypeSpecificFields(true, [], true),\n            widgetProps: {\n              mode: 'multiple',\n              allowClear: true,\n              showSearch: true,\n              optionFilterProp: 'children'\n            }\n            // rules : [{\n            //     required: true,\n            //     message : 'Please select specific Fields'\n            // }],\n          };\n          roleWiseSpecificFields.push(roleWiseSpecificFieldsobj);\n        });\n        if ((seletcedRoleIds === null || seletcedRoleIds === void 0 ? void 0 : seletcedRoleIds.length) > 0) {\n          let obj = {\n            label: 'Enable location group based filteration',\n            key: 'enable_location_group_based_filteration',\n            widget: 'checkbox',\n            onChange: () => this.refresh()\n          };\n          enableLocGrpFilteration.push(obj);\n        }\n        if (isFilterationEnable && (seletcedRoleIds === null || seletcedRoleIds === void 0 ? void 0 : seletcedRoleIds.length) > 0) {\n          let data = {\n            label: 'Select authorities to be filtered',\n            key: 'select_authorities_to_be_filtered_fr_loc_grp',\n            widget: 'select',\n            widgetProps: {\n              mode: 'multiple',\n              allowClear: true,\n              optionFilterProp: 'children'\n            },\n            options: this.getSelectedSpAuthoritiesRoleList()\n          };\n          enableLocGrpFilteration.push(data);\n        }\n      }\n      let selected_roles_srvc_authorities_seperator = [];\n      if (selected_srvc_prvdr_roles_authorities.length > 0) {\n        let dummyObj = {\n          render: () => {\n            return /*#__PURE__*/React.createElement(Alert, {\n              message: \"Enable cross visibility of selected authorities\",\n              type: \"info\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1989,\n                columnNumber: 25\n              }\n            });\n          }\n        };\n        selected_roles_srvc_authorities_seperator.push(dummyObj);\n      }\n      const meta = {\n        columns: 1,\n        formItemLayout: null,\n        initialValues: initialValues,\n        fields: [{\n          key: 'authority_id',\n          label: 'Select roles',\n          widget: 'select',\n          widgetProps: {\n            mode: 'multiple',\n            allowClear: true,\n            optionFilterProp: 'children'\n          },\n          options: this.state.viewData.role_list,\n          placeholder: 'Please select role',\n          onChange: value => {\n            const currentSelectedRolesFrAutohrities = this.formRef.current.getFieldValue('select_authorities_to_be_filtered_fr_loc_grp') || [];\n            // Filter the current authorities to only include those that are still selected as auhtorities\n            const updatedSelectedAuthorities = currentSelectedRolesFrAutohrities.filter(auth => value.includes(auth));\n            //update the select_authorities_to_be_filtered_fr_loc_grp\n            this.formRef.current.setFieldsValue({\n              select_authorities_to_be_filtered_fr_loc_grp: updatedSelectedAuthorities\n            });\n            this.refresh();\n          }\n        }, ...selected_roles_srvc_authorities_seperator, ...selected_srvc_prvdr_roles_authorities, ...roleWiseSpecificFields, ...enableLocGrpFilteration]\n      };\n      return meta;\n    };\n    this.getParamsFrSpAuthorityRatings = () => {\n      return {\n        viewData: this.state.viewData,\n        formRef: this.formRef,\n        editMode: this.state.editMode,\n        message: 'Here, you can configure who will rate which authority.',\n        keySelectedAuthoritiesorDeployment: 'authority_id',\n        keyRoleList: 'role_list',\n        collapseKey: 'sp_rating_authority_',\n        tabName: 'authority',\n        ratingTypeKey: 'sp_rating_type_',\n        refresh: () => this.refresh(),\n        authorityIdKey: 'authority_id',\n        selectedRatingAuthority: 'sp_authority_',\n        staticUserKey: 'sp_static_user_',\n        templateKey: 'sp_rating_template_'\n      };\n    };\n    this.getParamsFrSpAssigneeRatings = () => {\n      return {\n        viewData: this.state.viewData,\n        formRef: this.formRef,\n        editMode: this.state.editMode,\n        message: this.isProjectNature() ? 'Here, you can configure who will rate On Field Users.' : 'Here, you can configure who will rate subtask assignees.',\n        keySelectedAuthoritiesorDeployment: 'deployment_possible_roles',\n        keyRoleList: 'onfield_role_list',\n        collapseKey: 'sp_rating_assignee_',\n        tabName: 'assignee',\n        ratingTypeKey: 'sp_rating_type_fr_deployment_',\n        refresh: () => this.refresh(),\n        authorityIdKey: 'authority_id',\n        selectedRatingAuthority: 'sp_authority_fr_deployment_',\n        selectedRatingDynamicUserRole: 'sp_dynamic_user_role_fr_deployment_',\n        staticUserKey: 'sp_static_user_fr_deployment_',\n        templateKey: 'sp_rating_template_fr_deployment',\n        isProjectBased: this.isProjectNature(),\n        selectedRolesToBeRatedKey: 'selected_roles_to_be_rated',\n        modeOfRatingFrAssigneesKey: 'mode_of_rating_fr_assignees',\n        HasMatchingCustomerAccessKey: 'has_matching_customer_access_fr_assignee_',\n        HasMatchingLocationGroupKey: 'has_matching_location_group_fr_assignee_'\n      };\n    };\n    this.getAutomationMeta = () => {\n      var _this$formRef12, _this$formRef12$curre;\n      let _allSelectedSubTasks = [];\n      if (this.formRef && ((_this$formRef12 = this.formRef) === null || _this$formRef12 === void 0 ? void 0 : (_this$formRef12$curre = _this$formRef12.current) === null || _this$formRef12$curre === void 0 ? void 0 : _this$formRef12$curre.getFieldsValue('select_subtask_fr_readiness'))) {\n        _allSelectedSubTasks = this.getDynamicSbtskFields();\n      }\n      return {\n        fields: [{\n          key: `automation_tabs_readiness`,\n          render: () => {\n            var _this$state$viewData38;\n            return /*#__PURE__*/React.createElement(Tabs, {\n              defaultActiveKey: \"readiness\",\n              className: \"gx-mb-2\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 2477,\n                columnNumber: 29\n              }\n            }, /*#__PURE__*/React.createElement(Tabs.TabPane, {\n              tab: \"Readiness\",\n              key: \"readiness\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 2481,\n                columnNumber: 33\n              }\n            }, /*#__PURE__*/React.createElement(FormBuilder, {\n              form: this.formRef,\n              meta: {\n                formItemLayout: null,\n                fields: [{\n                  label: 'Select subtasks',\n                  key: 'select_subtask_fr_readiness',\n                  widget: 'select',\n                  options: [...((_this$state$viewData38 = this.state.viewData) === null || _this$state$viewData38 === void 0 ? void 0 : _this$state$viewData38.applicable_subtasks_list)],\n                  widgetProps: {\n                    mode: 'multiple',\n                    allowClear: true,\n                    showSearch: true,\n                    optionFilterProp: 'children',\n                    onChange: () => this.refresh()\n                  }\n                }, ..._allSelectedSubTasks]\n              },\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 2482,\n                columnNumber: 37\n              }\n            })));\n          }\n        }]\n      };\n    };\n    this.formRef = React.createRef();\n  }\n  componentDidMount() {\n    this.initViewData();\n  }\n  initViewData() {\n    if (this.state.editMode && this.state.visible || !this.state.editMode && this.state.viewData == undefined && !this.state.isLoadingViewData) {\n      this.setState({\n        isLoadingViewData: true\n      });\n      var params = {};\n      const onComplete = resp => {\n        this.setState({\n          isLoadingViewData: false,\n          viewData: resp.data,\n          error: ''\n        });\n      };\n      const onError = error => {\n        // console.log(error.response.status);\n        this.setState({\n          isLoadingViewData: false,\n          error: http_utils.decodeErrorToMessage(error)\n        });\n      };\n      var url = !this.state.editMode ? protoUrl : protoUrl + '/' + this.props.editorItem.id;\n      // console.log(url);\n      http_utils.performGetCall(url, params, onComplete, onError);\n    }\n  }\n  componentDidUpdate(prevProps, prevState) {\n    if (prevProps.editorItem != this.props.editorItem || prevProps.showEditor != this.props.showEditor) {\n      this.setState({\n        render_helper: !this.state.render_helper,\n        visible: this.props.showEditor\n      }, function () {\n        if (this.props.showEditor && this.state.editMode) {\n          this.initViewData();\n        }\n      });\n    } else {\n      if (this.state.refreshOnUpdate) {\n        this.setState({\n          refreshOnUpdate: false\n        }, this.initViewData());\n      }\n    }\n  }\n  updateClosureToParent() {\n    if (this.props.onClose != undefined) {\n      this.props.onClose();\n    }\n    this.setState({\n      refreshOnUpdate: true,\n      ...this.initState\n    });\n  }\n  tellParentToRefreshList(entry_id) {\n    if (this.props.onDataModified != undefined) {\n      this.props.onDataModified(entry_id);\n    }\n  }\n  handleRowDataChange(newRowData) {\n    this.setState({\n      revenuecol: newRowData\n    });\n  }\n  getLineItemConfig() {\n    var _this$formRef13, _this$formRef13$curre, _this$state$viewData39, _this$state$viewData40, _this$state$viewData41;\n    if ((_this$formRef13 = this.formRef) === null || _this$formRef13 === void 0 ? void 0 : (_this$formRef13$curre = _this$formRef13.current) === null || _this$formRef13$curre === void 0 ? void 0 : _this$formRef13$curre.getFieldValue) {\n      const fieldJson = this.formRef.current.getFieldValue('srvc_type_line_item_config');\n      if (fieldJson) {\n        return JSON.parse(fieldJson);\n      }\n    }\n    if ((_this$state$viewData39 = this.state.viewData) === null || _this$state$viewData39 === void 0 ? void 0 : (_this$state$viewData40 = _this$state$viewData39.form_data) === null || _this$state$viewData40 === void 0 ? void 0 : (_this$state$viewData41 = _this$state$viewData40.form_data) === null || _this$state$viewData41 === void 0 ? void 0 : _this$state$viewData41.srvc_type_line_item_config) {\n      var _this$state$viewData42, _this$state$viewData43, _this$state$viewData44;\n      return JSON.parse((_this$state$viewData42 = this.state.viewData) === null || _this$state$viewData42 === void 0 ? void 0 : (_this$state$viewData43 = _this$state$viewData42.form_data) === null || _this$state$viewData43 === void 0 ? void 0 : (_this$state$viewData44 = _this$state$viewData43.form_data) === null || _this$state$viewData44 === void 0 ? void 0 : _this$state$viewData44.srvc_type_line_item_config);\n    }\n    return {};\n  }\n  getConfigFrSpPayouts() {\n    var _this$formRef14, _this$formRef14$curre, _this$state$viewData45, _this$state$viewData46, _this$state$viewData47;\n    //here\n    if ((_this$formRef14 = this.formRef) === null || _this$formRef14 === void 0 ? void 0 : (_this$formRef14$curre = _this$formRef14.current) === null || _this$formRef14$curre === void 0 ? void 0 : _this$formRef14$curre.getFieldValue) {\n      let fieldJson = this.formRef.current.getFieldValue('srvc_type_sp_payouts_config');\n      //Set sp payouts initial values\n      if (fieldJson == undefined) {\n        fieldJson = '{\"1236c271-876d-4352-a044-157acbeee076\":{\"key\":\"1236c271-876d-4352-a044-157acbeee076\",\"label\":\"Payouts\"}}';\n      }\n      if (fieldJson) {\n        return JSON.parse(fieldJson);\n      }\n    }\n    if ((_this$state$viewData45 = this.state.viewData) === null || _this$state$viewData45 === void 0 ? void 0 : (_this$state$viewData46 = _this$state$viewData45.form_data) === null || _this$state$viewData46 === void 0 ? void 0 : (_this$state$viewData47 = _this$state$viewData46.form_data) === null || _this$state$viewData47 === void 0 ? void 0 : _this$state$viewData47.srvc_type_sp_payouts_config) {\n      var _this$state$viewData48, _this$state$viewData49, _this$state$viewData50;\n      return JSON.parse((_this$state$viewData48 = this.state.viewData) === null || _this$state$viewData48 === void 0 ? void 0 : (_this$state$viewData49 = _this$state$viewData48.form_data) === null || _this$state$viewData49 === void 0 ? void 0 : (_this$state$viewData50 = _this$state$viewData49.form_data) === null || _this$state$viewData50 === void 0 ? void 0 : _this$state$viewData50.srvc_type_sp_payouts_config);\n    }\n    return {};\n  }\n  getConfigFrDeduction() {\n    var _this$formRef15, _this$formRef15$curre, _this$state$viewData51, _this$state$viewData52, _this$state$viewData53;\n    let fieldJson = (_this$formRef15 = this.formRef) === null || _this$formRef15 === void 0 ? void 0 : (_this$formRef15$curre = _this$formRef15.current) === null || _this$formRef15$curre === void 0 ? void 0 : _this$formRef15$curre.getFieldValue('srvc_type_deduction_config');\n    if (!fieldJson) {\n      fieldJson = '{\"deduction_item\":{\"key\":\"deduction_item\",\"label\":\"Deduction\"}}';\n    }\n    if (fieldJson) {\n      return JSON.parse(fieldJson);\n    }\n    if ((_this$state$viewData51 = this.state.viewData) === null || _this$state$viewData51 === void 0 ? void 0 : (_this$state$viewData52 = _this$state$viewData51.form_data) === null || _this$state$viewData52 === void 0 ? void 0 : (_this$state$viewData53 = _this$state$viewData52.form_data) === null || _this$state$viewData53 === void 0 ? void 0 : _this$state$viewData53.srvc_type_deduction_config) {\n      var _this$state$viewData54, _this$state$viewData55, _this$state$viewData56;\n      return JSON.parse((_this$state$viewData54 = this.state.viewData) === null || _this$state$viewData54 === void 0 ? void 0 : (_this$state$viewData55 = _this$state$viewData54.form_data) === null || _this$state$viewData55 === void 0 ? void 0 : (_this$state$viewData56 = _this$state$viewData55.form_data) === null || _this$state$viewData56 === void 0 ? void 0 : _this$state$viewData56.srvc_type_deduction_config);\n    }\n    return {};\n  }\n  getMetaFrLineItemConfiguration() {\n    const lineItemConfig = this.getLineItemConfig();\n    let who_cannot_download_sp_line_item = [];\n    let who_cannot_view_line_items_tab = [];\n    if (Object.keys(lineItemConfig).length > 0) {\n      var _this$state$viewData57, _this$state$viewData58;\n      who_cannot_download_sp_line_item.push({\n        key: 'who_cannot_download_sp_line_item',\n        label: /*#__PURE__*/React.createElement(\"span\", {\n          style: {\n            marginTop: 20\n          },\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 21\n          }\n        }, ' ', \"Who cannot download SP line items\", ' '),\n        widget: 'select',\n        widgetProps: {\n          mode: 'multiple',\n          allowClear: true,\n          showSearch: true,\n          optionFilterProp: 'children'\n        },\n        options: ((_this$state$viewData57 = this.state.viewData) === null || _this$state$viewData57 === void 0 ? void 0 : _this$state$viewData57.role_list) || []\n      });\n      who_cannot_view_line_items_tab.push({\n        key: 'who_cannot_view_line_items_tab',\n        label: /*#__PURE__*/React.createElement(\"span\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 24\n          }\n        }, \" Who cannot view line items tab\"),\n        formItemLayout: null,\n        widget: 'select',\n        widgetProps: {\n          mode: 'multiple',\n          allowClear: true,\n          showSearch: true,\n          optionFilterProp: 'children'\n        },\n        options: ((_this$state$viewData58 = this.state.viewData) === null || _this$state$viewData58 === void 0 ? void 0 : _this$state$viewData58.role_list) || []\n      });\n    }\n    const meta = {\n      columns: 1,\n      formItemLayout: null,\n      fields: [{\n        label: 'Service type line item config json',\n        key: 'srvc_type_line_item_config',\n        formItemProps: {\n          style: {\n            display: 'none'\n          }\n        }\n      }, {\n        key: 'Line items',\n        render: () => /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(LineItemManagementModule, {\n          form: this.formRef,\n          onChange: newObj => {\n            this.formRef.current.setFieldsValue({\n              srvc_type_line_item_config: JSON.stringify(newObj)\n            });\n            this.refresh();\n          },\n          currValue: lineItemConfig,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 29\n          }\n        }))\n      }, ...who_cannot_download_sp_line_item, ...who_cannot_view_line_items_tab, {\n        key: 'seperator_6',\n        render: () => /*#__PURE__*/React.createElement(\"div\", {\n          className: \"gx-mb-3 gx-bg-grey\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 25\n          }\n        }, /*#__PURE__*/React.createElement(\"hr\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 29\n          }\n        }))\n      }]\n    };\n    return meta;\n  }\n  getInitialValues() {\n    var _this$state$viewData59, _this$state$viewData60;\n    return this.state.editMode ? (_this$state$viewData59 = this.state.viewData) === null || _this$state$viewData59 === void 0 ? void 0 : (_this$state$viewData60 = _this$state$viewData59.form_data) === null || _this$state$viewData60 === void 0 ? void 0 : _this$state$viewData60.form_data : {};\n  }\n  getOrgListBasedOnNature() {\n    var _this$formRef16, _this$formRef16$curre;\n    let vertical_nature = (_this$formRef16 = this.formRef) === null || _this$formRef16 === void 0 ? void 0 : (_this$formRef16$curre = _this$formRef16.current) === null || _this$formRef16$curre === void 0 ? void 0 : _this$formRef16$curre.getFieldValue('vertical_nature');\n    let org_list = [...this.state.viewData.org_list];\n    let filtered_org_list = [];\n    filtered_org_list = org_list.filter(singleOrgSrvcTypeObj => {\n      return singleOrgSrvcTypeObj.srvc_type_nature == vertical_nature;\n    });\n    return filtered_org_list;\n  }\n  getMetaFrSearchableCustomFields() {\n    let prefix = 'searchable_sp_fields';\n    return [{\n      key: `${prefix}_form_data`,\n      label: 'Select searchable SP fields',\n      formItemProps: {\n        style: {\n          display: 'none'\n        }\n      }\n    }, {\n      key: `${prefix}`,\n      label: 'Select searchable SP fields',\n      widget: 'select',\n      widgetProps: {\n        mode: 'multiple'\n      },\n      options: this.getSearchableCustomFields(),\n      onChange: (selectedField, selectedOption) => {\n        let filterConfigValue = [];\n        selectedOption.map(singleElem => {\n          filterConfigValue.push({\n            search_keyword: singleElem.label,\n            key: singleElem.key\n          });\n        });\n        this.formRef.current.setFieldsValue({\n          searchable_sp_fields_form_data: filterConfigValue\n        });\n      }\n    }];\n  }\n  getPricingConfig() {\n    if (this.state.editMode) {\n      var _this$formRef17, _this$formRef17$curre, _this$state$viewData61, _this$state$viewData62, _this$state$viewData63;\n      if ((_this$formRef17 = this.formRef) === null || _this$formRef17 === void 0 ? void 0 : (_this$formRef17$curre = _this$formRef17.current) === null || _this$formRef17$curre === void 0 ? void 0 : _this$formRef17$curre.getFieldValue) {\n        const fieldJson = this.formRef.current.getFieldValue('srvc_type_pricing_config_for_line_item');\n        if (fieldJson) {\n          return JSON.parse(fieldJson);\n        }\n      }\n      const config_in_form_data = (_this$state$viewData61 = this.state.viewData) === null || _this$state$viewData61 === void 0 ? void 0 : (_this$state$viewData62 = _this$state$viewData61.form_data) === null || _this$state$viewData62 === void 0 ? void 0 : (_this$state$viewData63 = _this$state$viewData62.form_data) === null || _this$state$viewData63 === void 0 ? void 0 : _this$state$viewData63.srvc_type_pricing_config_for_line_item;\n      return config_in_form_data ? JSON.parse(config_in_form_data) : {};\n    }\n    return {};\n  }\n  onSaveOfPricingConfig(data, singleLineItemGrpKey) {\n    let pricingConfigData = this.getPricingConfig();\n    pricingConfigData[singleLineItemGrpKey] = data;\n    this.formRef.current.setFieldsValue({\n      srvc_type_pricing_config_for_line_item: JSON.stringify(pricingConfigData)\n    });\n  }\n  getPricingConfigFrManday() {\n    if (this.state.editMode) {\n      var _this$formRef18, _this$formRef18$curre, _this$state$viewData64, _this$state$viewData65, _this$state$viewData66;\n      if ((_this$formRef18 = this.formRef) === null || _this$formRef18 === void 0 ? void 0 : (_this$formRef18$curre = _this$formRef18.current) === null || _this$formRef18$curre === void 0 ? void 0 : _this$formRef18$curre.getFieldValue) {\n        const fieldJson = this.formRef.current.getFieldValue('srvc_type_pricing_config_for_manday');\n        if (fieldJson) {\n          return JSON.parse(fieldJson);\n        }\n      }\n      const config_in_form_data = (_this$state$viewData64 = this.state.viewData) === null || _this$state$viewData64 === void 0 ? void 0 : (_this$state$viewData65 = _this$state$viewData64.form_data) === null || _this$state$viewData65 === void 0 ? void 0 : (_this$state$viewData66 = _this$state$viewData65.form_data) === null || _this$state$viewData66 === void 0 ? void 0 : _this$state$viewData66.srvc_type_pricing_config_for_manday;\n      return config_in_form_data ? JSON.parse(config_in_form_data) : {};\n    }\n    return {};\n  }\n  onSaveOfPricingConfigFrManday(data) {\n    let pricingConfigDataFrManday = this.getPricingConfigFrManday();\n    pricingConfigDataFrManday['srvc_type_pricing_config_for_manday'] = data;\n    this.formRef.current.setFieldsValue({\n      srvc_type_pricing_config_for_manday: JSON.stringify(pricingConfigDataFrManday)\n    });\n  }\n  getSelectedOrgWithSrvcTypeIds() {\n    var _this$formRef19, _this$formRef19$curre, _this$getInitialValue;\n    let selectedOrgs = [];\n    let srvcTypeIds = ((_this$formRef19 = this.formRef) === null || _this$formRef19 === void 0 ? void 0 : (_this$formRef19$curre = _this$formRef19.current) === null || _this$formRef19$curre === void 0 ? void 0 : _this$formRef19$curre.getFieldValue('srvc_type_id')) || ((_this$getInitialValue = this.getInitialValues()) === null || _this$getInitialValue === void 0 ? void 0 : _this$getInitialValue.srvc_type_id);\n    let org_list = this.state.viewData.org_list;\n    if (Array.isArray(srvcTypeIds) && srvcTypeIds.length > 0) {\n      srvcTypeIds.forEach(singlesrvcTypeId => {\n        var _org_list$filter;\n        let selectedOrgsList = (_org_list$filter = org_list.filter(singleOrg => singleOrg.value == singlesrvcTypeId)) === null || _org_list$filter === void 0 ? void 0 : _org_list$filter[0];\n        selectedOrgs.push(selectedOrgsList);\n      });\n    }\n    return selectedOrgs;\n  }\n  getPricingMasterConfigMeta(initialValues) {\n    let lineItemConfigData = this.getLineItemConfig();\n    let pricingConfigData = this.getPricingConfig();\n    let pricingConfigDataFrManday = this.getPricingConfigFrManday();\n    return {\n      fields: [{\n        label: 'Service provider line item pricing config json',\n        key: 'srvc_type_pricing_config_for_line_item',\n        formItemProps: {\n          style: {\n            display: 'none'\n          }\n        }\n      }, {\n        label: 'Service type line item pricing config json for manday',\n        key: 'srvc_type_pricing_config_for_manday',\n        formItemProps: {\n          style: {\n            display: 'none'\n          }\n        }\n      }, {\n        key: `sp_srvc_type_pricing_master_config`,\n        render: () => {\n          var _this$state$viewData67;\n          return /*#__PURE__*/React.createElement(Tabs, {\n            defaultActiveKey: \"line_item\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 795,\n              columnNumber: 29\n            }\n          }, Object.keys(lineItemConfigData).length > 0 && /*#__PURE__*/React.createElement(Tabs.TabPane, {\n            tab: \"Line Items\",\n            key: \"line_item\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 797,\n              columnNumber: 37\n            }\n          }, /*#__PURE__*/React.createElement(Tabs, {\n            defaultActiveKey: \"line_item\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 801,\n              columnNumber: 41\n            }\n          }, Object.keys(lineItemConfigData).map((singleLineItemGrpKey, index) => {\n            var _lineItemConfigData$s;\n            return /*#__PURE__*/React.createElement(Tabs.TabPane, {\n              key: index,\n              tab: lineItemConfigData === null || lineItemConfigData === void 0 ? void 0 : (_lineItemConfigData$s = lineItemConfigData[singleLineItemGrpKey]) === null || _lineItemConfigData$s === void 0 ? void 0 : _lineItemConfigData$s.label,\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 809,\n                columnNumber: 53\n              }\n            }, /*#__PURE__*/React.createElement(PricingMaster, {\n              pricingInitialValue: pricingConfigData[singleLineItemGrpKey],\n              fullFormInitialValue: initialValues,\n              form: this.formRef,\n              config_data: lineItemConfigData[singleLineItemGrpKey],\n              onChange: data => {\n                // WE GET pricing for single line item group here\n                this.onSaveOfPricingConfig(data, singleLineItemGrpKey);\n              },\n              editMode: this.state.editMode,\n              org_list: this.getSelectedOrgWithSrvcTypeIds(),\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 817,\n                columnNumber: 57\n              }\n            }));\n          }))), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n            tab: \"Manday\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 853,\n              columnNumber: 33\n            }\n          }, /*#__PURE__*/React.createElement(PricingMaster, {\n            isManday: true,\n            pricingInitialValue: pricingConfigDataFrManday === null || pricingConfigDataFrManday === void 0 ? void 0 : pricingConfigDataFrManday.srvc_type_pricing_config_for_manday,\n            fullFormInitialValue: initialValues,\n            form: this.formRef,\n            config_data: (_this$state$viewData67 = this.state.viewData) === null || _this$state$viewData67 === void 0 ? void 0 : _this$state$viewData67.location_grp_list,\n            onChange: data => {\n              // WE GET pricing for single line item group here\n              this.onSaveOfPricingConfigFrManday(data);\n            },\n            editMode: this.state.editMode,\n            org_list: this.getSelectedOrgWithSrvcTypeIds(),\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 854,\n              columnNumber: 37\n            }\n          })));\n        }\n      }]\n    };\n  }\n  getPossibleSpecificFields(essentialFields = []) {\n    var _this$formRef20, _this$formRef20$curre;\n    let labelValuePairData = [];\n    let customFields = (_this$formRef20 = this.formRef) === null || _this$formRef20 === void 0 ? void 0 : (_this$formRef20$curre = _this$formRef20.current) === null || _this$formRef20$curre === void 0 ? void 0 : _this$formRef20$curre.getFieldValue('sp_cust_fields_json');\n    if (customFields && customFields != '') {\n      customFields = decodeFieldsMetaFrmJson(customFields, undefined, true);\n      customFields.forEach(singleField => {\n        if (essentialFields.length == 0 || this.checkIfPresentInEssentialField(singleField, essentialFields)) {\n          labelValuePairData.push({\n            label: singleField.label,\n            value: singleField.key,\n            key: singleField.key\n          });\n        }\n      });\n    }\n    return labelValuePairData;\n  }\n  getSearchableCustomFields() {\n    var _this$formRef21, _this$formRef21$curre;\n    let labelValuePairData = [];\n    let customFields = (_this$formRef21 = this.formRef) === null || _this$formRef21 === void 0 ? void 0 : (_this$formRef21$curre = _this$formRef21.current) === null || _this$formRef21$curre === void 0 ? void 0 : _this$formRef21$curre.getFieldValue('sp_cust_fields_json');\n    if (customFields && customFields != '') {\n      customFields = decodeFieldsMetaFrmJson(customFields);\n      customFields.forEach(singleField => {\n        if ((singleField === null || singleField === void 0 ? void 0 : singleField.cust_component) != 'linebreak' && (singleField === null || singleField === void 0 ? void 0 : singleField.type) != 'Barcode_scanner' && (singleField === null || singleField === void 0 ? void 0 : singleField.cust_component) != 'legend') {\n          if ((singleField === null || singleField === void 0 ? void 0 : singleField.widget) != 'select' && (singleField === null || singleField === void 0 ? void 0 : singleField.widget) != 'radio-group' && (singleField === null || singleField === void 0 ? void 0 : singleField.widget) != 'checkbox-group' && (singleField === null || singleField === void 0 ? void 0 : singleField.cust_widget) != 'Rating' && singleField.widget != 'date-picker') {\n            labelValuePairData.push({\n              value: singleField.key,\n              label: singleField.label,\n              require: singleField === null || singleField === void 0 ? void 0 : singleField.required\n            });\n          }\n        }\n      });\n    }\n    return labelValuePairData;\n  }\n  getPossibleSpecificFilesFields() {\n    var _this$formRef22, _this$formRef22$curre;\n    let customFileFields = (_this$formRef22 = this.formRef) === null || _this$formRef22 === void 0 ? void 0 : (_this$formRef22$curre = _this$formRef22.current) === null || _this$formRef22$curre === void 0 ? void 0 : _this$formRef22$curre.getFieldValue('sp_cust_fields_json');\n    if (customFileFields && customFileFields != '') {\n      customFileFields = decodeFileSectionsFrmJson(customFileFields);\n    }\n    return customFileFields;\n  }\n  getBillingDiscountConfig() {\n    if (this.state.editMode) {\n      var _this$formRef23, _this$formRef23$curre, _this$state$viewData68, _this$state$viewData69;\n      if ((_this$formRef23 = this.formRef) === null || _this$formRef23 === void 0 ? void 0 : (_this$formRef23$curre = _this$formRef23.current) === null || _this$formRef23$curre === void 0 ? void 0 : _this$formRef23$curre.getFieldValue) {\n        const fieldJson = this.formRef.current.getFieldValue('srvc_type_billing_discounting_rule_config');\n        if (fieldJson) {\n          return JSON.parse(fieldJson);\n        }\n      }\n      let initialFieldJson = (_this$state$viewData68 = this.state.viewData) === null || _this$state$viewData68 === void 0 ? void 0 : (_this$state$viewData69 = _this$state$viewData68.form_data) === null || _this$state$viewData69 === void 0 ? void 0 : _this$state$viewData69.srvc_type_billing_discounting_rule_config;\n      if (initialFieldJson) {\n        return JSON.parse(initialFieldJson);\n      }\n    }\n    return {};\n  }\n  getAdditionalBillingJson() {\n    if (this.state.editMode) {\n      var _this$formRef24, _this$formRef24$curre;\n      if ((_this$formRef24 = this.formRef) === null || _this$formRef24 === void 0 ? void 0 : (_this$formRef24$curre = _this$formRef24.current) === null || _this$formRef24$curre === void 0 ? void 0 : _this$formRef24$curre.getFieldValue) {\n        const fieldJson = this.formRef.current.getFieldValue('srvc_type_additional_billing_config');\n        if (fieldJson) {\n          return JSON.parse(fieldJson);\n        }\n      }\n      return {\n        additional_line_item: {\n          key: 'additional_line_item',\n          label: 'Additional line items'\n        }\n      };\n    }\n    return {};\n  }\n  getDeductionConfigMeta() {\n    var _this$formRef25, _this$formRef25$curre;\n    const isDeductionEnable = (_this$formRef25 = this.formRef) === null || _this$formRef25 === void 0 ? void 0 : (_this$formRef25$curre = _this$formRef25.current) === null || _this$formRef25$curre === void 0 ? void 0 : _this$formRef25$curre.getFieldValue('is_enable_deduction');\n    const deductionConfig = this.getConfigFrDeduction();\n    return {\n      formItemLayout: null,\n      fields: [{\n        key: 'is_enable_deduction',\n        label: 'Enable Deduction',\n        widget: 'checkbox',\n        onChange: e => {\n          this.refresh();\n          if (e.target.checked == true && this.formRef.current.getFieldValue('srvc_type_deduction_config') == undefined) {\n            this.formRef.current.setFieldsValue({\n              srvc_type_deduction_config: JSON.stringify({\n                deduction_item: {\n                  key: 'deduction_item',\n                  label: 'Deduction',\n                  total_field_label: ''\n                }\n              })\n            });\n          }\n        }\n      }, ...(isDeductionEnable ? [{\n        label: 'Service type deduction config json',\n        key: 'srvc_type_deduction_config',\n        formItemProps: {\n          style: {\n            display: 'none'\n          }\n        }\n      }, {\n        key: 'Deduction',\n        render: () => /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(LineItemManagementModule, {\n          form: this.formRef,\n          onChange: newObj => {\n            this.formRef.current.setFieldsValue({\n              srvc_type_deduction_config: JSON.stringify(newObj)\n            });\n            this.refresh();\n          },\n          currValue: deductionConfig,\n          isDeduction: true,\n          hideBtn: true,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1230,\n            columnNumber: 39\n          }\n        }))\n      }] : [])]\n    };\n  }\n  getFieldsWiseNotificationConfigMeta(initialValues) {\n    return {\n      fields: [{\n        key: `sp_srvc_type_fields_wise_notification_config`,\n        render: () => {\n          return /*#__PURE__*/React.createElement(Tabs, {\n            defaultActiveKey: \"field_wise\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1263,\n              columnNumber: 29\n            }\n          }, /*#__PURE__*/React.createElement(Tabs.TabPane, {\n            tab: \"Field Wise\",\n            key: \"field_wise\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1264,\n              columnNumber: 33\n            }\n          }, /*#__PURE__*/React.createElement(FormBuilder, {\n            form: this.formRef,\n            meta: this.getFieldWiseAuthorityNotification(initialValues),\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1265,\n              columnNumber: 37\n            }\n          })));\n        }\n      }]\n    };\n  }\n  getFieldWiseAuthorityNotification(initialValues) {\n    return {\n      fields: [{\n        key: `sp_srvc_type_field_wise_authority_notification_config`,\n        render: () => {\n          return /*#__PURE__*/React.createElement(Tabs, {\n            defaultActiveKey: \"authority\",\n            className: \"gx-mb-2\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1287,\n              columnNumber: 29\n            }\n          }, /*#__PURE__*/React.createElement(Tabs.TabPane, {\n            tab: \"Authority\",\n            key: \"authority\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1291,\n              columnNumber: 33\n            }\n          }, /*#__PURE__*/React.createElement(FormBuilder, {\n            form: this.formRef,\n            meta: this.getSpecificFieldsWiseNotificationFieldsMeta(initialValues),\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1292,\n              columnNumber: 37\n            }\n          })));\n        }\n      }]\n    };\n  }\n  getSpecificFieldsNotificationJson() {\n    if (this.state.editMode) {\n      var _this$formRef26, _this$formRef26$curre, _this$state$viewData70, _this$state$viewData71;\n      if ((_this$formRef26 = this.formRef) === null || _this$formRef26 === void 0 ? void 0 : (_this$formRef26$curre = _this$formRef26.current) === null || _this$formRef26$curre === void 0 ? void 0 : _this$formRef26$curre.getFieldValue) {\n        const fieldJson = this.formRef.current.getFieldValue('srvc_type_specific_fields_wise_notification');\n        if (fieldJson) {\n          return JSON.parse(fieldJson);\n        }\n      }\n      let initialFieldJson = (_this$state$viewData70 = this.state.viewData) === null || _this$state$viewData70 === void 0 ? void 0 : (_this$state$viewData71 = _this$state$viewData70.form_data) === null || _this$state$viewData71 === void 0 ? void 0 : _this$state$viewData71.srvc_type_specific_fields_wise_notification;\n      if (initialFieldJson) {\n        return JSON.parse(initialFieldJson);\n      }\n    }\n    return {};\n  }\n  getBillingMasterConfigMeta(initialValues) {\n    return {\n      fields: [{\n        //All data of Billing section will be saved under this key and this is to be done later\n        label: 'Service type billing config json for billing',\n        key: 'srvc_type_billing_config_for_billing',\n        formItemProps: {\n          style: {\n            display: 'none'\n          }\n        }\n      }, {\n        label: 'Service type discounting config json for billing',\n        key: 'srvc_type_billing_discounting_rule_config',\n        formItemProps: {\n          style: {\n            display: 'none'\n          }\n        }\n      }, {\n        label: 'Service type additional config json for billing',\n        key: 'srvc_type_additional_billing_config',\n        formItemProps: {\n          style: {\n            display: 'none'\n          }\n        }\n      }, {\n        key: `srvc_type_billing_master_config`,\n        render: () => {\n          return /*#__PURE__*/React.createElement(Tabs, {\n            defaultActiveKey: \"billing\",\n            className: \"gx-mb-2\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1428,\n              columnNumber: 29\n            }\n          }, /*#__PURE__*/React.createElement(Tabs.TabPane, {\n            tab: \"Billing\",\n            key: \"billing\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1432,\n              columnNumber: 33\n            }\n          }, /*#__PURE__*/React.createElement(FormBuilder, {\n            form: this.formRef,\n            meta: this.getBillingformMeta(initialValues),\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1434,\n              columnNumber: 37\n            }\n          })), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n            tab: \"Discounting\",\n            key: \"discounting\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1442,\n              columnNumber: 33\n            }\n          }, /*#__PURE__*/React.createElement(FormBuilder, {\n            form: this.formRef,\n            meta: this.getDiscountingBillingformMeta(initialValues),\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1446,\n              columnNumber: 37\n            }\n          })), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n            tab: \"Additional\",\n            key: \"additional\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1454,\n              columnNumber: 33\n            }\n          }, /*#__PURE__*/React.createElement(FormBuilder, {\n            form: this.formRef,\n            meta: this.getAdditionalBillingformMeta(initialValues),\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1455,\n              columnNumber: 37\n            }\n          })), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n            tab: \"Deduction\",\n            key: \"deduction_tab\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1462,\n              columnNumber: 33\n            }\n          }, /*#__PURE__*/React.createElement(FormBuilder, {\n            form: this.formRef,\n            meta: this.getDeductionConfigMeta(initialValues),\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1466,\n              columnNumber: 37\n            }\n          })));\n        }\n      }]\n    };\n  }\n  refresh() {\n    this.setState({\n      render_helper: !this.state.render_helper\n    });\n  }\n  handleShowFieldsPreviewClick(customFields) {\n    this.setState({\n      showFormPreview: true,\n      formPreviewMeta: customFields\n    });\n  }\n  getTabularViewFieldsConfigMeta() {\n    return {\n      formItemLayout: null,\n      fields: [{\n        label: 'Select table columns',\n        key: 'srvc_type_tabular_view_columns',\n        widget: 'select',\n        options: getSrvcReqStaticAndCustomPossibleFields(this.getPossibleSpecificFields()),\n        widgetProps: {\n          mode: 'multiple',\n          allowClear: true,\n          showSearch: true,\n          optionFilterProp: 'children'\n        }\n      }]\n    };\n  }\n  getViewFieldsConfigMeta() {\n    return {\n      formItemLayout: null,\n      fields: [{\n        label: 'Select customer request filters',\n        key: 'srvc_type_view_columns',\n        widget: 'select',\n        options: getSrvcReqStaticAndCustomPossibleFieldsFilter(this.getPossibleSpecificFields(), this.getAuthoritiesList()),\n        widgetProps: {\n          mode: 'multiple',\n          allowClear: true,\n          showSearch: true,\n          optionFilterProp: 'children'\n        }\n      }]\n    };\n  }\n  getVerticalOrSelectedSrvcTypeSpecificFields(Files = false, selectedCustomFields = [], onlyAttachments = false) {\n    var _this$formRef27, _this$formRef27$curre, _this$formRef28, _this$formRef28$curre;\n    //Make a dynamic authorities role_access base on selected role\n    let brandSrvcTypeDetails = this.state.viewData.brand_wise_specific_fields;\n    let verticalOrSelectedSrvcTypeSpecificFields = [];\n    let seletcedSrvcType = (_this$formRef27 = this.formRef) === null || _this$formRef27 === void 0 ? void 0 : (_this$formRef27$curre = _this$formRef27.current) === null || _this$formRef27$curre === void 0 ? void 0 : _this$formRef27$curre.getFieldValue('srvc_type_id');\n    if (seletcedSrvcType) {\n      seletcedSrvcType.forEach(singleSrvcType => {\n        var _brandSrvcTypeDetails;\n        let singleBrandSrvcTypeDeatils = brandSrvcTypeDetails === null || brandSrvcTypeDetails === void 0 ? void 0 : (_brandSrvcTypeDetails = brandSrvcTypeDetails.filter(singleSrvcTypeId => singleSrvcTypeId.service_type_id == singleSrvcType)) === null || _brandSrvcTypeDetails === void 0 ? void 0 : _brandSrvcTypeDetails[0];\n        if (singleBrandSrvcTypeDeatils == undefined) {\n          return;\n        }\n        let org_name = singleBrandSrvcTypeDeatils === null || singleBrandSrvcTypeDeatils === void 0 ? void 0 : singleBrandSrvcTypeDeatils.org_name;\n        let service_types_name = singleBrandSrvcTypeDeatils === null || singleBrandSrvcTypeDeatils === void 0 ? void 0 : singleBrandSrvcTypeDeatils.service_types_name;\n        let srvc_type_specific_fields = singleBrandSrvcTypeDeatils === null || singleBrandSrvcTypeDeatils === void 0 ? void 0 : singleBrandSrvcTypeDeatils.specific_fields;\n        console.log('srvc_type_specific_fields', srvc_type_specific_fields);\n        if (srvc_type_specific_fields) {\n          // let dummyVerticalOrSelectedSrvcTypeSpecificFields = this.getVerticalOrSelectedSrvcTypeSpecificFields(customFields,Files=false,selectedCustomFields=[])\n          // verticalOrSelectedSrvcTypeSpecificFields = [...verticalOrSelectedSrvcTypeSpecificFields,...dummyVerticalOrSelectedSrvcTypeSpecificFields];\n          let translatedFields = Files ? decodeFieldsMetaFrmJson(srvc_type_specific_fields, undefined, true) : decodeFieldsMetaFrmJson(srvc_type_specific_fields);\n          if (translatedFields) {\n            translatedFields.forEach(singleTranslatedFields => {\n              if ((singleTranslatedFields === null || singleTranslatedFields === void 0 ? void 0 : singleTranslatedFields.label) && (!Files || Files && !(selectedCustomFields === null || selectedCustomFields === void 0 ? void 0 : selectedCustomFields.includes(singleTranslatedFields === null || singleTranslatedFields === void 0 ? void 0 : singleTranslatedFields.key)))) {\n                let dummyObj = {\n                  label: `${org_name} - ${service_types_name} - ${singleTranslatedFields === null || singleTranslatedFields === void 0 ? void 0 : singleTranslatedFields.label}`,\n                  value: singleTranslatedFields === null || singleTranslatedFields === void 0 ? void 0 : singleTranslatedFields.key\n                };\n                if (!onlyAttachments) {\n                  verticalOrSelectedSrvcTypeSpecificFields.push(dummyObj);\n                } else if (!singleTranslatedFields.cust_component || (singleTranslatedFields === null || singleTranslatedFields === void 0 ? void 0 : singleTranslatedFields.cust_component) == 'Files') {\n                  verticalOrSelectedSrvcTypeSpecificFields.push(dummyObj);\n                }\n              }\n            });\n          }\n        }\n      });\n    }\n    //Get service provider specific details\n    let srvc_prvdr_specific_fields = (_this$formRef28 = this.formRef) === null || _this$formRef28 === void 0 ? void 0 : (_this$formRef28$curre = _this$formRef28.current) === null || _this$formRef28$curre === void 0 ? void 0 : _this$formRef28$curre.getFieldValue('sp_cust_fields_json');\n    if (srvc_prvdr_specific_fields) {\n      srvc_prvdr_specific_fields = Files ? decodeFieldsMetaFrmJson(srvc_prvdr_specific_fields, undefined, true) : decodeFieldsMetaFrmJson(srvc_prvdr_specific_fields);\n    }\n    if (srvc_prvdr_specific_fields) {\n      srvc_prvdr_specific_fields.forEach(singlesrvcPrvdrSpcField => {\n        if ((singlesrvcPrvdrSpcField === null || singlesrvcPrvdrSpcField === void 0 ? void 0 : singlesrvcPrvdrSpcField.label) && (!Files || Files && !(selectedCustomFields === null || selectedCustomFields === void 0 ? void 0 : selectedCustomFields.includes(singlesrvcPrvdrSpcField === null || singlesrvcPrvdrSpcField === void 0 ? void 0 : singlesrvcPrvdrSpcField.key)))) {\n          let srvcPrvdrSpecificFields = {\n            label: `Wify - ${singlesrvcPrvdrSpcField === null || singlesrvcPrvdrSpcField === void 0 ? void 0 : singlesrvcPrvdrSpcField.label}`,\n            value: singlesrvcPrvdrSpcField === null || singlesrvcPrvdrSpcField === void 0 ? void 0 : singlesrvcPrvdrSpcField.key\n          };\n          if (!onlyAttachments) {\n            verticalOrSelectedSrvcTypeSpecificFields.push(srvcPrvdrSpecificFields);\n          } else if (!singlesrvcPrvdrSpcField.cust_component || (singlesrvcPrvdrSpcField === null || singlesrvcPrvdrSpcField === void 0 ? void 0 : singlesrvcPrvdrSpcField.cust_component) == 'Files') {\n            verticalOrSelectedSrvcTypeSpecificFields.push(srvcPrvdrSpecificFields);\n          }\n        }\n      });\n    }\n    return verticalOrSelectedSrvcTypeSpecificFields;\n  }\n  getRatingConfigMeta(initialValues) {\n    var _this$formRef29, _this$formRef29$curre;\n    const enableRatingValue = (_this$formRef29 = this.formRef) === null || _this$formRef29 === void 0 ? void 0 : (_this$formRef29$curre = _this$formRef29.current) === null || _this$formRef29$curre === void 0 ? void 0 : _this$formRef29$curre.getFieldValue('enable_sp_rating');\n    return {\n      fields: [{\n        key: 'enable_sp_rating',\n        label: 'Enable Ratings',\n        widget: 'checkbox',\n        onChange: () => this.refresh()\n      }, ...(enableRatingValue ? [{\n        key: `vertical_type_rating_config`,\n        render: () => {\n          return /*#__PURE__*/React.createElement(Tabs, {\n            defaultActiveKey: \"sp_rating\",\n            className: \"gx-mb-2\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 2177,\n              columnNumber: 39\n            }\n          }, /*#__PURE__*/React.createElement(Tabs.TabPane, {\n            tab: \"Authorities\",\n            key: \"authorities\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 2181,\n              columnNumber: 43\n            }\n          }, /*#__PURE__*/React.createElement(FormBuilder, {\n            form: this.formRef,\n            meta: getRatingFormMeta(initialValues, this.getParamsFrSpAuthorityRatings()),\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 2185,\n              columnNumber: 47\n            }\n          })), \"(\", /*#__PURE__*/React.createElement(Tabs.TabPane, {\n            tab: \"Assignees\",\n            key: \"sp_assignees\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 2194,\n              columnNumber: 43\n            }\n          }, /*#__PURE__*/React.createElement(FormBuilder, {\n            form: this.formRef,\n            meta: getRatingFormMeta(initialValues, this.getParamsFrSpAssigneeRatings()),\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 2198,\n              columnNumber: 47\n            }\n          })), \")\");\n        }\n      }] : [])]\n    };\n  }\n  getSpPayoutsConfigMeta() {\n    var _this$formRef30, _this$formRef30$curre, _this$formRef31, _this$formRef31$curre, _this$state$viewData72, _this$formRef32, _this$formRef32$curre, _this$formRef33, _this$formRef33$curre, _this$state$viewData73;\n    // here\n    const enableSpPayouts = (_this$formRef30 = this.formRef) === null || _this$formRef30 === void 0 ? void 0 : (_this$formRef30$curre = _this$formRef30.current) === null || _this$formRef30$curre === void 0 ? void 0 : _this$formRef30$curre.getFieldValue('enable_sp_payouts');\n    const selectedSPAuthorities = (_this$formRef31 = this.formRef) === null || _this$formRef31 === void 0 ? void 0 : (_this$formRef31$curre = _this$formRef31.current) === null || _this$formRef31$curre === void 0 ? void 0 : _this$formRef31$curre.getFieldValue('authority_id');\n    const allOrgRoles = (_this$state$viewData72 = this.state.viewData) === null || _this$state$viewData72 === void 0 ? void 0 : _this$state$viewData72.role_list;\n    const deploymentRoles = (_this$formRef32 = this.formRef) === null || _this$formRef32 === void 0 ? void 0 : (_this$formRef32$curre = _this$formRef32.current) === null || _this$formRef32$curre === void 0 ? void 0 : _this$formRef32$curre.getFieldValue('deployment_possible_roles');\n    let spAuthoritiesFrOptions = [];\n    if (selectedSPAuthorities && allOrgRoles) {\n      spAuthoritiesFrOptions = allOrgRoles.filter(role => selectedSPAuthorities.includes(role.value));\n    }\n    let spAuthoritiesFrVendorOptions = [];\n    if (selectedSPAuthorities || deploymentRoles) {\n      // Combine the selected authorities and deployment roles into a single array\n      const combinedAuthorities = [...(selectedSPAuthorities || []), ...(deploymentRoles || [])];\n\n      // Create a Set to get distinct values\n      const distinctAuthorities = new Set(combinedAuthorities);\n\n      // Filter allOrgRoles to include only those with distinct authorities\n      spAuthoritiesFrVendorOptions = allOrgRoles.filter(role => distinctAuthorities.has(role.value));\n    }\n    const spPayoutsConfig = this.getConfigFrSpPayouts();\n    const addPayoutUserType = (_this$formRef33 = this.formRef) === null || _this$formRef33 === void 0 ? void 0 : (_this$formRef33$curre = _this$formRef33.current) === null || _this$formRef33$curre === void 0 ? void 0 : _this$formRef33$curre.getFieldValue('select_who_can_add_payout');\n    let userSelector = {\n      key: 'which_static_user_can_add_sp_payout',\n      widget: UserSelectorWidget,\n      placeholder: 'Search User',\n      className: 'wy-pl-1rem',\n      required: true,\n      widgetProps: {\n        mode: 'multiple',\n        allowClear: true\n      }\n    };\n    let authoritySelector = {\n      key: 'which_sp_authority_can_add_sp_payout',\n      widget: 'select',\n      placeholder: 'Select Authority',\n      className: 'wy-pl-1rem',\n      required: true,\n      widgetProps: {\n        mode: 'multiple',\n        allowClear: true,\n        showSearch: true,\n        optionFilterProp: 'children'\n      },\n      options: spAuthoritiesFrOptions\n    };\n    const selectAddSPPayoutWidgetRights = addPayoutUserType === 'authority' ? [authoritySelector] : addPayoutUserType === 'static_user' ? [userSelector] : [];\n    return {\n      fields: [{\n        key: 'enable_sp_payouts',\n        label: 'Enable Payout',\n        widget: 'checkbox',\n        onChange: e => {\n          this.refresh();\n          if (e.target.checked == true && this.formRef.current.getFieldValue('srvc_type_sp_payouts_config') == undefined) {\n            this.formRef.current.setFieldsValue({\n              srvc_type_sp_payouts_config: JSON.stringify({\n                '1236c271-876d-4352-a044-157acbeee076': {\n                  key: '1236c271-876d-4352-a044-157acbeee076',\n                  label: 'Payouts',\n                  total_field_label: ''\n                }\n              })\n            });\n          }\n        }\n      }, ...(enableSpPayouts ? [{\n        label: 'Service type sp payouts config json',\n        key: 'srvc_type_sp_payouts_config',\n        formItemProps: {\n          style: {\n            display: 'none'\n          }\n        }\n      }, {\n        key: 'SP Payouts',\n        render: () => /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(LineItemManagementModule, {\n          form: this.formRef,\n          onChange: newObj => {\n            this.formRef.current.setFieldsValue({\n              srvc_type_sp_payouts_config: JSON.stringify(newObj)\n            });\n            this.refresh();\n          },\n          currValue: spPayoutsConfig,\n          isVendorPayouts: true,\n          hideBtn: true,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 2331,\n            columnNumber: 39\n          }\n        }))\n      }, {\n        key: 'seperator_6',\n        render: () => /*#__PURE__*/React.createElement(\"div\", {\n          className: \"gx-mb-3 gx-bg-grey\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 2354,\n            columnNumber: 35\n          }\n        })\n      }, {\n        key: 'sp_vendor_roles',\n        label: 'Vendor Roles',\n        widget: 'select',\n        required: true,\n        widgetProps: {\n          mode: 'multiple',\n          allowClear: true,\n          showSearch: true,\n          optionFilterProp: 'children'\n        },\n        options: spAuthoritiesFrVendorOptions\n      }, {\n        key: 'select_who_can_add_payout',\n        label: 'Select who can add payout',\n        widget: 'radio-group',\n        onChange: () => this.refresh(),\n        required: true,\n        options: [{\n          value: 'authority',\n          label: 'Authority'\n        }, {\n          value: 'static_user',\n          label: 'Static User'\n        }]\n      }, ...selectAddSPPayoutWidgetRights, {\n        key: 'sp_who_will_not_be_able_to_see_the_sp_payout_tab',\n        label: 'Who will not be able to see the SP payout tab',\n        widget: 'select',\n        required: true,\n        widgetProps: {\n          mode: 'multiple',\n          allowClear: true,\n          showSearch: true,\n          optionFilterProp: 'children'\n        },\n        options: (_this$state$viewData73 = this.state.viewData) === null || _this$state$viewData73 === void 0 ? void 0 : _this$state$viewData73.role_list\n      }] : [])]\n    };\n  }\n  isProjectNature() {\n    var _this$formRef34, _this$formRef34$curre;\n    return ((_this$formRef34 = this.formRef) === null || _this$formRef34 === void 0 ? void 0 : (_this$formRef34$curre = _this$formRef34.current) === null || _this$formRef34$curre === void 0 ? void 0 : _this$formRef34$curre.getFieldValue('vertical_nature')) == PROJECT_BASED_SERVICE_TYPE;\n  }\n  getDynamicSbtskFields() {\n    var _this$formRef35, _this$formRef35$curre;\n    const returnDynamicSbtskFields = [];\n    const selectedSbtsks = this === null || this === void 0 ? void 0 : (_this$formRef35 = this.formRef) === null || _this$formRef35 === void 0 ? void 0 : (_this$formRef35$curre = _this$formRef35.current) === null || _this$formRef35$curre === void 0 ? void 0 : _this$formRef35$curre.getFieldValue('select_subtask_fr_readiness');\n    selectedSbtsks && selectedSbtsks.map(_eachSubTaskSelected => {\n      const selectedSubTaskName = this.state.viewData.applicable_subtasks_list.filter(_eachSubTask => _eachSubTask.value == _eachSubTaskSelected);\n      const mandatoryField = {\n        label: /*#__PURE__*/React.createElement(React.Fragment, null, \"Select mandatory field for\", ' ', /*#__PURE__*/React.createElement(\"span\", {\n          style: {\n            fontWeight: '500',\n            marginLeft: 2\n          },\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 2534,\n            columnNumber: 29\n          }\n        }, selectedSubTaskName[0].label)),\n        key: `select_mandatory_field_fr_${selectedSubTaskName[0].value}`,\n        widget: 'select',\n        required: true,\n        options: getSrvcReqStaticAndCustomPossibleFields(this.getPossibleSpecificFields(), ['full_address', 'srvc_prvdr', 'sbtsks', 'vertical_title'], true),\n        widgetProps: {\n          mode: 'multiple',\n          allowClear: true,\n          showSearch: true,\n          optionFilterProp: 'children'\n        }\n      };\n      returnDynamicSbtskFields.push(mandatoryField);\n    });\n    return returnDynamicSbtskFields;\n  }\n  getMetaFrSubtaskTypesConfiguration() {\n    const meta = {\n      columns: 1,\n      formItemLayout: null,\n      fields: [{\n        key: `applicable_subtask_types_fr_vertical`,\n        label: 'Applicable subtask types (leave empty if all are applicable)',\n        widget: 'select',\n        widgetProps: {\n          mode: 'multiple',\n          allowClear: true\n        },\n        options: this.getApplicableSubtaskTypes(),\n        onChange: value => {\n          this.onApplicableSubtaskTypesFrVerticalChange(value);\n          this.refresh();\n        }\n      }, {\n        key: `select_default_subtask_type`,\n        label: 'Select default subtask type',\n        widget: 'select',\n        options: this.getDefaultApplicableSubtaskTypesList(),\n        widgetProps: {\n          allowClear: true,\n          onChange: value => {\n            handleClearSelect(value, this.formRef, 'select_default_subtask_type');\n          }\n        }\n      }]\n    };\n    return meta;\n  }\n  getApplicableSubtaskTypes() {\n    var _this$state$viewData74;\n    return ((_this$state$viewData74 = this.state.viewData) === null || _this$state$viewData74 === void 0 ? void 0 : _this$state$viewData74.applicable_subtasks_list) || [];\n  }\n  getDefaultApplicableSubtaskTypesList() {\n    var _this$formRef36, _this$formRef36$curre, _this$state$viewData75, _applicableSubtaskTyp;\n    const selectedApplicableSubtaskTypes = ((_this$formRef36 = this.formRef) === null || _this$formRef36 === void 0 ? void 0 : (_this$formRef36$curre = _this$formRef36.current) === null || _this$formRef36$curre === void 0 ? void 0 : _this$formRef36$curre.getFieldValue('applicable_subtask_types_fr_vertical')) || [];\n    let applicableSubtaskTypes = ((_this$state$viewData75 = this.state.viewData) === null || _this$state$viewData75 === void 0 ? void 0 : _this$state$viewData75.applicable_subtasks_list) || [];\n    if ((selectedApplicableSubtaskTypes === null || selectedApplicableSubtaskTypes === void 0 ? void 0 : selectedApplicableSubtaskTypes.length) > 0) {\n      applicableSubtaskTypes = applicableSubtaskTypes.filter(singleApplicableSubtaskType => selectedApplicableSubtaskTypes.includes(singleApplicableSubtaskType.value));\n    }\n    const defaultSubtaskTypesList = ((_applicableSubtaskTyp = applicableSubtaskTypes) === null || _applicableSubtaskTyp === void 0 ? void 0 : _applicableSubtaskTyp.length) > 0 ? applicableSubtaskTypes : this.getApplicableSubtaskTypes();\n    return defaultSubtaskTypesList;\n  }\n  onApplicableSubtaskTypesFrVerticalChange(value) {\n    var _this$formRef37, _this$formRef37$curre;\n    const defaultSubtaskType = (_this$formRef37 = this.formRef) === null || _this$formRef37 === void 0 ? void 0 : (_this$formRef37$curre = _this$formRef37.current) === null || _this$formRef37$curre === void 0 ? void 0 : _this$formRef37$curre.getFieldValue('select_default_subtask_type');\n    if (!value.includes(defaultSubtaskType)) {\n      handleClearSelect(undefined, this.formRef, 'select_default_subtask_type');\n    }\n  }\n  render() {\n    var _this$state$viewData76, _this$state$viewData77, _this$state$viewData78, _this$state$viewData79, _this$state$viewData80, _this$state$viewData81;\n    const {\n      editorItem\n    } = this.props;\n    const {\n      isFormSubmitting,\n      visible,\n      isLoadingViewData,\n      error,\n      viewData,\n      showFormPreview,\n      formPreviewMeta,\n      profitLossTab\n    } = this.state;\n    var editorTitle = editorItem === null || editorItem === void 0 ? void 0 : editorItem.title;\n    var editMode = true;\n    if (editorTitle == undefined) {\n      editorTitle = 'Add new custom fields';\n      editMode = false;\n    } else {\n      editorTitle = 'Edit custom fields - ' + editorTitle;\n    }\n    const initialValues = this.state.editMode ? (_this$state$viewData76 = this.state.viewData) === null || _this$state$viewData76 === void 0 ? void 0 : (_this$state$viewData77 = _this$state$viewData76.form_data) === null || _this$state$viewData77 === void 0 ? void 0 : _this$state$viewData77.form_data : {};\n    //Set default table columns for srvc_type_tabular_view_columns\n    if ((initialValues === null || initialValues === void 0 ? void 0 : initialValues.hasOwnProperty('srvc_type_tabular_view_columns')) == false) {\n      initialValues['srvc_type_tabular_view_columns'] = addDefaultKeysForTabularView();\n    }\n    return visible ? /*#__PURE__*/React.createElement(Modal, {\n      title: `${editorTitle}`,\n      visible: visible,\n      onOk: this.handleOk,\n      confirmLoading: isFormSubmitting,\n      width: 1300,\n      onCancel: this.handleCancel,\n      footer: null,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2674,\n        columnNumber: 13\n      }\n    }, isLoadingViewData ? /*#__PURE__*/React.createElement(\"div\", {\n      className: \"gx-loader-view gx-loader-position\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2684,\n        columnNumber: 21\n      }\n    }, /*#__PURE__*/React.createElement(CircularProgress, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2685,\n        columnNumber: 25\n      }\n    })) : viewData == undefined ? /*#__PURE__*/React.createElement(\"p\", {\n      className: \"gx-text-red\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2688,\n        columnNumber: 21\n      }\n    }, error) : /*#__PURE__*/React.createElement(Form, {\n      className: \"ant-col gx-my-1 ant-col-xs-24 gx-mt-0\",\n      layout: \"vertical\",\n      ref: this.formRef,\n      onFinish: data => {\n        this.submitForm(data);\n      },\n      initialValues: this.state.editMode ? initialValues : {},\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2690,\n        columnNumber: 21\n      }\n    }, /*#__PURE__*/React.createElement(Tabs, {\n      defaultActiveKey: \"sp_fields\",\n      onChange: activeKey => {\n        if (activeKey === 'profitLossTab') {\n          this.setState({\n            profitLossTab: true\n          });\n        }\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2699,\n        columnNumber: 25\n      }\n    }, /*#__PURE__*/React.createElement(Tabs.TabPane, {\n      tab: \"SP Fields\",\n      key: \"sp_fields\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2709,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(FormBuilder, {\n      meta: this.getMeta(),\n      form: this.formRef,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2710,\n        columnNumber: 33\n      }\n    })), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n      tab: \"Line Item\",\n      key: \"line_item\",\n      forceRender: true,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2716,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(FormBuilder, {\n      meta: this.getMetaFrLineItemConfiguration(),\n      form: this.formRef,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2721,\n        columnNumber: 33\n      }\n    })), !this.isProjectNature() && /*#__PURE__*/React.createElement(Tabs.TabPane, {\n      tab: \"Subtask Types\",\n      key: \"subtask_types\",\n      forceRender: true,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2727,\n        columnNumber: 33\n      }\n    }, /*#__PURE__*/React.createElement(FormBuilder, {\n      meta: this.getMetaFrSubtaskTypesConfiguration(),\n      form: this.formRef,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2732,\n        columnNumber: 37\n      }\n    })), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n      tab: \"Deployment\",\n      key: \"deployment\",\n      forceRender: true,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2738,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(FormBuilder, {\n      meta: this.getDeploymentConfigMeta(initialValues),\n      form: this.formRef,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2743,\n        columnNumber: 33\n      }\n    })), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n      tab: \"Daily updates\",\n      key: \"daily_updates\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2751,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(FormBuilder, {\n      meta: this.getDailyUpdatesMeta(initialValues),\n      form: this.formRef,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2755,\n        columnNumber: 33\n      }\n    })), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n      tab: \"Pricing Master\",\n      key: \"pricing_master\",\n      forceRender: false,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2763,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(FormBuilder, {\n      meta: this.getPricingMasterConfigMeta(initialValues),\n      form: this.formRef,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2768,\n        columnNumber: 33\n      }\n    })), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n      tab: \"Billing\",\n      key: \"billing\",\n      forceRender: true,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2776,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(FormBuilder, {\n      meta: this.getBillingMasterConfigMeta(initialValues),\n      form: this.formRef,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2781,\n        columnNumber: 33\n      }\n    })), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n      tab: \"SP Payouts\",\n      key: \"sp_payouts\",\n      forceRender: true,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2788,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(FormBuilder, {\n      meta: this.getSpPayoutsConfigMeta(),\n      form: this.formRef,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2793,\n        columnNumber: 33\n      }\n    })), !this.isProjectNature() && /*#__PURE__*/React.createElement(Tabs.TabPane, {\n      tab: \"P&L\",\n      key: \"profitLossTab\",\n      forceRender: true,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2799,\n        columnNumber: 33\n      }\n    }, profitLossTab && /*#__PURE__*/React.createElement(ProfitLossConfig, {\n      roleList: (_this$state$viewData78 = this.state.viewData) === null || _this$state$viewData78 === void 0 ? void 0 : _this$state$viewData78.role_list,\n      form_data: (_this$state$viewData79 = this.state.viewData) === null || _this$state$viewData79 === void 0 ? void 0 : _this$state$viewData79.form_data,\n      form: this.formRef,\n      editMode: this.props.editMode,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2805,\n        columnNumber: 41\n      }\n    })), this.isProjectNature() && /*#__PURE__*/React.createElement(Tabs.TabPane, {\n      tab: \"P&L\",\n      key: \"profitLossTab\",\n      forceRender: true,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2819,\n        columnNumber: 33\n      }\n    }, profitLossTab && /*#__PURE__*/React.createElement(ProjectPLConfig, {\n      form: this.formRef,\n      roleList: (_this$state$viewData80 = this.state.viewData) === null || _this$state$viewData80 === void 0 ? void 0 : _this$state$viewData80.role_list,\n      form_data: (_this$state$viewData81 = this.state.viewData) === null || _this$state$viewData81 === void 0 ? void 0 : _this$state$viewData81.form_data,\n      isVendorPayouts: true,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2825,\n        columnNumber: 41\n      }\n    })), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n      tab: \"Notification\",\n      key: \"notification\",\n      forceRender: true,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2838,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(FormBuilder, {\n      meta: this.getFieldsWiseNotificationConfigMeta(initialValues),\n      form: this.formRef,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2843,\n        columnNumber: 33\n      }\n    })), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n      tab: \"Tabular view\",\n      key: \"tabular_view\",\n      forceRender: true,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2851,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(FormBuilder, {\n      meta: this.getTabularViewFieldsConfigMeta(initialValues),\n      form: this.formRef,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2856,\n        columnNumber: 33\n      }\n    })), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n      tab: \"Views\",\n      key: \"views\",\n      forceRender: true,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2864,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(FormBuilder, {\n      meta: this.getViewFieldsConfigMeta(),\n      form: this.formRef,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2869,\n        columnNumber: 33\n      }\n    })), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n      tab: \"SP Authorities\",\n      key: \"sp_authorities\",\n      forceRender: true,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2875,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(FormBuilder, {\n      meta: this.getSpAuthoritiesMeta(),\n      form: this.formRef,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2880,\n        columnNumber: 33\n      }\n    })), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n      tab: \"Automations\",\n      key: \"automations\",\n      forceRender: true,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2885,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(FormBuilder, {\n      meta: this.getAutomationMeta(),\n      form: this.formRef,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2890,\n        columnNumber: 33\n      }\n    })), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n      tab: \"SP Ratings\",\n      key: \"sp_rating\",\n      forceRender: true,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2895,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(FormBuilder, {\n      meta: this.getRatingConfigMeta(initialValues),\n      form: this.formRef,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2900,\n        columnNumber: 33\n      }\n    }))), /*#__PURE__*/React.createElement(Form.Item, {\n      className: \"gx-mt-3\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2909,\n        columnNumber: 25\n      }\n    }, /*#__PURE__*/React.createElement(Button, {\n      type: \"primary\",\n      htmlType: \"submit\",\n      disabled: isFormSubmitting,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2910,\n        columnNumber: 29\n      }\n    }, editMode ? 'Save' : 'Submit')), isFormSubmitting ? /*#__PURE__*/React.createElement(\"div\", {\n      className: \"gx-loader-view gx-loader-position\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2919,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(CircularProgress, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2920,\n        columnNumber: 33\n      }\n    })) : null, error ? /*#__PURE__*/React.createElement(\"p\", {\n      className: \"gx-text-red\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2923,\n        columnNumber: 34\n      }\n    }, error) : null), showFormPreview && /*#__PURE__*/React.createElement(FormPreviewMeta, {\n      formPreviewMeta: formPreviewMeta,\n      onCancel: () => {\n        this.setState({\n          showFormPreview: false\n        });\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2927,\n        columnNumber: 21\n      }\n    })) : /*#__PURE__*/React.createElement(React.Fragment, null);\n  }\n}\nexport default SpConfigEditor;", "map": {"version": 3, "names": ["React", "Component", "Modal", "Form", "Input", "<PERSON><PERSON>", "Collapse", "Tabs", "<PERSON><PERSON>", "FormBuilder", "http_utils", "CircularProgress", "FormPreviewMeta", "ZoomInOutlined", "Link", "LineItemManagementModule", "PricingMaster", "TimePickerWidget", "BILLING_TYPE", "PROJECT_BASED_SERVICE_TYPE", "RATING_TYPE", "getOptionsFrNatureOfServiceType", "handleClearSelect", "validateLambdaArn", "decodeFieldsMetaFrmJson", "decodeFileSectionsFrmJson", "SpecificFieldsWiseNotificationModule", "BillingDiscountingConfigModule", "addDefaultKeysForTabularView", "getDefaultColumnsForTabularView", "getRatingFormMeta", "getSrvcReqStaticAndCustomPossibleFields", "getSrvcReqStaticAndCustomPossibleFieldsFilter", "UserSelectorWidget", "getUsersInfoMeta", "ConfigHelpers", "SP_USER_EXCLUDED_FIELDS", "InputTable", "ProfitLossConfig", "ProjectPLConfig", "InlineCheckbox", "protoUrl", "submitUrl", "TextArea", "startOfDay", "endOfDay", "DailyUpdateModes", "value", "label", "SpConfigEditor", "constructor", "props", "initState", "render_helper", "visible", "isFormSubmitting", "viewData", "undefined", "isLoadingViewData", "editMode", "error", "editModeForceRefreshDone", "spSrvcTypeSpecificFieldsNotification", "profitLossTab", "revenuecol", "state", "handleOk", "setState", "updateClosureToParent", "handleCancel", "submitForm", "data", "params", "srvc_type_revenue_column_meta", "JSON", "parse", "for<PERSON>ach", "singleObj", "_this$state$viewData", "_this$state$viewData$", "_this$state$viewData$2", "_this$state$viewData2", "_this$state$viewData3", "_this$state$viewData4", "_this$state$viewData5", "_this$state$viewData6", "_this$state$viewData7", "console", "log", "form_data", "row_id", "sku", "item_name", "qty", "revenue_column_meta", "_this$state$viewData8", "_this$state$viewData9", "_this$state$viewData0", "_this$state$viewData1", "_this$state$viewData10", "_this$state$viewData11", "_this$state$viewData12", "_this$state$viewData13", "_this$state$viewData14", "onComplete", "resp", "tellParentToRefreshList", "entry_id", "onError", "decodeErrorToMessage", "performPutCall", "editor<PERSON><PERSON>", "id", "performPostCall", "getMeta", "_this$formRef", "_this$formRef$current", "_this$formRef2", "_this$formRef2$curren", "_this$state$viewData15", "initialValues", "getInitialValues", "FieldsInitialValues", "sp_cust_fields_json", "customFields", "formRef", "current", "getFieldValue", "isCustFieldDynamicEnabled", "meta", "columns", "formItemLayout", "fields", "key", "required", "rules", "max", "widget", "createElement", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "srvc_type_id", "refresh", "options", "widgetProps", "mode", "optionFilterProp", "getOrgListBasedOnNature", "message", "placeholder", "event", "render", "onClick", "e", "handleShowFieldsPreviewClick", "icon", "to", "encodeURIComponent", "target", "className", "allowClear", "validator", "getPossibleSpecificFields", "location_grp_list", "tooltip", "getMetaFrSearchableCustomFields", "checkIfPresentInEssentialField", "singleField", "essentialFields", "essentialField", "getBillingformMeta", "_this$state$viewData16", "_this$state$viewData17", "_this$state$viewData18", "_this$state$viewData19", "_this$state$viewData20", "showSearch", "role_list", "getDiscountingBillingformMeta", "_this$state$viewData21", "_this$state$viewData22", "newObj", "srvc_type_billing_discounting_rule_config", "stringify", "currValue", "getBillingDiscountConfig", "authorities_list", "getAdditionalBillingformMeta", "Fragment", "srvc_type_additional_billing_config", "getAdditionalBillingJson", "hideBtn", "getAuthoritiesList", "_this$formRef3", "_this$formRef3$curren", "_this$state$viewData23", "authoritiesList", "authority_id", "roleList", "length", "singleAuthority", "_roleList$filter", "filteredAuthorityList", "filter", "singleRole", "push", "getSpecificFieldsWiseNotificationFieldsMeta", "formItemProps", "style", "display", "_this$formRef4", "_this$formRef4$curren", "_this$state$viewData24", "_this$formRef5", "_this$formRef5$curren", "specific_fields", "brand_wise_specific_fields", "initialValue", "getSpecificFieldsNotificationJson", "srvc_type_specific_fields_wise_notification", "isSrvcPrvdrTab", "sp_specific_fields", "getDeploymentConfigMeta", "_this$formRef$current2", "_this$state$viewData25", "_this$formRef6", "_this$formRef6$curren", "_this$state$viewData26", "_this$state$viewData27", "_this$state$viewData29", "_this$state$viewData30", "_this$state$viewData31", "_this$formRef7", "_this$formRef7$curren", "startTimeFrEndTime", "role_vs_subtask_fields", "possible_roles", "deployment_roles", "deployment_possible_roles", "singleDeploymentRoleId", "_this$state$viewData28", "role_details", "applicable_subtasks_list", "beginLimit", "endLimit", "step", "getDailyUpdatesMeta", "_this$state$viewData32", "_this$formRef8", "_this$formRef8$curren", "_this$formRef9", "_this$formRef9$curren", "_this$formRef0", "_this$formRef0$curren", "_this$state$viewData33", "_this$state$viewData34", "daily_update_issue_fields", "daily_update_issue_form_fields", "sp_daily_update_form_fields", "isTrackingAllLineItemWiseProgress", "daily_update_track_line_item_progress", "forceUpdate", "checked", "getSelectedSpAuthoritiesRoleList", "_this$formRef1", "_this$formRef1$curren", "seletcedRoleIds", "selectedRoleData", "_this$state$viewData35", "roleData", "find", "getSpAuthoritiesMeta", "_this$state$viewData36", "_this$state$viewData37", "_this$formRef10", "_this$formRef10$curre", "_this$formRef11", "_this$formRef11$curre", "roleWiseSpecificFields", "enableLocGrpFilteration", "selected_srvc_prvdr_roles_authorities", "authorities_role_list", "isFilterationEnable", "_authorities_role_lis", "_authorities_role_lis2", "seletcedRoleTitle", "dummy<PERSON>b<PERSON>", "roleWiseSpecificFieldsobj", "getVerticalOrSelectedSrvcTypeSpecificFields", "obj", "selected_roles_srvc_authorities_seperator", "type", "currentSelectedRolesFrAutohrities", "updatedSelectedAuthorities", "auth", "includes", "select_authorities_to_be_filtered_fr_loc_grp", "getParamsFrSpAuthorityRatings", "keySelectedAuthoritiesorDeployment", "keyRoleList", "<PERSON><PERSON>ey", "tabName", "ratingTypeKey", "authorityIdKey", "selectedRatingAuthority", "staticUserKey", "<PERSON><PERSON><PERSON>", "getParamsFrSpAssigneeRatings", "isProjectNature", "selectedRatingDynamicUserRole", "isProjectBased", "selectedRolesToBeRatedKey", "modeOfRatingFrAssignees<PERSON>ey", "HasMatchingCustomerAccessKey", "HasMatchingLocationGroupKey", "getAutomationMeta", "_this$formRef12", "_this$formRef12$curre", "_allSelectedSubTasks", "getFieldsValue", "getDynamicSbtskFields", "_this$state$viewData38", "defaultActiveKey", "TabPane", "tab", "form", "createRef", "componentDidMount", "initViewData", "url", "performGetCall", "componentDidUpdate", "prevProps", "prevState", "showEditor", "refreshOnUpdate", "onClose", "onDataModified", "handleRowDataChange", "newRowData", "getLineItemConfig", "_this$formRef13", "_this$formRef13$curre", "_this$state$viewData39", "_this$state$viewData40", "_this$state$viewData41", "<PERSON><PERSON><PERSON>", "srvc_type_line_item_config", "_this$state$viewData42", "_this$state$viewData43", "_this$state$viewData44", "getConfigFrSpPayouts", "_this$formRef14", "_this$formRef14$curre", "_this$state$viewData45", "_this$state$viewData46", "_this$state$viewData47", "srvc_type_sp_payouts_config", "_this$state$viewData48", "_this$state$viewData49", "_this$state$viewData50", "getConfigFrDeduction", "_this$formRef15", "_this$formRef15$curre", "_this$state$viewData51", "_this$state$viewData52", "_this$state$viewData53", "srvc_type_deduction_config", "_this$state$viewData54", "_this$state$viewData55", "_this$state$viewData56", "getMetaFrLineItemConfiguration", "lineItemConfig", "who_cannot_download_sp_line_item", "who_cannot_view_line_items_tab", "Object", "keys", "_this$state$viewData57", "_this$state$viewData58", "marginTop", "_this$state$viewData59", "_this$state$viewData60", "_this$formRef16", "_this$formRef16$curre", "vertical_nature", "org_list", "filtered_org_list", "singleOrgSrvcTypeObj", "srvc_type_nature", "prefix", "getSearchableCustomFields", "<PERSON><PERSON><PERSON>", "selectedOption", "filterConfigValue", "map", "singleElem", "search_keyword", "searchable_sp_fields_form_data", "getPricingConfig", "_this$formRef17", "_this$formRef17$curre", "_this$state$viewData61", "_this$state$viewData62", "_this$state$viewData63", "config_in_form_data", "srvc_type_pricing_config_for_line_item", "onSaveOfPricingConfig", "singleLineItemGrpKey", "pricingConfigData", "getPricingConfigFrManday", "_this$formRef18", "_this$formRef18$curre", "_this$state$viewData64", "_this$state$viewData65", "_this$state$viewData66", "srvc_type_pricing_config_for_manday", "onSaveOfPricingConfigFrManday", "pricingConfigDataFrManday", "getSelectedOrgWithSrvcTypeIds", "_this$formRef19", "_this$formRef19$curre", "_this$getInitialValue", "<PERSON><PERSON><PERSON><PERSON>", "srvcTypeIds", "Array", "isArray", "singlesrvcTypeId", "_org_list$filter", "selectedOrgsList", "singleOrg", "getPricingMasterConfigMeta", "lineItemConfigData", "_this$state$viewData67", "index", "_lineItemConfigData$s", "pricingInitialValue", "fullFormInitialValue", "config_data", "isManday", "_this$formRef20", "_this$formRef20$curre", "labelValuePairData", "_this$formRef21", "_this$formRef21$curre", "cust_component", "cust_widget", "require", "getPossibleSpecificFilesFields", "_this$formRef22", "_this$formRef22$curre", "customFileFields", "_this$formRef23", "_this$formRef23$curre", "_this$state$viewData68", "_this$state$viewData69", "initial<PERSON><PERSON><PERSON><PERSON>", "_this$formRef24", "_this$formRef24$curre", "additional_line_item", "getDeductionConfigMeta", "_this$formRef25", "_this$formRef25$curre", "isDeductionEnable", "deductionConfig", "deduction_item", "total_field_label", "isDeduction", "getFieldsWiseNotificationConfigMeta", "getFieldWiseAuthorityNotification", "_this$formRef26", "_this$formRef26$curre", "_this$state$viewData70", "_this$state$viewData71", "getBillingMasterConfigMeta", "showFormPreview", "formPreviewMeta", "getTabularViewFieldsConfigMeta", "getViewFieldsConfigMeta", "Files", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlyAttachments", "_this$formRef27", "_this$formRef27$curre", "_this$formRef28", "_this$formRef28$curre", "brandSrvcTypeDetails", "verticalOrSelectedSrvcTypeSpecificFields", "seletcedSrvcType", "singleSrvcType", "_brandSrvcTypeDetails", "singleBrandSrvcTypeDeatils", "singleSrvcTypeId", "service_type_id", "org_name", "service_types_name", "srvc_type_specific_fields", "<PERSON><PERSON>ields", "singleTranslatedFields", "srvc_prvdr_specific_fields", "singlesrvcPrvdrSpcField", "srvcPrvdrSpecificFields", "getRatingConfigMeta", "_this$formRef29", "_this$formRef29$curre", "enableRatingValue", "getSpPayoutsConfigMeta", "_this$formRef30", "_this$formRef30$curre", "_this$formRef31", "_this$formRef31$curre", "_this$state$viewData72", "_this$formRef32", "_this$formRef32$curre", "_this$formRef33", "_this$formRef33$curre", "_this$state$viewData73", "enableSpPayouts", "selectedSPAuthorities", "allOrgRoles", "deploymentRoles", "spAuthoritiesFrOptions", "role", "spAuthoritiesFrVendorOptions", "combinedAuthorities", "distinctAuthorities", "Set", "has", "spPayoutsConfig", "addPayoutUserType", "userSelector", "authoritySelector", "selectAddSPPayoutWidgetRights", "isVendorPayouts", "_this$formRef34", "_this$formRef34$curre", "_this$formRef35", "_this$formRef35$curre", "returnDynamicSbtskFields", "selectedSbtsks", "_eachSubTaskSelected", "selectedSubTaskName", "_eachSubTask", "mandatoryField", "fontWeight", "marginLeft", "getMetaFrSubtaskTypesConfiguration", "getApplicableSubtaskTypes", "onApplicableSubtaskTypesFrVerticalChange", "getDefaultApplicableSubtaskTypesList", "_this$state$viewData74", "_this$formRef36", "_this$formRef36$curre", "_this$state$viewData75", "_applicableSubtaskTyp", "selectedApplicableSubtaskTypes", "applicableSubtaskTypes", "singleApplicableSubtaskType", "defaultSubtaskTypesList", "_this$formRef37", "_this$formRef37$curre", "defaultSubtaskType", "_this$state$viewData76", "_this$state$viewData77", "_this$state$viewData78", "_this$state$viewData79", "_this$state$viewData80", "_this$state$viewData81", "editor<PERSON><PERSON><PERSON>", "title", "hasOwnProperty", "onOk", "confirmLoading", "width", "onCancel", "footer", "layout", "ref", "onFinish", "active<PERSON><PERSON>", "forceRender", "<PERSON><PERSON>", "htmlType", "disabled"], "sources": ["C:/Users/<USER>/Desktop/Github/wify_tms_v2/frontend/react-app1/src/routes/setup/srvc-req/sp-fields/SpConfigEditor.js"], "sourcesContent": ["import React, { Component } from 'react';\r\nimport { Modal, Form, Input, Button, Collapse, Tabs, Alert } from 'antd';\r\nimport FormBuilder from 'antd-form-builder';\r\nimport http_utils from '../../../../util/http_utils';\r\nimport CircularProgress from '../../../../components/CircularProgress';\r\nimport FormPreviewMeta from '../../../../components/wify-utils/FieldCreator/FormPreviewMeta';\r\nimport { ZoomInOutlined } from '@ant-design/icons';\r\nimport { Link } from 'react-router-dom';\r\nimport LineItemManagementModule from '../service-types/project-components/LineItemManagementModule';\r\nimport PricingMaster from '../service-types/PricingMaster';\r\nimport TimePickerWidget from '../../../../components/wify-utils/TimePickerWidget';\r\nimport {\r\n    BILLING_TYPE,\r\n    PROJECT_BASED_SERVICE_TYPE,\r\n    RATING_TYPE,\r\n    getOptionsFrNatureOfServiceType,\r\n    handleClearSelect,\r\n    validateLambdaArn,\r\n} from '../../../../util/helpers';\r\nimport {\r\n    decodeFieldsMetaFrmJson,\r\n    decodeFileSectionsFrmJson,\r\n} from '../../../../components/wify-utils/FieldCreator/helpers';\r\nimport SpecificFieldsWiseNotificationModule from '../service-types/project-components/SpecificFieldsWiseNotificationModule';\r\nimport BillingDiscountingConfigModule from '../service-types/project-components/BillingDiscountingConfigModule';\r\nimport {\r\n    addDefaultKeysForTabularView,\r\n    getDefaultColumnsForTabularView,\r\n    getRatingFormMeta,\r\n    getSrvcReqStaticAndCustomPossibleFields,\r\n} from '../../../services/helpers';\r\nimport { getSrvcReqStaticAndCustomPossibleFieldsFilter } from '../../../services/helpers';\r\nimport UserSelectorWidget from '../../../../components/wify-utils/UserSelectorWidget';\r\nimport { getUsersInfoMeta } from '../../../users/helper';\r\nimport ConfigHelpers from '../../../../util/ConfigHelpers';\r\nimport { SP_USER_EXCLUDED_FIELDS } from '../../../../util/constants';\r\nimport InputTable from '../../../../components/WIFY/subtasks/InputTable';\r\nimport ProfitLossConfig from './ProfitLossConfig.tsx';\r\nimport ProjectPLConfig from './ProjectPLConfig.tsx';\r\nimport InlineCheckbox from '../../../../components/WIFY/InlineCheckbox.tsx';\r\n\r\nconst protoUrl = '/setup/sp-custom-fields/proto';\r\nconst submitUrl = '/setup/sp-custom-fields';\r\nconst { TextArea } = Input;\r\nconst startOfDay = '12:00AM';\r\nconst endOfDay = '11:45PM';\r\n\r\nconst DailyUpdateModes = [\r\n    {\r\n        value: 'lenient_mode',\r\n        label: 'Lenient mode(Progress is autofilled till the previous date and no restriction in progress slider movement)',\r\n    },\r\n    {\r\n        value: 'strict_mode',\r\n        label: 'Strict mode(Progress is autofilled till the previous date and cannot be modified out of permissible range)',\r\n    },\r\n];\r\nclass SpConfigEditor extends Component {\r\n    constructor(props) {\r\n        super(props);\r\n        this.formRef = React.createRef();\r\n    }\r\n\r\n    initState = {\r\n        render_helper: false,\r\n        visible: false,\r\n        isFormSubmitting: false,\r\n        viewData: undefined,\r\n        isLoadingViewData: false,\r\n        editMode: this.props.editMode,\r\n        error: '',\r\n        editModeForceRefreshDone: false,\r\n        spSrvcTypeSpecificFieldsNotification: undefined,\r\n        profitLossTab: false,\r\n        revenuecol: [],\r\n    };\r\n    state = this.initState;\r\n\r\n    componentDidMount() {\r\n        this.initViewData();\r\n    }\r\n\r\n    initViewData() {\r\n        if (\r\n            (this.state.editMode && this.state.visible) ||\r\n            (!this.state.editMode &&\r\n                this.state.viewData == undefined &&\r\n                !this.state.isLoadingViewData)\r\n        ) {\r\n            this.setState({\r\n                isLoadingViewData: true,\r\n            });\r\n            var params = {};\r\n            const onComplete = (resp) => {\r\n                this.setState({\r\n                    isLoadingViewData: false,\r\n                    viewData: resp.data,\r\n                    error: '',\r\n                });\r\n            };\r\n            const onError = (error) => {\r\n                // console.log(error.response.status);\r\n                this.setState({\r\n                    isLoadingViewData: false,\r\n                    error: http_utils.decodeErrorToMessage(error),\r\n                });\r\n            };\r\n            var url = !this.state.editMode\r\n                ? protoUrl\r\n                : protoUrl + '/' + this.props.editorItem.id;\r\n            // console.log(url);\r\n            http_utils.performGetCall(url, params, onComplete, onError);\r\n        }\r\n    }\r\n\r\n    componentDidUpdate(prevProps, prevState) {\r\n        if (\r\n            prevProps.editorItem != this.props.editorItem ||\r\n            prevProps.showEditor != this.props.showEditor\r\n        ) {\r\n            this.setState(\r\n                {\r\n                    render_helper: !this.state.render_helper,\r\n                    visible: this.props.showEditor,\r\n                },\r\n                function () {\r\n                    if (this.props.showEditor && this.state.editMode) {\r\n                        this.initViewData();\r\n                    }\r\n                }\r\n            );\r\n        } else {\r\n            if (this.state.refreshOnUpdate) {\r\n                this.setState(\r\n                    {\r\n                        refreshOnUpdate: false,\r\n                    },\r\n                    this.initViewData()\r\n                );\r\n            }\r\n        }\r\n    }\r\n\r\n    handleOk = () => {\r\n        this.setState({\r\n            visible: false,\r\n            isFormSubmitting: false,\r\n        });\r\n        this.updateClosureToParent();\r\n    };\r\n\r\n    updateClosureToParent() {\r\n        if (this.props.onClose != undefined) {\r\n            this.props.onClose();\r\n        }\r\n        this.setState({\r\n            refreshOnUpdate: true,\r\n            ...this.initState,\r\n        });\r\n    }\r\n\r\n    tellParentToRefreshList(entry_id) {\r\n        if (this.props.onDataModified != undefined) {\r\n            this.props.onDataModified(entry_id);\r\n        }\r\n    }\r\n\r\n    handleCancel = () => {\r\n        this.setState({\r\n            visible: false,\r\n        });\r\n        this.updateClosureToParent();\r\n    };\r\n\r\n    submitForm = (data) => {\r\n        this.setState({\r\n            isFormSubmitting: true,\r\n        });\r\n        var params = data;\r\n        if (data?.srvc_type_revenue_column_meta) {\r\n            const srvc_type_revenue_column_meta = JSON.parse(\r\n                data?.srvc_type_revenue_column_meta\r\n            );\r\n            if (srvc_type_revenue_column_meta) {\r\n                srvc_type_revenue_column_meta.forEach((singleObj) => {\r\n                    console.log('yeti singleObj', singleObj);\r\n                    if (\r\n                        this.state.viewData?.form_data?.form_data?.[\r\n                            `sku_${singleObj.row_id}`\r\n                        ] &&\r\n                        !data?.[`sku_${singleObj.row_id}`]\r\n                    ) {\r\n                        params[`sku_${singleObj.row_id}`] =\r\n                            singleObj?.sku || '';\r\n                    }\r\n                    if (\r\n                        this.state.viewData?.form_data?.form_data?.[\r\n                            `item_name_${singleObj.row_id}`\r\n                        ] &&\r\n                        !data?.[`item_name_${singleObj.row_id}`]\r\n                    ) {\r\n                        params[`item_name_${singleObj.row_id}`] =\r\n                            singleObj?.item_name || '';\r\n                    }\r\n                    if (\r\n                        this.state.viewData?.form_data?.form_data?.[\r\n                            `qty_${singleObj.row_id}`\r\n                        ] &&\r\n                        !data?.[`qty_${singleObj.row_id}`]\r\n                    ) {\r\n                        params[`qty_${singleObj.row_id}`] =\r\n                            singleObj?.qty || '';\r\n                    }\r\n                });\r\n            }\r\n        }\r\n        if (data?.revenue_column_meta) {\r\n            const revenue_column_meta = JSON.parse(data?.revenue_column_meta);\r\n            if (revenue_column_meta) {\r\n                revenue_column_meta.forEach((singleObj) => {\r\n                    if (\r\n                        this.state.viewData?.form_data?.form_data?.[\r\n                            `sku_${singleObj.row_id}`\r\n                        ] &&\r\n                        !data?.[`sku_${singleObj.row_id}`]\r\n                    ) {\r\n                        params[`sku_${singleObj.row_id}`] = '';\r\n                    }\r\n                    if (\r\n                        this.state.viewData?.form_data?.form_data?.[\r\n                            `item_name_${singleObj.row_id}`\r\n                        ] &&\r\n                        !data?.[`item_name_${singleObj.row_id}`]\r\n                    ) {\r\n                        params[`item_name_${singleObj.row_id}`] = '';\r\n                    }\r\n                    if (\r\n                        this.state.viewData?.form_data?.form_data?.[\r\n                            `qty_${singleObj.row_id}`\r\n                        ] &&\r\n                        !data?.[`qty_${singleObj.row_id}`]\r\n                    ) {\r\n                        params[`qty_${singleObj.row_id}`] = '';\r\n                    }\r\n                });\r\n            }\r\n        }\r\n\r\n        const onComplete = (resp) => {\r\n            this.setState({\r\n                isFormSubmitting: false,\r\n                error: '',\r\n                visible: false,\r\n            });\r\n            this.tellParentToRefreshList(resp.entry_id);\r\n            this.updateClosureToParent();\r\n        };\r\n        const onError = (error) => {\r\n            // compare statuses here\r\n            this.setState({\r\n                isFormSubmitting: false,\r\n                error: http_utils.decodeErrorToMessage(error),\r\n            });\r\n        };\r\n        if (this.state.editMode) {\r\n            http_utils.performPutCall(\r\n                submitUrl + '/' + this.props.editorItem.id,\r\n                params,\r\n                onComplete,\r\n                onError\r\n            );\r\n        } else {\r\n            http_utils.performPostCall(submitUrl, params, onComplete, onError);\r\n        }\r\n    };\r\n    handleRowDataChange(newRowData) {\r\n        this.setState({ revenuecol: newRowData });\r\n    }\r\n\r\n    getLineItemConfig() {\r\n        if (this.formRef?.current?.getFieldValue) {\r\n            const fieldJson = this.formRef.current.getFieldValue(\r\n                'srvc_type_line_item_config'\r\n            );\r\n            if (fieldJson) {\r\n                return JSON.parse(fieldJson);\r\n            }\r\n        }\r\n        if (\r\n            this.state.viewData?.form_data?.form_data\r\n                ?.srvc_type_line_item_config\r\n        ) {\r\n            return JSON.parse(\r\n                this.state.viewData?.form_data?.form_data\r\n                    ?.srvc_type_line_item_config\r\n            );\r\n        }\r\n        return {};\r\n    }\r\n\r\n    getConfigFrSpPayouts() {\r\n        //here\r\n        if (this.formRef?.current?.getFieldValue) {\r\n            let fieldJson = this.formRef.current.getFieldValue(\r\n                'srvc_type_sp_payouts_config'\r\n            );\r\n            //Set sp payouts initial values\r\n            if (fieldJson == undefined) {\r\n                fieldJson =\r\n                    '{\"1236c271-876d-4352-a044-157acbeee076\":{\"key\":\"1236c271-876d-4352-a044-157acbeee076\",\"label\":\"Payouts\"}}';\r\n            }\r\n            if (fieldJson) {\r\n                return JSON.parse(fieldJson);\r\n            }\r\n        }\r\n        if (\r\n            this.state.viewData?.form_data?.form_data\r\n                ?.srvc_type_sp_payouts_config\r\n        ) {\r\n            return JSON.parse(\r\n                this.state.viewData?.form_data?.form_data\r\n                    ?.srvc_type_sp_payouts_config\r\n            );\r\n        }\r\n        return {};\r\n    }\r\n\r\n    getConfigFrDeduction() {\r\n        let fieldJson = this.formRef?.current?.getFieldValue(\r\n            'srvc_type_deduction_config'\r\n        );\r\n\r\n        if (!fieldJson) {\r\n            fieldJson =\r\n                '{\"deduction_item\":{\"key\":\"deduction_item\",\"label\":\"Deduction\"}}';\r\n        }\r\n\r\n        if (fieldJson) {\r\n            return JSON.parse(fieldJson);\r\n        }\r\n\r\n        if (\r\n            this.state.viewData?.form_data?.form_data\r\n                ?.srvc_type_deduction_config\r\n        ) {\r\n            return JSON.parse(\r\n                this.state.viewData?.form_data?.form_data\r\n                    ?.srvc_type_deduction_config\r\n            );\r\n        }\r\n        return {};\r\n    }\r\n\r\n    getMetaFrLineItemConfiguration() {\r\n        const lineItemConfig = this.getLineItemConfig();\r\n        let who_cannot_download_sp_line_item = [];\r\n        let who_cannot_view_line_items_tab = [];\r\n        if (Object.keys(lineItemConfig).length > 0) {\r\n            who_cannot_download_sp_line_item.push({\r\n                key: 'who_cannot_download_sp_line_item',\r\n                label: (\r\n                    <span style={{ marginTop: 20 }}>\r\n                        {' '}\r\n                        Who cannot download SP line items{' '}\r\n                    </span>\r\n                ),\r\n                widget: 'select',\r\n                widgetProps: {\r\n                    mode: 'multiple',\r\n                    allowClear: true,\r\n                    showSearch: true,\r\n                    optionFilterProp: 'children',\r\n                },\r\n                options: this.state.viewData?.role_list || [],\r\n            });\r\n            who_cannot_view_line_items_tab.push({\r\n                key: 'who_cannot_view_line_items_tab',\r\n                label: <span> Who cannot view line items tab</span>,\r\n                formItemLayout: null,\r\n                widget: 'select',\r\n                widgetProps: {\r\n                    mode: 'multiple',\r\n                    allowClear: true,\r\n                    showSearch: true,\r\n                    optionFilterProp: 'children',\r\n                },\r\n                options: this.state.viewData?.role_list || [],\r\n            });\r\n        }\r\n        const meta = {\r\n            columns: 1,\r\n            formItemLayout: null,\r\n            fields: [\r\n                {\r\n                    label: 'Service type line item config json',\r\n                    key: 'srvc_type_line_item_config',\r\n                    formItemProps: {\r\n                        style: {\r\n                            display: 'none',\r\n                        },\r\n                    },\r\n                },\r\n                {\r\n                    key: 'Line items',\r\n                    render: () => (\r\n                        <>\r\n                            <LineItemManagementModule\r\n                                form={this.formRef}\r\n                                onChange={(newObj) => {\r\n                                    this.formRef.current.setFieldsValue({\r\n                                        srvc_type_line_item_config:\r\n                                            JSON.stringify(newObj),\r\n                                    });\r\n                                    this.refresh();\r\n                                }}\r\n                                currValue={lineItemConfig}\r\n                            />\r\n                        </>\r\n                    ),\r\n                },\r\n                ...who_cannot_download_sp_line_item,\r\n                ...who_cannot_view_line_items_tab,\r\n                {\r\n                    key: 'seperator_6',\r\n                    render: () => (\r\n                        <div className=\"gx-mb-3 gx-bg-grey\">\r\n                            <hr></hr>\r\n                        </div>\r\n                    ),\r\n                },\r\n            ],\r\n        };\r\n        return meta;\r\n    }\r\n\r\n    getInitialValues() {\r\n        return this.state.editMode\r\n            ? this.state.viewData?.form_data?.form_data\r\n            : {};\r\n    }\r\n\r\n    getOrgListBasedOnNature() {\r\n        let vertical_nature =\r\n            this.formRef?.current?.getFieldValue('vertical_nature');\r\n\r\n        let org_list = [...this.state.viewData.org_list];\r\n        let filtered_org_list = [];\r\n        filtered_org_list = org_list.filter((singleOrgSrvcTypeObj) => {\r\n            return singleOrgSrvcTypeObj.srvc_type_nature == vertical_nature;\r\n        });\r\n        return filtered_org_list;\r\n    }\r\n    getMetaFrSearchableCustomFields() {\r\n        let prefix = 'searchable_sp_fields';\r\n        return [\r\n            {\r\n                key: `${prefix}_form_data`,\r\n                label: 'Select searchable SP fields',\r\n                formItemProps: {\r\n                    style: {\r\n                        display: 'none',\r\n                    },\r\n                },\r\n            },\r\n            {\r\n                key: `${prefix}`,\r\n                label: 'Select searchable SP fields',\r\n                widget: 'select',\r\n                widgetProps: {\r\n                    mode: 'multiple',\r\n                },\r\n                options: this.getSearchableCustomFields(),\r\n                onChange: (selectedField, selectedOption) => {\r\n                    let filterConfigValue = [];\r\n                    selectedOption.map((singleElem) => {\r\n                        filterConfigValue.push({\r\n                            search_keyword: singleElem.label,\r\n                            key: singleElem.key,\r\n                        });\r\n                    });\r\n                    this.formRef.current.setFieldsValue({\r\n                        searchable_sp_fields_form_data: filterConfigValue,\r\n                    });\r\n                },\r\n            },\r\n        ];\r\n    }\r\n\r\n    getMeta = () => {\r\n        const initialValues = this.getInitialValues();\r\n        let FieldsInitialValues = initialValues;\r\n        if (\r\n            initialValues?.sp_cust_fields_json == undefined ||\r\n            initialValues?.sp_cust_fields_json == ''\r\n        ) {\r\n            FieldsInitialValues = {};\r\n        }\r\n        var customFields = this.formRef?.current?.getFieldValue(\r\n            'sp_cust_fields_json'\r\n        );\r\n        if (customFields == undefined) {\r\n            customFields = initialValues?.sp_cust_fields_json;\r\n        }\r\n        const isCustFieldDynamicEnabled = this.formRef?.current?.getFieldValue(\r\n            'is_custom_fields_dynamic'\r\n        );\r\n\r\n        const meta = {\r\n            columns: 1,\r\n            formItemLayout: null,\r\n            initialValues: FieldsInitialValues,\r\n            fields: [\r\n                {\r\n                    key: 'vertical_title',\r\n                    label: 'Vertical title(Unique)',\r\n                    required: true,\r\n                    rules: [\r\n                        {\r\n                            max: 50,\r\n                        },\r\n                    ],\r\n                },\r\n                {\r\n                    key: 'vertical_desc',\r\n                    label: 'Vertical description',\r\n                    widget: 'textarea',\r\n                },\r\n                {\r\n                    label: <span>Nature of requests </span>,\r\n                    key: 'vertical_nature',\r\n                    widget: 'radio-group',\r\n                    required: true,\r\n                    onChange: () => {\r\n                        this.formRef.current.setFieldsValue({\r\n                            srvc_type_id: [],\r\n                        });\r\n                        this.refresh();\r\n                    },\r\n                    // widgetProps:{\r\n                    //     defaultValue:'task_based'\r\n                    // },\r\n                    options: getOptionsFrNatureOfServiceType(),\r\n                },\r\n                {\r\n                    key: 'srvc_type_id',\r\n                    label: 'Select organization name',\r\n                    widget: 'select',\r\n                    widgetProps: {\r\n                        mode: 'multiple',\r\n                        optionFilterProp: 'children',\r\n                        onChange: () => this.refresh(),\r\n                    },\r\n                    options: this.getOrgListBasedOnNature(),\r\n                    rules: [\r\n                        {\r\n                            required: true,\r\n                            message: 'Please select organization name',\r\n                        },\r\n                    ],\r\n                    placeholder: 'Please select org name',\r\n                },\r\n                {\r\n                    label: 'Enter custom fields json',\r\n                    key: 'sp_cust_fields_json',\r\n                    widget: 'textarea',\r\n                    placeholder: 'Please paste the JSON here',\r\n                    rules: [\r\n                        {\r\n                            required: true,\r\n                            message: 'Please enter custom fields json',\r\n                        },\r\n                    ],\r\n                    onChange: (event) => {\r\n                        this.setState({\r\n                            render_helper: !this.state.render_helper,\r\n                        });\r\n                    },\r\n                },\r\n                {\r\n                    key: 'link_to_field_creator',\r\n                    render: () => {\r\n                        return (\r\n                            <div>\r\n                                {customFields != '' && (\r\n                                    <div>\r\n                                        <Button\r\n                                            onClick={(e) =>\r\n                                                this.handleShowFieldsPreviewClick(\r\n                                                    customFields\r\n                                                )\r\n                                            }\r\n                                            icon={<ZoomInOutlined />}\r\n                                        >\r\n                                            Form preview\r\n                                        </Button>\r\n                                    </div>\r\n                                )}\r\n                                <Link\r\n                                    to={\r\n                                        '/fields-creator?edit=' +\r\n                                        encodeURIComponent(customFields)\r\n                                    }\r\n                                    target=\"_blank\"\r\n                                >\r\n                                    Open field creator {'-->'}\r\n                                </Link>\r\n                            </div>\r\n                        );\r\n                    },\r\n                },\r\n                {\r\n                    key: `is_custom_fields_dynamic`,\r\n                    label: (\r\n                        <div className=\"gx-mt-2 \">\r\n                            Are custom fields dynamic?\r\n                        </div>\r\n                    ),\r\n                    widget: 'checkbox',\r\n                    onChange: () => this.refresh(),\r\n                },\r\n                ...(isCustFieldDynamicEnabled\r\n                    ? [\r\n                          {\r\n                              key: `sp_cust_fields_dynamic_form_lambda_arn`,\r\n                              label: `Enter custom fields lambda arn`,\r\n                              placeholder: 'Lambda ARN',\r\n                              required: true,\r\n                              widgetProps: {\r\n                                  allowClear: true,\r\n                                  onChange: (value) => {\r\n                                      handleClearSelect(\r\n                                          value,\r\n                                          this.formRef,\r\n                                          'sp_cust_fields_dynamic_form_lambda_arn'\r\n                                      );\r\n                                  },\r\n                              },\r\n                              rules: [\r\n                                  {\r\n                                      validator: validateLambdaArn,\r\n                                      message:\r\n                                          'Please enter a valid Lambda ARN',\r\n                                  },\r\n                              ],\r\n                          },\r\n                      ]\r\n                    : []),\r\n                {\r\n                    label: 'Service provider category field',\r\n                    key: `srvc_prvdr_category_field`,\r\n                    widget: 'select',\r\n                    options: this.getPossibleSpecificFields([\r\n                        {\r\n                            widget: 'select',\r\n                        },\r\n                    ]),\r\n                    widgetProps: {\r\n                        allowClear: true,\r\n                        onChange: (value) => {\r\n                            handleClearSelect(\r\n                                value,\r\n                                this.formRef,\r\n                                'srvc_prvdr_category_field'\r\n                            );\r\n                        },\r\n                    },\r\n                },\r\n                {\r\n                    label: 'Specific location groups (Leave empty for all)',\r\n                    key: `specific_loc_grps`,\r\n                    widget: 'select',\r\n                    options: this.state.viewData?.location_grp_list || [],\r\n                    tooltip:\r\n                        'If you want to restrict location groups that will be tagged to requests for this vertical',\r\n                    widgetProps: {\r\n                        mode: 'multiple',\r\n                        optionFilterProp: 'children',\r\n                        allowClear: true,\r\n                        onChange: (value) => {\r\n                            handleClearSelect(\r\n                                value,\r\n                                this.formRef,\r\n                                'specific_loc_grps'\r\n                            );\r\n                        },\r\n                    },\r\n                },\r\n                // ...this.getMetaFrLineItemConfiguration(),\r\n                ...this.getMetaFrSearchableCustomFields(),\r\n            ],\r\n        };\r\n        return meta;\r\n    };\r\n\r\n    getPricingConfig() {\r\n        if (this.state.editMode) {\r\n            if (this.formRef?.current?.getFieldValue) {\r\n                const fieldJson = this.formRef.current.getFieldValue(\r\n                    'srvc_type_pricing_config_for_line_item'\r\n                );\r\n                if (fieldJson) {\r\n                    return JSON.parse(fieldJson);\r\n                }\r\n            }\r\n            const config_in_form_data =\r\n                this.state.viewData?.form_data?.form_data\r\n                    ?.srvc_type_pricing_config_for_line_item;\r\n            return config_in_form_data ? JSON.parse(config_in_form_data) : {};\r\n        }\r\n        return {};\r\n    }\r\n\r\n    onSaveOfPricingConfig(data, singleLineItemGrpKey) {\r\n        let pricingConfigData = this.getPricingConfig();\r\n        pricingConfigData[singleLineItemGrpKey] = data;\r\n        this.formRef.current.setFieldsValue({\r\n            srvc_type_pricing_config_for_line_item:\r\n                JSON.stringify(pricingConfigData),\r\n        });\r\n    }\r\n\r\n    getPricingConfigFrManday() {\r\n        if (this.state.editMode) {\r\n            if (this.formRef?.current?.getFieldValue) {\r\n                const fieldJson = this.formRef.current.getFieldValue(\r\n                    'srvc_type_pricing_config_for_manday'\r\n                );\r\n                if (fieldJson) {\r\n                    return JSON.parse(fieldJson);\r\n                }\r\n            }\r\n            const config_in_form_data =\r\n                this.state.viewData?.form_data?.form_data\r\n                    ?.srvc_type_pricing_config_for_manday;\r\n            return config_in_form_data ? JSON.parse(config_in_form_data) : {};\r\n        }\r\n        return {};\r\n    }\r\n\r\n    onSaveOfPricingConfigFrManday(data) {\r\n        let pricingConfigDataFrManday = this.getPricingConfigFrManday();\r\n        pricingConfigDataFrManday['srvc_type_pricing_config_for_manday'] = data;\r\n        this.formRef.current.setFieldsValue({\r\n            srvc_type_pricing_config_for_manday: JSON.stringify(\r\n                pricingConfigDataFrManday\r\n            ),\r\n        });\r\n    }\r\n\r\n    getSelectedOrgWithSrvcTypeIds() {\r\n        let selectedOrgs = [];\r\n        let srvcTypeIds =\r\n            this.formRef?.current?.getFieldValue('srvc_type_id') ||\r\n            this.getInitialValues()?.srvc_type_id;\r\n        let org_list = this.state.viewData.org_list;\r\n        if (Array.isArray(srvcTypeIds) && srvcTypeIds.length > 0) {\r\n            srvcTypeIds.forEach((singlesrvcTypeId) => {\r\n                let selectedOrgsList = org_list.filter(\r\n                    (singleOrg) => singleOrg.value == singlesrvcTypeId\r\n                )?.[0];\r\n                selectedOrgs.push(selectedOrgsList);\r\n            });\r\n        }\r\n        return selectedOrgs;\r\n    }\r\n\r\n    getPricingMasterConfigMeta(initialValues) {\r\n        let lineItemConfigData = this.getLineItemConfig();\r\n        let pricingConfigData = this.getPricingConfig();\r\n        let pricingConfigDataFrManday = this.getPricingConfigFrManday();\r\n        return {\r\n            fields: [\r\n                {\r\n                    label: 'Service provider line item pricing config json',\r\n                    key: 'srvc_type_pricing_config_for_line_item',\r\n                    formItemProps: {\r\n                        style: {\r\n                            display: 'none',\r\n                        },\r\n                    },\r\n                },\r\n                {\r\n                    label: 'Service type line item pricing config json for manday',\r\n                    key: 'srvc_type_pricing_config_for_manday',\r\n                    formItemProps: {\r\n                        style: {\r\n                            display: 'none',\r\n                        },\r\n                    },\r\n                },\r\n                {\r\n                    key: `sp_srvc_type_pricing_master_config`,\r\n                    render: () => {\r\n                        return (\r\n                            <Tabs defaultActiveKey=\"line_item\">\r\n                                {Object.keys(lineItemConfigData).length > 0 && (\r\n                                    <Tabs.TabPane\r\n                                        tab=\"Line Items\"\r\n                                        key=\"line_item\"\r\n                                    >\r\n                                        <Tabs defaultActiveKey=\"line_item\">\r\n                                            {Object.keys(\r\n                                                lineItemConfigData\r\n                                            ).map(\r\n                                                (\r\n                                                    singleLineItemGrpKey,\r\n                                                    index\r\n                                                ) => (\r\n                                                    <Tabs.TabPane\r\n                                                        key={index}\r\n                                                        tab={\r\n                                                            lineItemConfigData?.[\r\n                                                                singleLineItemGrpKey\r\n                                                            ]?.label\r\n                                                        }\r\n                                                    >\r\n                                                        <PricingMaster\r\n                                                            pricingInitialValue={\r\n                                                                pricingConfigData[\r\n                                                                    singleLineItemGrpKey\r\n                                                                ]\r\n                                                            }\r\n                                                            fullFormInitialValue={\r\n                                                                initialValues\r\n                                                            }\r\n                                                            form={this.formRef}\r\n                                                            config_data={\r\n                                                                lineItemConfigData[\r\n                                                                    singleLineItemGrpKey\r\n                                                                ]\r\n                                                            }\r\n                                                            onChange={(\r\n                                                                data\r\n                                                            ) => {\r\n                                                                // WE GET pricing for single line item group here\r\n                                                                this.onSaveOfPricingConfig(\r\n                                                                    data,\r\n                                                                    singleLineItemGrpKey\r\n                                                                );\r\n                                                            }}\r\n                                                            editMode={\r\n                                                                this.state\r\n                                                                    .editMode\r\n                                                            }\r\n                                                            org_list={this.getSelectedOrgWithSrvcTypeIds()}\r\n                                                        />\r\n                                                    </Tabs.TabPane>\r\n                                                )\r\n                                            )}\r\n                                        </Tabs>\r\n                                    </Tabs.TabPane>\r\n                                )}\r\n                                <Tabs.TabPane tab=\"Manday\">\r\n                                    <PricingMaster\r\n                                        isManday\r\n                                        pricingInitialValue={\r\n                                            pricingConfigDataFrManday?.srvc_type_pricing_config_for_manday\r\n                                        }\r\n                                        fullFormInitialValue={initialValues}\r\n                                        form={this.formRef}\r\n                                        config_data={\r\n                                            this.state.viewData\r\n                                                ?.location_grp_list\r\n                                        }\r\n                                        onChange={(data) => {\r\n                                            // WE GET pricing for single line item group here\r\n                                            this.onSaveOfPricingConfigFrManday(\r\n                                                data\r\n                                            );\r\n                                        }}\r\n                                        editMode={this.state.editMode}\r\n                                        org_list={this.getSelectedOrgWithSrvcTypeIds()}\r\n                                    />\r\n                                </Tabs.TabPane>\r\n                            </Tabs>\r\n                        );\r\n                    },\r\n                },\r\n            ],\r\n        };\r\n    }\r\n\r\n    getPossibleSpecificFields(essentialFields = []) {\r\n        let labelValuePairData = [];\r\n        let customFields = this.formRef?.current?.getFieldValue(\r\n            'sp_cust_fields_json'\r\n        );\r\n        if (customFields && customFields != '') {\r\n            customFields = decodeFieldsMetaFrmJson(\r\n                customFields,\r\n                undefined,\r\n                true\r\n            );\r\n            customFields.forEach((singleField) => {\r\n                if (\r\n                    essentialFields.length == 0 ||\r\n                    this.checkIfPresentInEssentialField(\r\n                        singleField,\r\n                        essentialFields\r\n                    )\r\n                ) {\r\n                    labelValuePairData.push({\r\n                        label: singleField.label,\r\n                        value: singleField.key,\r\n                        key: singleField.key,\r\n                    });\r\n                }\r\n            });\r\n        }\r\n        return labelValuePairData;\r\n    }\r\n\r\n    checkIfPresentInEssentialField = (singleField, essentialFields) => {\r\n        for (let essentialField of essentialFields) {\r\n            if (\r\n                singleField.widget == essentialField.widget &&\r\n                singleField.widgetProps.mode != 'multiple'\r\n            ) {\r\n                return true;\r\n            }\r\n        }\r\n    };\r\n\r\n    getSearchableCustomFields() {\r\n        let labelValuePairData = [];\r\n        let customFields = this.formRef?.current?.getFieldValue(\r\n            'sp_cust_fields_json'\r\n        );\r\n        if (customFields && customFields != '') {\r\n            customFields = decodeFieldsMetaFrmJson(customFields);\r\n            customFields.forEach((singleField) => {\r\n                if (\r\n                    singleField?.cust_component != 'linebreak' &&\r\n                    singleField?.type != 'Barcode_scanner' &&\r\n                    singleField?.cust_component != 'legend'\r\n                ) {\r\n                    if (\r\n                        singleField?.widget != 'select' &&\r\n                        singleField?.widget != 'radio-group' &&\r\n                        singleField?.widget != 'checkbox-group' &&\r\n                        singleField?.cust_widget != 'Rating' &&\r\n                        singleField.widget != 'date-picker'\r\n                    ) {\r\n                        labelValuePairData.push({\r\n                            value: singleField.key,\r\n                            label: singleField.label,\r\n                            require: singleField?.required,\r\n                        });\r\n                    }\r\n                }\r\n            });\r\n        }\r\n        return labelValuePairData;\r\n    }\r\n\r\n    getPossibleSpecificFilesFields() {\r\n        let customFileFields = this.formRef?.current?.getFieldValue(\r\n            'sp_cust_fields_json'\r\n        );\r\n        if (customFileFields && customFileFields != '') {\r\n            customFileFields = decodeFileSectionsFrmJson(customFileFields);\r\n        }\r\n        return customFileFields;\r\n    }\r\n\r\n    //Create a new component for Billing to be done later\r\n    getBillingformMeta = (initialValues) => {\r\n        const meta = {\r\n            formItemLayout: null,\r\n            initialValues: initialValues,\r\n            fields: [\r\n                {\r\n                    label: 'Enable Billing',\r\n                    key: 'srvc_type_enable_billing',\r\n                    widget: 'checkbox',\r\n                },\r\n                {\r\n                    key: 'srvc_type_billing_type',\r\n                    label: 'Billing Type',\r\n                    widget: 'select',\r\n                    widgetProps: {\r\n                        mode: 'single',\r\n                    },\r\n                    options: BILLING_TYPE,\r\n                },\r\n                {\r\n                    key: 'srvc_type_categorize_specific_fields_for_billing',\r\n                    label: 'Categorize specific fields for billing section',\r\n                    widget: 'select',\r\n                    widgetProps: {\r\n                        mode: 'multiple',\r\n                        showSearch: true,\r\n                        optionFilterProp: 'children',\r\n                    },\r\n                    options: this.getPossibleSpecificFields() || [],\r\n                },\r\n                {\r\n                    key: 'srvc_type_who_can_lock_srvc_req_for_billing',\r\n                    label: 'Who can lock service request for billing',\r\n                    widget: 'select',\r\n                    widgetProps: {\r\n                        mode: 'multiple',\r\n                        showSearch: true,\r\n                        optionFilterProp: 'children',\r\n                    },\r\n                    options: this.state.viewData?.role_list || [],\r\n                },\r\n                {\r\n                    key: 'srvc_type_who_can_sync_srvc_req_prc',\r\n                    label: 'Who can sync service request price',\r\n                    widget: 'select',\r\n                    widgetProps: {\r\n                        mode: 'multiple',\r\n                        showSearch: true,\r\n                        optionFilterProp: 'children',\r\n                    },\r\n                    options: this.state.viewData?.role_list || [],\r\n                },\r\n                {\r\n                    key: 'srvc_type_who_can_send_req_for_billing',\r\n                    label: 'Who can send a Request for billing',\r\n                    widget: 'select',\r\n                    widgetProps: {\r\n                        mode: 'multiple',\r\n                        showSearch: true,\r\n                        optionFilterProp: 'children',\r\n                    },\r\n                    options: this.state.viewData?.role_list || [],\r\n                },\r\n                {\r\n                    key: 'srvc_type_who_will_get_notified_for_billing',\r\n                    label: 'Who will get notified for billing',\r\n                    widget: 'select',\r\n                    widgetProps: {\r\n                        mode: 'multiple',\r\n                        showSearch: true,\r\n                        optionFilterProp: 'children',\r\n                    },\r\n                    options: this.state.viewData?.role_list || [],\r\n                },\r\n                {\r\n                    key: 'hide_sp_billing_tab_from_specific_roles',\r\n                    label: 'Who will not be able to see the billing tab',\r\n                    widget: 'select',\r\n                    widgetProps: {\r\n                        mode: 'multiple',\r\n                        showSearch: true,\r\n                        optionFilterProp: 'children',\r\n                    },\r\n                    options: this.state.viewData?.role_list || [],\r\n                },\r\n            ],\r\n        };\r\n        return meta;\r\n    };\r\n\r\n    getBillingDiscountConfig() {\r\n        if (this.state.editMode) {\r\n            if (this.formRef?.current?.getFieldValue) {\r\n                const fieldJson = this.formRef.current.getFieldValue(\r\n                    'srvc_type_billing_discounting_rule_config'\r\n                );\r\n                if (fieldJson) {\r\n                    return JSON.parse(fieldJson);\r\n                }\r\n            }\r\n            let initialFieldJson =\r\n                this.state.viewData?.form_data\r\n                    ?.srvc_type_billing_discounting_rule_config;\r\n            if (initialFieldJson) {\r\n                return JSON.parse(initialFieldJson);\r\n            }\r\n        }\r\n        return {};\r\n    }\r\n\r\n    getDiscountingBillingformMeta = (initialValues) => {\r\n        const meta = {\r\n            formItemLayout: null,\r\n            initialValues: initialValues,\r\n            fields: [\r\n                {\r\n                    label: 'Enable Discounting',\r\n                    key: 'srvc_type_enable_billing_discounting',\r\n                    widget: 'checkbox',\r\n                },\r\n                {\r\n                    label: (\r\n                        <span>\r\n                            {' '}\r\n                            When discount approval status changes, notify{' '}\r\n                        </span>\r\n                    ),\r\n                    key: `srvc_type_discount_approval_status_changes_notify`,\r\n                    widget: 'select',\r\n                    options: this.state.viewData?.role_list || [],\r\n                    widgetProps: {\r\n                        allowClear: true,\r\n                        mode: 'multiple',\r\n                    },\r\n                },\r\n                {\r\n                    key: `srvc_type_discounting_billing_master`,\r\n                    render: () => {\r\n                        return (\r\n                            <BillingDiscountingConfigModule\r\n                                onChange={(newObj) => {\r\n                                    this.formRef.current.setFieldsValue({\r\n                                        srvc_type_billing_discounting_rule_config:\r\n                                            JSON.stringify(newObj),\r\n                                    });\r\n                                    this.refresh();\r\n                                }}\r\n                                currValue={this.getBillingDiscountConfig()}\r\n                                authorities_list={\r\n                                    this.state.viewData?.role_list || []\r\n                                }\r\n                            />\r\n                        );\r\n                    },\r\n                },\r\n            ],\r\n        };\r\n        return meta;\r\n    };\r\n\r\n    getAdditionalBillingformMeta = (initialValues) => {\r\n        const meta = {\r\n            formItemLayout: null,\r\n            initialValues: initialValues,\r\n            fields: [\r\n                {\r\n                    label: 'Enable Additional Billing',\r\n                    key: 'srvc_type_enable_additional_billing',\r\n                    widget: 'checkbox',\r\n                },\r\n                {\r\n                    key: `srvc_type_additional_billing_master`,\r\n                    render: () => {\r\n                        return (\r\n                            <>\r\n                                <LineItemManagementModule\r\n                                    onChange={(newObj) => {\r\n                                        this.formRef.current.setFieldsValue({\r\n                                            srvc_type_additional_billing_config:\r\n                                                JSON.stringify(newObj),\r\n                                        });\r\n                                        this.refresh();\r\n                                    }}\r\n                                    currValue={this.getAdditionalBillingJson()}\r\n                                    hideBtn\r\n                                />\r\n                            </>\r\n                        );\r\n                    },\r\n                },\r\n            ],\r\n        };\r\n        return meta;\r\n    };\r\n\r\n    getAdditionalBillingJson() {\r\n        if (this.state.editMode) {\r\n            if (this.formRef?.current?.getFieldValue) {\r\n                const fieldJson = this.formRef.current.getFieldValue(\r\n                    'srvc_type_additional_billing_config'\r\n                );\r\n                if (fieldJson) {\r\n                    return JSON.parse(fieldJson);\r\n                }\r\n            }\r\n            return {\r\n                additional_line_item: {\r\n                    key: 'additional_line_item',\r\n                    label: 'Additional line items',\r\n                },\r\n            };\r\n        }\r\n        return {};\r\n    }\r\n    getDeductionConfigMeta() {\r\n        const isDeductionEnable = this.formRef?.current?.getFieldValue(\r\n            'is_enable_deduction'\r\n        );\r\n\r\n        const deductionConfig = this.getConfigFrDeduction();\r\n\r\n        return {\r\n            formItemLayout: null,\r\n            fields: [\r\n                {\r\n                    key: 'is_enable_deduction',\r\n                    label: 'Enable Deduction',\r\n                    widget: 'checkbox',\r\n                    onChange: (e) => {\r\n                        this.refresh();\r\n                        if (\r\n                            e.target.checked == true &&\r\n                            this.formRef.current.getFieldValue(\r\n                                'srvc_type_deduction_config'\r\n                            ) == undefined\r\n                        ) {\r\n                            this.formRef.current.setFieldsValue({\r\n                                srvc_type_deduction_config: JSON.stringify({\r\n                                    deduction_item: {\r\n                                        key: 'deduction_item',\r\n                                        label: 'Deduction',\r\n                                        total_field_label: '',\r\n                                    },\r\n                                }),\r\n                            });\r\n                        }\r\n                    },\r\n                },\r\n                ...(isDeductionEnable\r\n                    ? [\r\n                          {\r\n                              label: 'Service type deduction config json',\r\n                              key: 'srvc_type_deduction_config',\r\n                              formItemProps: {\r\n                                  style: {\r\n                                      display: 'none',\r\n                                  },\r\n                              },\r\n                          },\r\n                          {\r\n                              key: 'Deduction',\r\n                              render: () => (\r\n                                  <>\r\n                                      <LineItemManagementModule\r\n                                          form={this.formRef}\r\n                                          onChange={(newObj) => {\r\n                                              this.formRef.current.setFieldsValue(\r\n                                                  {\r\n                                                      srvc_type_deduction_config:\r\n                                                          JSON.stringify(\r\n                                                              newObj\r\n                                                          ),\r\n                                                  }\r\n                                              );\r\n                                              this.refresh();\r\n                                          }}\r\n                                          currValue={deductionConfig}\r\n                                          isDeduction\r\n                                          hideBtn\r\n                                      />\r\n                                  </>\r\n                              ),\r\n                          },\r\n                      ]\r\n                    : []),\r\n            ],\r\n        };\r\n    }\r\n\r\n    getFieldsWiseNotificationConfigMeta(initialValues) {\r\n        return {\r\n            fields: [\r\n                {\r\n                    key: `sp_srvc_type_fields_wise_notification_config`,\r\n                    render: () => {\r\n                        return (\r\n                            <Tabs defaultActiveKey=\"field_wise\">\r\n                                <Tabs.TabPane tab=\"Field Wise\" key=\"field_wise\">\r\n                                    <FormBuilder\r\n                                        form={this.formRef}\r\n                                        meta={this.getFieldWiseAuthorityNotification(\r\n                                            initialValues\r\n                                        )}\r\n                                    />\r\n                                </Tabs.TabPane>\r\n                            </Tabs>\r\n                        );\r\n                    },\r\n                },\r\n            ],\r\n        };\r\n    }\r\n\r\n    getFieldWiseAuthorityNotification(initialValues) {\r\n        return {\r\n            fields: [\r\n                {\r\n                    key: `sp_srvc_type_field_wise_authority_notification_config`,\r\n                    render: () => {\r\n                        return (\r\n                            <Tabs\r\n                                defaultActiveKey=\"authority\"\r\n                                className=\"gx-mb-2\"\r\n                            >\r\n                                <Tabs.TabPane tab=\"Authority\" key=\"authority\">\r\n                                    <FormBuilder\r\n                                        form={this.formRef}\r\n                                        meta={this.getSpecificFieldsWiseNotificationFieldsMeta(\r\n                                            initialValues\r\n                                        )}\r\n                                    />\r\n                                </Tabs.TabPane>\r\n                            </Tabs>\r\n                        );\r\n                    },\r\n                },\r\n            ],\r\n        };\r\n    }\r\n\r\n    getAuthoritiesList = () => {\r\n        let authoritiesList = [];\r\n        let authority_id = this.formRef?.current?.getFieldValue('authority_id');\r\n        let roleList = this.state.viewData?.role_list;\r\n        if (authority_id && authority_id.length > 0) {\r\n            authority_id.forEach((singleAuthority) => {\r\n                let filteredAuthorityList = roleList.filter(\r\n                    (singleRole) => singleRole.value == singleAuthority\r\n                )?.[0];\r\n                if (filteredAuthorityList) {\r\n                    authoritiesList.push(filteredAuthorityList);\r\n                }\r\n            });\r\n        }\r\n        return authoritiesList;\r\n    };\r\n\r\n    getSpecificFieldsWiseNotificationFieldsMeta = (initialValues) => {\r\n        const meta = {\r\n            formItemLayout: null,\r\n            initialValues: initialValues,\r\n            fields: [\r\n                {\r\n                    label: 'Service type specific fields wise notification',\r\n                    key: 'srvc_type_specific_fields_wise_notification',\r\n                    formItemProps: {\r\n                        style: {\r\n                            display: 'none',\r\n                        },\r\n                    },\r\n                },\r\n                {\r\n                    key: `sp_srvc_type_fields_wise_notification`,\r\n                    render: () => {\r\n                        return (\r\n                            <SpecificFieldsWiseNotificationModule\r\n                                srvc_type_id={this.formRef?.current?.getFieldValue(\r\n                                    'srvc_type_id'\r\n                                )}\r\n                                authorities_list={this.getAuthoritiesList()}\r\n                                specific_fields={\r\n                                    this.state.viewData\r\n                                        ?.brand_wise_specific_fields\r\n                                }\r\n                                initialValue={this.getSpecificFieldsNotificationJson()}\r\n                                onChange={(newObj) => {\r\n                                    // console.log(\"newObj\",newObj);\r\n                                    this.formRef.current.setFieldsValue({\r\n                                        srvc_type_specific_fields_wise_notification:\r\n                                            JSON.stringify(newObj),\r\n                                    });\r\n                                    this.refresh();\r\n                                }}\r\n                                isSrvcPrvdrTab\r\n                                sp_specific_fields={this.formRef?.current?.getFieldValue(\r\n                                    'sp_cust_fields_json'\r\n                                )}\r\n                            />\r\n                        );\r\n                    },\r\n                },\r\n            ],\r\n        };\r\n        return meta;\r\n    };\r\n\r\n    getSpecificFieldsNotificationJson() {\r\n        if (this.state.editMode) {\r\n            if (this.formRef?.current?.getFieldValue) {\r\n                const fieldJson = this.formRef.current.getFieldValue(\r\n                    'srvc_type_specific_fields_wise_notification'\r\n                );\r\n                if (fieldJson) {\r\n                    return JSON.parse(fieldJson);\r\n                }\r\n            }\r\n            let initialFieldJson =\r\n                this.state.viewData?.form_data\r\n                    ?.srvc_type_specific_fields_wise_notification;\r\n            if (initialFieldJson) {\r\n                return JSON.parse(initialFieldJson);\r\n            }\r\n        }\r\n        return {};\r\n    }\r\n\r\n    getBillingMasterConfigMeta(initialValues) {\r\n        return {\r\n            fields: [\r\n                {\r\n                    //All data of Billing section will be saved under this key and this is to be done later\r\n                    label: 'Service type billing config json for billing',\r\n                    key: 'srvc_type_billing_config_for_billing',\r\n                    formItemProps: {\r\n                        style: {\r\n                            display: 'none',\r\n                        },\r\n                    },\r\n                },\r\n                {\r\n                    label: 'Service type discounting config json for billing',\r\n                    key: 'srvc_type_billing_discounting_rule_config',\r\n                    formItemProps: {\r\n                        style: {\r\n                            display: 'none',\r\n                        },\r\n                    },\r\n                },\r\n                {\r\n                    label: 'Service type additional config json for billing',\r\n                    key: 'srvc_type_additional_billing_config',\r\n                    formItemProps: {\r\n                        style: {\r\n                            display: 'none',\r\n                        },\r\n                    },\r\n                },\r\n                {\r\n                    key: `srvc_type_billing_master_config`,\r\n                    render: () => {\r\n                        return (\r\n                            <Tabs\r\n                                defaultActiveKey=\"billing\"\r\n                                className=\"gx-mb-2\"\r\n                            >\r\n                                <Tabs.TabPane tab=\"Billing\" key=\"billing\">\r\n                                    {/* Create a new component for Billing to be done later */}\r\n                                    <FormBuilder\r\n                                        form={this.formRef}\r\n                                        meta={this.getBillingformMeta(\r\n                                            initialValues\r\n                                        )}\r\n                                    />\r\n                                </Tabs.TabPane>\r\n\r\n                                <Tabs.TabPane\r\n                                    tab=\"Discounting\"\r\n                                    key=\"discounting\"\r\n                                >\r\n                                    <FormBuilder\r\n                                        form={this.formRef}\r\n                                        meta={this.getDiscountingBillingformMeta(\r\n                                            initialValues\r\n                                        )}\r\n                                    />\r\n                                </Tabs.TabPane>\r\n\r\n                                <Tabs.TabPane tab=\"Additional\" key=\"additional\">\r\n                                    <FormBuilder\r\n                                        form={this.formRef}\r\n                                        meta={this.getAdditionalBillingformMeta(\r\n                                            initialValues\r\n                                        )}\r\n                                    />\r\n                                </Tabs.TabPane>\r\n                                <Tabs.TabPane\r\n                                    tab=\"Deduction\"\r\n                                    key=\"deduction_tab\"\r\n                                >\r\n                                    <FormBuilder\r\n                                        form={this.formRef}\r\n                                        meta={this.getDeductionConfigMeta(\r\n                                            initialValues\r\n                                        )}\r\n                                    />\r\n                                </Tabs.TabPane>\r\n                            </Tabs>\r\n                        );\r\n                    },\r\n                },\r\n            ],\r\n        };\r\n    }\r\n    getDeploymentConfigMeta = (initialValues) => {\r\n        if (this.state.editMode && !this.state.editModeForceRefreshDone) {\r\n            // refreshing state to get form ref\r\n            this.setState({ editModeForceRefreshDone: true });\r\n            return;\r\n        }\r\n        const startTimeFrEndTime = this.formRef.current?.getFieldValue(\r\n            'deployment_time_slot_lower_limit'\r\n        );\r\n        let role_vs_subtask_fields = [];\r\n        let possible_roles = this.state.viewData?.role_list || [];\r\n        var deployment_roles =\r\n            this.formRef?.current?.getFieldValue('deployment_possible_roles') ||\r\n            this.state.viewData?.form_data?.deployment_possible_roles;\r\n        if (deployment_roles) {\r\n            deployment_roles.forEach((singleDeploymentRoleId) => {\r\n                let role_details =\r\n                    possible_roles.filter(\r\n                        (singleRole) =>\r\n                            singleRole.value == singleDeploymentRoleId\r\n                    )[0] || {};\r\n\r\n                role_vs_subtask_fields.push({\r\n                    key: `sbtsk_fr_${singleDeploymentRoleId}`,\r\n                    label: `Select subtask for ${role_details.label}`,\r\n                    required: true,\r\n                    widget: 'select',\r\n                    options:\r\n                        this.state.viewData?.applicable_subtasks_list || [],\r\n                });\r\n            });\r\n        }\r\n        if (\r\n            initialValues &&\r\n            initialValues['deployment_time_slot_lower_limit'] == undefined\r\n        ) {\r\n            initialValues['deployment_time_slot_lower_limit'] = startOfDay;\r\n            initialValues['deployment_time_slot_upper_limit'] = endOfDay;\r\n        }\r\n        const meta = {\r\n            columns: 1,\r\n            formItemLayout: null,\r\n            initialValues: initialValues,\r\n            fields: [\r\n                {\r\n                    key: 'sp_deployment_who_can_edit',\r\n                    label: 'Who can edit deployment ?',\r\n                    widget: 'select',\r\n                    widgetProps: {\r\n                        mode: 'multiple',\r\n                        allowClear: true,\r\n                        showSearch: true,\r\n                    },\r\n                    options: this.state.viewData?.role_list,\r\n                },\r\n                {\r\n                    key: 'deployment_time_slot_lower_limit',\r\n                    label: (\r\n                        <span>\r\n                            <i className=\"icon icon-timepicker gx-mr-2\"></i>\r\n                            Start Time Slot (Lower Limit)\r\n                        </span>\r\n                    ),\r\n                    widget: TimePickerWidget,\r\n                    widgetProps: {\r\n                        beginLimit: startOfDay,\r\n                        endLimit: endOfDay,\r\n                        step: 15,\r\n                        onChange: (e) => {\r\n                            this.refresh();\r\n                        },\r\n                    },\r\n                },\r\n                {\r\n                    key: 'deployment_time_slot_upper_limit',\r\n                    label: (\r\n                        <span>\r\n                            <i className=\"icon icon-timepicker gx-mr-2\"></i>End\r\n                            Time Slot (Upper Limit)\r\n                        </span>\r\n                    ),\r\n                    widget: TimePickerWidget,\r\n                    widgetProps: {\r\n                        beginLimit: startTimeFrEndTime || startOfDay,\r\n                        endLimit: endOfDay,\r\n                        step: 15,\r\n                    },\r\n                },\r\n                {\r\n                    key: `who_cannot_download_site_level_attendance`,\r\n                    label: 'Who cannot download site level attendance ',\r\n                    widget: 'select',\r\n                    placeholder: 'Select roles',\r\n                    onChange: () => this.refresh(),\r\n                    widgetProps: {\r\n                        mode: 'multiple',\r\n                        allowClear: true,\r\n                        showSearch: true,\r\n                        optionFilterProp: 'children',\r\n                    },\r\n                    options: this.state.viewData?.role_list || [],\r\n                },\r\n                {\r\n                    key: `deployment_possible_roles`,\r\n                    label: 'Please select roles to deploy',\r\n                    widget: 'select',\r\n                    placeholder: 'Select roles',\r\n                    onChange: () => this.refresh(),\r\n                    widgetProps: {\r\n                        mode: 'multiple',\r\n                        allowClear: true,\r\n                        showSearch: true,\r\n                        optionFilterProp: 'children',\r\n                    },\r\n                    options: this.state.viewData?.role_list || [],\r\n                },\r\n                {\r\n                    key: 'show_role_indicator_on_calendar',\r\n                    label: 'Show role indicator on calendar',\r\n                    widget: 'checkbox',\r\n                    onChange: () => this.refresh(),\r\n                },\r\n                {\r\n                    key: 'show_deployed_role_on_calendar',\r\n                    widget: 'radio-group',\r\n                    options: [\r\n                        {\r\n                            value: 'show_deployed_role',\r\n                            label: 'Show deployed role',\r\n                        },\r\n                        { value: 'show_role_code', label: 'Show role code' },\r\n                    ],\r\n                    initialValue: 'show_deployed_role',\r\n                    formItemProps: {\r\n                        style: {\r\n                            display: this.formRef?.current?.getFieldValue(\r\n                                'show_role_indicator_on_calendar'\r\n                            )\r\n                                ? 'block'\r\n                                : 'none',\r\n                        },\r\n                    },\r\n                },\r\n                ...role_vs_subtask_fields,\r\n            ],\r\n        };\r\n        return meta;\r\n    };\r\n\r\n    getDailyUpdatesMeta = (initialValues) => {\r\n        let possible_roles = this.state.viewData?.role_list || [];\r\n        var daily_update_issue_fields = this.formRef?.current?.getFieldValue(\r\n            'daily_update_issue_form_fields'\r\n        );\r\n        if (daily_update_issue_fields == undefined) {\r\n            daily_update_issue_fields =\r\n                initialValues?.daily_update_issue_form_fields;\r\n        }\r\n        var sp_daily_update_form_fields = this.formRef?.current?.getFieldValue(\r\n            'sp_daily_update_form_fields'\r\n        );\r\n        if (sp_daily_update_form_fields == undefined) {\r\n            sp_daily_update_form_fields =\r\n                initialValues?.sp_daily_update_form_fields;\r\n        }\r\n        const isTrackingAllLineItemWiseProgress =\r\n            this.formRef?.current?.getFieldValue(\r\n                'daily_update_track_line_item_progress'\r\n            ) ||\r\n            this.state.viewData?.form_data\r\n                ?.daily_update_track_line_item_progress;\r\n        const meta = {\r\n            columns: 1,\r\n            formItemLayout: null,\r\n            initialValues: initialValues,\r\n            fields: [\r\n                {\r\n                    render: () => (\r\n                        <h3 className=\"gx-mb-2\">\r\n                            Configure daily updates form\r\n                        </h3>\r\n                    ),\r\n                },\r\n                {\r\n                    key: 'daily_update_who_can_edit',\r\n                    label: 'Who can edit daily updates ?',\r\n                    widget: 'select',\r\n                    widgetProps: {\r\n                        mode: 'multiple',\r\n                    },\r\n                    options: possible_roles,\r\n                },\r\n                {\r\n                    key: 'who_cannot_download_sp_daily_updates',\r\n                    label: 'Who cannot download daily updates',\r\n                    widget: 'select',\r\n                    widgetProps: {\r\n                        mode: 'multiple',\r\n                        allowClear: true,\r\n                        showSearch: true,\r\n                        optionFilterProp: 'children',\r\n                    },\r\n                    options: possible_roles,\r\n                },\r\n                {\r\n                    label: 'Daily update form fields',\r\n                    key: 'sp_daily_update_form_fields',\r\n                    widget: 'textarea',\r\n                    placeholder: 'Please paste the JSON here',\r\n                    onChange: (event) => {\r\n                        this.forceUpdate();\r\n                    },\r\n                },\r\n                {\r\n                    render: () => {\r\n                        return (\r\n                            <div>\r\n                                {sp_daily_update_form_fields != '' && (\r\n                                    <div>\r\n                                        <Button\r\n                                            onClick={(e) =>\r\n                                                this.handleShowFieldsPreviewClick(\r\n                                                    sp_daily_update_form_fields\r\n                                                )\r\n                                            }\r\n                                            icon={<ZoomInOutlined />}\r\n                                        >\r\n                                            Form preview\r\n                                        </Button>\r\n                                    </div>\r\n                                )}\r\n                                <Link\r\n                                    to={\r\n                                        '/fields-creator?edit=' +\r\n                                        encodeURIComponent(\r\n                                            sp_daily_update_form_fields\r\n                                        )\r\n                                    }\r\n                                    target=\"_blank\"\r\n                                >\r\n                                    Open field creator {'-->'}\r\n                                </Link>\r\n                            </div>\r\n                        );\r\n                    },\r\n                },\r\n                {\r\n                    render: () => <hr></hr>,\r\n                },\r\n                {\r\n                    key: `daily_update_will_have_issues`,\r\n                    label: 'Will have issues?',\r\n                    widget: 'radio-group',\r\n                    options: ['Yes', 'No'],\r\n                },\r\n                {\r\n                    label: 'Issue form fields',\r\n                    key: 'daily_update_issue_form_fields',\r\n                    widget: 'textarea',\r\n                    placeholder: 'Please paste the JSON here',\r\n                    onChange: (event) => {\r\n                        this.forceUpdate();\r\n                    },\r\n                },\r\n                {\r\n                    render: () => {\r\n                        return (\r\n                            <div>\r\n                                {daily_update_issue_fields != '' && (\r\n                                    <div>\r\n                                        <Button\r\n                                            onClick={(e) =>\r\n                                                this.handleShowFieldsPreviewClick(\r\n                                                    daily_update_issue_fields\r\n                                                )\r\n                                            }\r\n                                            icon={<ZoomInOutlined />}\r\n                                        >\r\n                                            Form preview\r\n                                        </Button>\r\n                                    </div>\r\n                                )}\r\n                                <Link\r\n                                    to={\r\n                                        '/fields-creator?edit=' +\r\n                                        encodeURIComponent(\r\n                                            daily_update_issue_fields\r\n                                        )\r\n                                    }\r\n                                    target=\"_blank\"\r\n                                >\r\n                                    Open field creator {'-->'}\r\n                                </Link>\r\n                            </div>\r\n                        );\r\n                    },\r\n                },\r\n                {\r\n                    render: () => <hr></hr>,\r\n                },\r\n                {\r\n                    key: 'daily_progress_update_mode',\r\n                    label: 'Daily progress update mode',\r\n                    widget: 'select',\r\n                    options: DailyUpdateModes,\r\n                },\r\n                {\r\n                    key: 'daily_update_track_line_item_progress',\r\n                    label: 'Track line item wise progress & assignment?',\r\n                    tooltip:\r\n                        'Updater will be able to update line item wise progress and cummulative progress will be calculated automatically',\r\n                    widget: 'radio-group',\r\n                    options: ['Yes', 'No'],\r\n                    onChange: () => {\r\n                        this.refresh();\r\n                    },\r\n                },\r\n                ...(isTrackingAllLineItemWiseProgress === 'Yes'\r\n                    ? [\r\n                          {\r\n                              key: 'show_line_item_by_selection',\r\n                              widget: (props) => (\r\n                                  <InlineCheckbox\r\n                                      label=\"Show line items by selection\"\r\n                                      tooltip=\"If enabled, line item progress will be shown only for the selected line items.\"\r\n                                      value={props.value}\r\n                                      onChange={(e) =>\r\n                                          props.onChange(e.target.checked)\r\n                                      }\r\n                                  />\r\n                              ),\r\n                          },\r\n                      ]\r\n                    : []),\r\n                {\r\n                    key: 'daily_update_dynamic_line_item_wise_files',\r\n                    label: 'Enable line item wise photos/videos ?',\r\n                    tooltip:\r\n                        'The updater will be able to upload photo videos with respect to each line item (in addition to a general photo section)',\r\n                    widget: 'radio-group',\r\n                    options: ['Yes', 'No'],\r\n                },\r\n            ],\r\n        };\r\n        return meta;\r\n    };\r\n\r\n    refresh() {\r\n        this.setState({\r\n            render_helper: !this.state.render_helper,\r\n        });\r\n    }\r\n\r\n    handleShowFieldsPreviewClick(customFields) {\r\n        this.setState({\r\n            showFormPreview: true,\r\n            formPreviewMeta: customFields,\r\n        });\r\n    }\r\n\r\n    getTabularViewFieldsConfigMeta() {\r\n        return {\r\n            formItemLayout: null,\r\n            fields: [\r\n                {\r\n                    label: 'Select table columns',\r\n                    key: 'srvc_type_tabular_view_columns',\r\n                    widget: 'select',\r\n                    options: getSrvcReqStaticAndCustomPossibleFields(\r\n                        this.getPossibleSpecificFields()\r\n                    ),\r\n                    widgetProps: {\r\n                        mode: 'multiple',\r\n                        allowClear: true,\r\n                        showSearch: true,\r\n                        optionFilterProp: 'children',\r\n                    },\r\n                },\r\n            ],\r\n        };\r\n    }\r\n\r\n    getViewFieldsConfigMeta() {\r\n        return {\r\n            formItemLayout: null,\r\n            fields: [\r\n                {\r\n                    label: 'Select customer request filters',\r\n                    key: 'srvc_type_view_columns',\r\n                    widget: 'select',\r\n                    options: getSrvcReqStaticAndCustomPossibleFieldsFilter(\r\n                        this.getPossibleSpecificFields(),\r\n                        this.getAuthoritiesList()\r\n                    ),\r\n                    widgetProps: {\r\n                        mode: 'multiple',\r\n                        allowClear: true,\r\n                        showSearch: true,\r\n                        optionFilterProp: 'children',\r\n                    },\r\n                },\r\n            ],\r\n        };\r\n    }\r\n    getSelectedSpAuthoritiesRoleList = () => {\r\n        var seletcedRoleIds =\r\n            this.formRef?.current?.getFieldValue('authority_id');\r\n        let selectedRoleData = [];\r\n        if (seletcedRoleIds) {\r\n            seletcedRoleIds.forEach((singleRole) => {\r\n                const roleData = this.state.viewData?.role_list.find(\r\n                    (singleAuthority) => singleAuthority.value === singleRole\r\n                );\r\n                if (roleData) {\r\n                    selectedRoleData.push({\r\n                        key: roleData.value,\r\n                        label: roleData.label,\r\n                        value: roleData.value,\r\n                    });\r\n                }\r\n            });\r\n        }\r\n        return selectedRoleData;\r\n    };\r\n\r\n    getSpAuthoritiesMeta = () => {\r\n        const initialValues = this.state.editMode\r\n            ? this.state.viewData?.form_data\r\n            : {};\r\n\r\n        let roleWiseSpecificFields = [];\r\n        let enableLocGrpFilteration = [];\r\n        let selected_srvc_prvdr_roles_authorities = [];\r\n        let authorities_role_list = this.state.viewData?.role_list;\r\n        var seletcedRoleIds =\r\n            this.formRef?.current?.getFieldValue('authority_id');\r\n        var isFilterationEnable = this.formRef?.current?.getFieldValue(\r\n            'enable_location_group_based_filteration'\r\n        );\r\n        if (seletcedRoleIds) {\r\n            seletcedRoleIds.forEach((singleRole) => {\r\n                let seletcedRoleTitle = authorities_role_list?.filter(\r\n                    (singleAuthority) => singleAuthority.value == singleRole\r\n                )?.[0]?.label;\r\n\r\n                let dummyObj = {\r\n                    label: seletcedRoleTitle,\r\n                    key: singleRole + '_enable_cross_visibility_of_authorities',\r\n                    widget: 'checkbox',\r\n                };\r\n                selected_srvc_prvdr_roles_authorities.push(dummyObj);\r\n\r\n                let roleWiseSpecificFieldsobj = {\r\n                    label:\r\n                        seletcedRoleTitle +\r\n                        ' Specific Fields (only ' +\r\n                        seletcedRoleTitle +\r\n                        ' of the selected service request can edit these fields)',\r\n                    key: 'srvc_authority_' + singleRole + '_specific_fields',\r\n                    widget: 'select',\r\n                    options: this.getVerticalOrSelectedSrvcTypeSpecificFields(\r\n                        true,\r\n                        [],\r\n                        true\r\n                    ),\r\n                    widgetProps: {\r\n                        mode: 'multiple',\r\n                        allowClear: true,\r\n                        showSearch: true,\r\n                        optionFilterProp: 'children',\r\n                    },\r\n                    // rules : [{\r\n                    //     required: true,\r\n                    //     message : 'Please select specific Fields'\r\n                    // }],\r\n                };\r\n                roleWiseSpecificFields.push(roleWiseSpecificFieldsobj);\r\n            });\r\n            if (seletcedRoleIds?.length > 0) {\r\n                let obj = {\r\n                    label: 'Enable location group based filteration',\r\n                    key: 'enable_location_group_based_filteration',\r\n                    widget: 'checkbox',\r\n                    onChange: () => this.refresh(),\r\n                };\r\n                enableLocGrpFilteration.push(obj);\r\n            }\r\n\r\n            if (isFilterationEnable && seletcedRoleIds?.length > 0) {\r\n                let data = {\r\n                    label: 'Select authorities to be filtered',\r\n                    key: 'select_authorities_to_be_filtered_fr_loc_grp',\r\n                    widget: 'select',\r\n                    widgetProps: {\r\n                        mode: 'multiple',\r\n                        allowClear: true,\r\n                        optionFilterProp: 'children',\r\n                    },\r\n                    options: this.getSelectedSpAuthoritiesRoleList(),\r\n                };\r\n                enableLocGrpFilteration.push(data);\r\n            }\r\n        }\r\n\r\n        let selected_roles_srvc_authorities_seperator = [];\r\n        if (selected_srvc_prvdr_roles_authorities.length > 0) {\r\n            let dummyObj = {\r\n                render: () => {\r\n                    return (\r\n                        <Alert\r\n                            message=\"Enable cross visibility of selected authorities\"\r\n                            type=\"info\"\r\n                        />\r\n                    );\r\n                },\r\n            };\r\n            selected_roles_srvc_authorities_seperator.push(dummyObj);\r\n        }\r\n\r\n        const meta = {\r\n            columns: 1,\r\n            formItemLayout: null,\r\n            initialValues: initialValues,\r\n            fields: [\r\n                {\r\n                    key: 'authority_id',\r\n                    label: 'Select roles',\r\n                    widget: 'select',\r\n                    widgetProps: {\r\n                        mode: 'multiple',\r\n                        allowClear: true,\r\n                        optionFilterProp: 'children',\r\n                    },\r\n                    options: this.state.viewData.role_list,\r\n                    placeholder: 'Please select role',\r\n                    onChange: (value) => {\r\n                        const currentSelectedRolesFrAutohrities =\r\n                            this.formRef.current.getFieldValue(\r\n                                'select_authorities_to_be_filtered_fr_loc_grp'\r\n                            ) || [];\r\n                        // Filter the current authorities to only include those that are still selected as auhtorities\r\n                        const updatedSelectedAuthorities =\r\n                            currentSelectedRolesFrAutohrities.filter((auth) =>\r\n                                value.includes(auth)\r\n                            );\r\n                        //update the select_authorities_to_be_filtered_fr_loc_grp\r\n                        this.formRef.current.setFieldsValue({\r\n                            select_authorities_to_be_filtered_fr_loc_grp:\r\n                                updatedSelectedAuthorities,\r\n                        });\r\n                        this.refresh();\r\n                    },\r\n                },\r\n                ...selected_roles_srvc_authorities_seperator,\r\n                ...selected_srvc_prvdr_roles_authorities,\r\n                ...roleWiseSpecificFields,\r\n                ...enableLocGrpFilteration,\r\n            ],\r\n        };\r\n        return meta;\r\n    };\r\n\r\n    getVerticalOrSelectedSrvcTypeSpecificFields(\r\n        Files = false,\r\n        selectedCustomFields = [],\r\n        onlyAttachments = false\r\n    ) {\r\n        //Make a dynamic authorities role_access base on selected role\r\n        let brandSrvcTypeDetails =\r\n            this.state.viewData.brand_wise_specific_fields;\r\n        let verticalOrSelectedSrvcTypeSpecificFields = [];\r\n        let seletcedSrvcType =\r\n            this.formRef?.current?.getFieldValue('srvc_type_id');\r\n        if (seletcedSrvcType) {\r\n            seletcedSrvcType.forEach((singleSrvcType) => {\r\n                let singleBrandSrvcTypeDeatils = brandSrvcTypeDetails?.filter(\r\n                    (singleSrvcTypeId) =>\r\n                        singleSrvcTypeId.service_type_id == singleSrvcType\r\n                )?.[0];\r\n\r\n                if (singleBrandSrvcTypeDeatils == undefined) {\r\n                    return;\r\n                }\r\n                let org_name = singleBrandSrvcTypeDeatils?.org_name;\r\n                let service_types_name =\r\n                    singleBrandSrvcTypeDeatils?.service_types_name;\r\n                let srvc_type_specific_fields =\r\n                    singleBrandSrvcTypeDeatils?.specific_fields;\r\n                console.log(\r\n                    'srvc_type_specific_fields',\r\n                    srvc_type_specific_fields\r\n                );\r\n                if (srvc_type_specific_fields) {\r\n                    // let dummyVerticalOrSelectedSrvcTypeSpecificFields = this.getVerticalOrSelectedSrvcTypeSpecificFields(customFields,Files=false,selectedCustomFields=[])\r\n                    // verticalOrSelectedSrvcTypeSpecificFields = [...verticalOrSelectedSrvcTypeSpecificFields,...dummyVerticalOrSelectedSrvcTypeSpecificFields];\r\n                    let translatedFields = Files\r\n                        ? decodeFieldsMetaFrmJson(\r\n                              srvc_type_specific_fields,\r\n                              undefined,\r\n                              true\r\n                          )\r\n                        : decodeFieldsMetaFrmJson(srvc_type_specific_fields);\r\n                    if (translatedFields) {\r\n                        translatedFields.forEach((singleTranslatedFields) => {\r\n                            if (\r\n                                singleTranslatedFields?.label &&\r\n                                (!Files ||\r\n                                    (Files &&\r\n                                        !selectedCustomFields?.includes(\r\n                                            singleTranslatedFields?.key\r\n                                        )))\r\n                            ) {\r\n                                let dummyObj = {\r\n                                    label: `${org_name} - ${service_types_name} - ${singleTranslatedFields?.label}`,\r\n                                    value: singleTranslatedFields?.key,\r\n                                };\r\n                                if (!onlyAttachments) {\r\n                                    verticalOrSelectedSrvcTypeSpecificFields.push(\r\n                                        dummyObj\r\n                                    );\r\n                                } else if (\r\n                                    !singleTranslatedFields.cust_component ||\r\n                                    singleTranslatedFields?.cust_component ==\r\n                                        'Files'\r\n                                ) {\r\n                                    verticalOrSelectedSrvcTypeSpecificFields.push(\r\n                                        dummyObj\r\n                                    );\r\n                                }\r\n                            }\r\n                        });\r\n                    }\r\n                }\r\n            });\r\n        }\r\n        //Get service provider specific details\r\n        let srvc_prvdr_specific_fields = this.formRef?.current?.getFieldValue(\r\n            'sp_cust_fields_json'\r\n        );\r\n        if (srvc_prvdr_specific_fields) {\r\n            srvc_prvdr_specific_fields = Files\r\n                ? decodeFieldsMetaFrmJson(\r\n                      srvc_prvdr_specific_fields,\r\n                      undefined,\r\n                      true\r\n                  )\r\n                : decodeFieldsMetaFrmJson(srvc_prvdr_specific_fields);\r\n        }\r\n        if (srvc_prvdr_specific_fields) {\r\n            srvc_prvdr_specific_fields.forEach((singlesrvcPrvdrSpcField) => {\r\n                if (\r\n                    singlesrvcPrvdrSpcField?.label &&\r\n                    (!Files ||\r\n                        (Files &&\r\n                            !selectedCustomFields?.includes(\r\n                                singlesrvcPrvdrSpcField?.key\r\n                            )))\r\n                ) {\r\n                    let srvcPrvdrSpecificFields = {\r\n                        label: `Wify - ${singlesrvcPrvdrSpcField?.label}`,\r\n                        value: singlesrvcPrvdrSpcField?.key,\r\n                    };\r\n                    if (!onlyAttachments) {\r\n                        verticalOrSelectedSrvcTypeSpecificFields.push(\r\n                            srvcPrvdrSpecificFields\r\n                        );\r\n                    } else if (\r\n                        !singlesrvcPrvdrSpcField.cust_component ||\r\n                        singlesrvcPrvdrSpcField?.cust_component == 'Files'\r\n                    ) {\r\n                        verticalOrSelectedSrvcTypeSpecificFields.push(\r\n                            srvcPrvdrSpecificFields\r\n                        );\r\n                    }\r\n                }\r\n            });\r\n        }\r\n        return verticalOrSelectedSrvcTypeSpecificFields;\r\n    }\r\n\r\n    getRatingConfigMeta(initialValues) {\r\n        const enableRatingValue =\r\n            this.formRef?.current?.getFieldValue('enable_sp_rating');\r\n        return {\r\n            fields: [\r\n                {\r\n                    key: 'enable_sp_rating',\r\n                    label: 'Enable Ratings',\r\n                    widget: 'checkbox',\r\n                    onChange: () => this.refresh(),\r\n                },\r\n                ...(enableRatingValue\r\n                    ? [\r\n                          {\r\n                              key: `vertical_type_rating_config`,\r\n                              render: () => {\r\n                                  return (\r\n                                      <Tabs\r\n                                          defaultActiveKey=\"sp_rating\"\r\n                                          className=\"gx-mb-2\"\r\n                                      >\r\n                                          <Tabs.TabPane\r\n                                              tab=\"Authorities\"\r\n                                              key=\"authorities\"\r\n                                          >\r\n                                              <FormBuilder\r\n                                                  form={this.formRef}\r\n                                                  meta={getRatingFormMeta(\r\n                                                      initialValues,\r\n                                                      this.getParamsFrSpAuthorityRatings()\r\n                                                  )}\r\n                                              />\r\n                                          </Tabs.TabPane>\r\n                                          (\r\n                                          <Tabs.TabPane\r\n                                              tab=\"Assignees\"\r\n                                              key=\"sp_assignees\"\r\n                                          >\r\n                                              <FormBuilder\r\n                                                  form={this.formRef}\r\n                                                  meta={getRatingFormMeta(\r\n                                                      initialValues,\r\n                                                      this.getParamsFrSpAssigneeRatings()\r\n                                                  )}\r\n                                              />\r\n                                          </Tabs.TabPane>\r\n                                          )\r\n                                      </Tabs>\r\n                                  );\r\n                              },\r\n                          },\r\n                      ]\r\n                    : []),\r\n            ],\r\n        };\r\n    }\r\n\r\n    getSpPayoutsConfigMeta() {\r\n        // here\r\n        const enableSpPayouts =\r\n            this.formRef?.current?.getFieldValue('enable_sp_payouts');\r\n        const selectedSPAuthorities =\r\n            this.formRef?.current?.getFieldValue('authority_id');\r\n        const allOrgRoles = this.state.viewData?.role_list;\r\n        const deploymentRoles = this.formRef?.current?.getFieldValue(\r\n            'deployment_possible_roles'\r\n        );\r\n        let spAuthoritiesFrOptions = [];\r\n        if (selectedSPAuthorities && allOrgRoles) {\r\n            spAuthoritiesFrOptions = allOrgRoles.filter((role) =>\r\n                selectedSPAuthorities.includes(role.value)\r\n            );\r\n        }\r\n\r\n        let spAuthoritiesFrVendorOptions = [];\r\n        if (selectedSPAuthorities || deploymentRoles) {\r\n            // Combine the selected authorities and deployment roles into a single array\r\n            const combinedAuthorities = [\r\n                ...(selectedSPAuthorities || []),\r\n                ...(deploymentRoles || []),\r\n            ];\r\n\r\n            // Create a Set to get distinct values\r\n            const distinctAuthorities = new Set(combinedAuthorities);\r\n\r\n            // Filter allOrgRoles to include only those with distinct authorities\r\n            spAuthoritiesFrVendorOptions = allOrgRoles.filter((role) =>\r\n                distinctAuthorities.has(role.value)\r\n            );\r\n        }\r\n\r\n        const spPayoutsConfig = this.getConfigFrSpPayouts();\r\n\r\n        const addPayoutUserType = this.formRef?.current?.getFieldValue(\r\n            'select_who_can_add_payout'\r\n        );\r\n\r\n        let userSelector = {\r\n            key: 'which_static_user_can_add_sp_payout',\r\n            widget: UserSelectorWidget,\r\n            placeholder: 'Search User',\r\n            className: 'wy-pl-1rem',\r\n            required: true,\r\n            widgetProps: {\r\n                mode: 'multiple',\r\n                allowClear: true,\r\n            },\r\n        };\r\n\r\n        let authoritySelector = {\r\n            key: 'which_sp_authority_can_add_sp_payout',\r\n            widget: 'select',\r\n            placeholder: 'Select Authority',\r\n            className: 'wy-pl-1rem',\r\n            required: true,\r\n            widgetProps: {\r\n                mode: 'multiple',\r\n                allowClear: true,\r\n                showSearch: true,\r\n                optionFilterProp: 'children',\r\n            },\r\n            options: spAuthoritiesFrOptions,\r\n        };\r\n\r\n        const selectAddSPPayoutWidgetRights =\r\n            addPayoutUserType === 'authority'\r\n                ? [authoritySelector]\r\n                : addPayoutUserType === 'static_user'\r\n                  ? [userSelector]\r\n                  : [];\r\n        return {\r\n            fields: [\r\n                {\r\n                    key: 'enable_sp_payouts',\r\n                    label: 'Enable Payout',\r\n                    widget: 'checkbox',\r\n                    onChange: (e) => {\r\n                        this.refresh();\r\n                        if (\r\n                            e.target.checked == true &&\r\n                            this.formRef.current.getFieldValue(\r\n                                'srvc_type_sp_payouts_config'\r\n                            ) == undefined\r\n                        ) {\r\n                            this.formRef.current.setFieldsValue({\r\n                                srvc_type_sp_payouts_config: JSON.stringify({\r\n                                    '1236c271-876d-4352-a044-157acbeee076': {\r\n                                        key: '1236c271-876d-4352-a044-157acbeee076',\r\n                                        label: 'Payouts',\r\n                                        total_field_label: '',\r\n                                    },\r\n                                }),\r\n                            });\r\n                        }\r\n                    },\r\n                },\r\n                ...(enableSpPayouts\r\n                    ? [\r\n                          {\r\n                              label: 'Service type sp payouts config json',\r\n                              key: 'srvc_type_sp_payouts_config',\r\n                              formItemProps: {\r\n                                  style: {\r\n                                      display: 'none',\r\n                                  },\r\n                              },\r\n                          },\r\n                          {\r\n                              key: 'SP Payouts',\r\n                              render: () => (\r\n                                  <>\r\n                                      <LineItemManagementModule\r\n                                          form={this.formRef}\r\n                                          onChange={(newObj) => {\r\n                                              this.formRef.current.setFieldsValue(\r\n                                                  {\r\n                                                      srvc_type_sp_payouts_config:\r\n                                                          JSON.stringify(\r\n                                                              newObj\r\n                                                          ),\r\n                                                  }\r\n                                              );\r\n                                              this.refresh();\r\n                                          }}\r\n                                          currValue={spPayoutsConfig}\r\n                                          isVendorPayouts\r\n                                          hideBtn\r\n                                      />\r\n                                  </>\r\n                              ),\r\n                          },\r\n                          {\r\n                              key: 'seperator_6',\r\n                              render: () => (\r\n                                  <div className=\"gx-mb-3 gx-bg-grey\"></div>\r\n                              ),\r\n                          },\r\n                          {\r\n                              key: 'sp_vendor_roles',\r\n                              label: 'Vendor Roles',\r\n                              widget: 'select',\r\n                              required: true,\r\n                              widgetProps: {\r\n                                  mode: 'multiple',\r\n                                  allowClear: true,\r\n                                  showSearch: true,\r\n                                  optionFilterProp: 'children',\r\n                              },\r\n                              options: spAuthoritiesFrVendorOptions,\r\n                          },\r\n                          {\r\n                              key: 'select_who_can_add_payout',\r\n                              label: 'Select who can add payout',\r\n                              widget: 'radio-group',\r\n                              onChange: () => this.refresh(),\r\n                              required: true,\r\n                              options: [\r\n                                  {\r\n                                      value: 'authority',\r\n                                      label: 'Authority',\r\n                                  },\r\n                                  {\r\n                                      value: 'static_user',\r\n                                      label: 'Static User',\r\n                                  },\r\n                              ],\r\n                          },\r\n                          ...selectAddSPPayoutWidgetRights,\r\n                          {\r\n                              key: 'sp_who_will_not_be_able_to_see_the_sp_payout_tab',\r\n                              label: 'Who will not be able to see the SP payout tab',\r\n                              widget: 'select',\r\n                              required: true,\r\n                              widgetProps: {\r\n                                  mode: 'multiple',\r\n                                  allowClear: true,\r\n                                  showSearch: true,\r\n                                  optionFilterProp: 'children',\r\n                              },\r\n                              options: this.state.viewData?.role_list,\r\n                          },\r\n                      ]\r\n                    : []),\r\n            ],\r\n        };\r\n    }\r\n\r\n    getParamsFrSpAuthorityRatings = () => {\r\n        return {\r\n            viewData: this.state.viewData,\r\n            formRef: this.formRef,\r\n            editMode: this.state.editMode,\r\n            message: 'Here, you can configure who will rate which authority.',\r\n            keySelectedAuthoritiesorDeployment: 'authority_id',\r\n            keyRoleList: 'role_list',\r\n            collapseKey: 'sp_rating_authority_',\r\n            tabName: 'authority',\r\n            ratingTypeKey: 'sp_rating_type_',\r\n            refresh: () => this.refresh(),\r\n            authorityIdKey: 'authority_id',\r\n            selectedRatingAuthority: 'sp_authority_',\r\n            staticUserKey: 'sp_static_user_',\r\n            templateKey: 'sp_rating_template_',\r\n        };\r\n    };\r\n\r\n    getParamsFrSpAssigneeRatings = () => {\r\n        return {\r\n            viewData: this.state.viewData,\r\n            formRef: this.formRef,\r\n            editMode: this.state.editMode,\r\n            message: this.isProjectNature()\r\n                ? 'Here, you can configure who will rate On Field Users.'\r\n                : 'Here, you can configure who will rate subtask assignees.',\r\n            keySelectedAuthoritiesorDeployment: 'deployment_possible_roles',\r\n            keyRoleList: 'onfield_role_list',\r\n            collapseKey: 'sp_rating_assignee_',\r\n            tabName: 'assignee',\r\n            ratingTypeKey: 'sp_rating_type_fr_deployment_',\r\n            refresh: () => this.refresh(),\r\n            authorityIdKey: 'authority_id',\r\n            selectedRatingAuthority: 'sp_authority_fr_deployment_',\r\n            selectedRatingDynamicUserRole:\r\n                'sp_dynamic_user_role_fr_deployment_',\r\n            staticUserKey: 'sp_static_user_fr_deployment_',\r\n            templateKey: 'sp_rating_template_fr_deployment',\r\n            isProjectBased: this.isProjectNature(),\r\n            selectedRolesToBeRatedKey: 'selected_roles_to_be_rated',\r\n            modeOfRatingFrAssigneesKey: 'mode_of_rating_fr_assignees',\r\n            HasMatchingCustomerAccessKey:\r\n                'has_matching_customer_access_fr_assignee_',\r\n            HasMatchingLocationGroupKey:\r\n                'has_matching_location_group_fr_assignee_',\r\n        };\r\n    };\r\n    isProjectNature() {\r\n        return (\r\n            this.formRef?.current?.getFieldValue('vertical_nature') ==\r\n            PROJECT_BASED_SERVICE_TYPE\r\n        );\r\n    }\r\n\r\n    getAutomationMeta = () => {\r\n        let _allSelectedSubTasks = [];\r\n        if (\r\n            this.formRef &&\r\n            this.formRef?.current?.getFieldsValue('select_subtask_fr_readiness')\r\n        ) {\r\n            _allSelectedSubTasks = this.getDynamicSbtskFields();\r\n        }\r\n\r\n        return {\r\n            fields: [\r\n                {\r\n                    key: `automation_tabs_readiness`,\r\n                    render: () => {\r\n                        return (\r\n                            <Tabs\r\n                                defaultActiveKey=\"readiness\"\r\n                                className=\"gx-mb-2\"\r\n                            >\r\n                                <Tabs.TabPane tab=\"Readiness\" key=\"readiness\">\r\n                                    <FormBuilder\r\n                                        form={this.formRef}\r\n                                        meta={{\r\n                                            formItemLayout: null,\r\n                                            fields: [\r\n                                                {\r\n                                                    label: 'Select subtasks',\r\n                                                    key: 'select_subtask_fr_readiness',\r\n                                                    widget: 'select',\r\n                                                    options: [\r\n                                                        ...this.state.viewData\r\n                                                            ?.applicable_subtasks_list,\r\n                                                    ],\r\n                                                    widgetProps: {\r\n                                                        mode: 'multiple',\r\n                                                        allowClear: true,\r\n                                                        showSearch: true,\r\n                                                        optionFilterProp:\r\n                                                            'children',\r\n                                                        onChange: () =>\r\n                                                            this.refresh(),\r\n                                                    },\r\n                                                },\r\n                                                ..._allSelectedSubTasks,\r\n                                            ],\r\n                                        }}\r\n                                    />\r\n                                </Tabs.TabPane>\r\n                            </Tabs>\r\n                        );\r\n                    },\r\n                },\r\n            ],\r\n        };\r\n    };\r\n\r\n    getDynamicSbtskFields() {\r\n        const returnDynamicSbtskFields = [];\r\n        const selectedSbtsks = this?.formRef?.current?.getFieldValue(\r\n            'select_subtask_fr_readiness'\r\n        );\r\n        selectedSbtsks &&\r\n            selectedSbtsks.map((_eachSubTaskSelected) => {\r\n                const selectedSubTaskName =\r\n                    this.state.viewData.applicable_subtasks_list.filter(\r\n                        (_eachSubTask) =>\r\n                            _eachSubTask.value == _eachSubTaskSelected\r\n                    );\r\n                const mandatoryField = {\r\n                    label: (\r\n                        <>\r\n                            Select mandatory field for{' '}\r\n                            <span style={{ fontWeight: '500', marginLeft: 2 }}>\r\n                                {selectedSubTaskName[0].label}\r\n                            </span>\r\n                        </>\r\n                    ),\r\n                    key: `select_mandatory_field_fr_${selectedSubTaskName[0].value}`,\r\n                    widget: 'select',\r\n                    required: true,\r\n                    options: getSrvcReqStaticAndCustomPossibleFields(\r\n                        this.getPossibleSpecificFields(),\r\n                        [\r\n                            'full_address',\r\n                            'srvc_prvdr',\r\n                            'sbtsks',\r\n                            'vertical_title',\r\n                        ],\r\n                        true\r\n                    ),\r\n                    widgetProps: {\r\n                        mode: 'multiple',\r\n                        allowClear: true,\r\n                        showSearch: true,\r\n                        optionFilterProp: 'children',\r\n                    },\r\n                };\r\n                returnDynamicSbtskFields.push(mandatoryField);\r\n            });\r\n        return returnDynamicSbtskFields;\r\n    }\r\n\r\n    getMetaFrSubtaskTypesConfiguration() {\r\n        const meta = {\r\n            columns: 1,\r\n            formItemLayout: null,\r\n            fields: [\r\n                {\r\n                    key: `applicable_subtask_types_fr_vertical`,\r\n                    label: 'Applicable subtask types (leave empty if all are applicable)',\r\n                    widget: 'select',\r\n                    widgetProps: {\r\n                        mode: 'multiple',\r\n                        allowClear: true,\r\n                    },\r\n                    options: this.getApplicableSubtaskTypes(),\r\n                    onChange: (value) => {\r\n                        this.onApplicableSubtaskTypesFrVerticalChange(value);\r\n                        this.refresh();\r\n                    },\r\n                },\r\n                {\r\n                    key: `select_default_subtask_type`,\r\n                    label: 'Select default subtask type',\r\n                    widget: 'select',\r\n                    options: this.getDefaultApplicableSubtaskTypesList(),\r\n                    widgetProps: {\r\n                        allowClear: true,\r\n                        onChange: (value) => {\r\n                            handleClearSelect(\r\n                                value,\r\n                                this.formRef,\r\n                                'select_default_subtask_type'\r\n                            );\r\n                        },\r\n                    },\r\n                },\r\n            ],\r\n        };\r\n        return meta;\r\n    }\r\n\r\n    getApplicableSubtaskTypes() {\r\n        return this.state.viewData?.applicable_subtasks_list || [];\r\n    }\r\n    getDefaultApplicableSubtaskTypesList() {\r\n        const selectedApplicableSubtaskTypes =\r\n            this.formRef?.current?.getFieldValue(\r\n                'applicable_subtask_types_fr_vertical'\r\n            ) || [];\r\n        let applicableSubtaskTypes =\r\n            this.state.viewData?.applicable_subtasks_list || [];\r\n        if (selectedApplicableSubtaskTypes?.length > 0) {\r\n            applicableSubtaskTypes = applicableSubtaskTypes.filter(\r\n                (singleApplicableSubtaskType) =>\r\n                    selectedApplicableSubtaskTypes.includes(\r\n                        singleApplicableSubtaskType.value\r\n                    )\r\n            );\r\n        }\r\n        const defaultSubtaskTypesList =\r\n            applicableSubtaskTypes?.length > 0\r\n                ? applicableSubtaskTypes\r\n                : this.getApplicableSubtaskTypes();\r\n        return defaultSubtaskTypesList;\r\n    }\r\n    onApplicableSubtaskTypesFrVerticalChange(value) {\r\n        const defaultSubtaskType = this.formRef?.current?.getFieldValue(\r\n            'select_default_subtask_type'\r\n        );\r\n        if (!value.includes(defaultSubtaskType)) {\r\n            handleClearSelect(\r\n                undefined,\r\n                this.formRef,\r\n                'select_default_subtask_type'\r\n            );\r\n        }\r\n    }\r\n    render() {\r\n        const { editorItem } = this.props;\r\n        const {\r\n            isFormSubmitting,\r\n            visible,\r\n            isLoadingViewData,\r\n            error,\r\n            viewData,\r\n            showFormPreview,\r\n            formPreviewMeta,\r\n            profitLossTab,\r\n        } = this.state;\r\n\r\n        var editorTitle = editorItem?.title;\r\n        var editMode = true;\r\n        if (editorTitle == undefined) {\r\n            editorTitle = 'Add new custom fields';\r\n            editMode = false;\r\n        } else {\r\n            editorTitle = 'Edit custom fields - ' + editorTitle;\r\n        }\r\n\r\n        const initialValues = this.state.editMode\r\n            ? this.state.viewData?.form_data?.form_data\r\n            : {};\r\n        //Set default table columns for srvc_type_tabular_view_columns\r\n        if (\r\n            initialValues?.hasOwnProperty('srvc_type_tabular_view_columns') ==\r\n            false\r\n        ) {\r\n            initialValues['srvc_type_tabular_view_columns'] =\r\n                addDefaultKeysForTabularView();\r\n        }\r\n        return visible ? (\r\n            <Modal\r\n                title={`${editorTitle}`}\r\n                visible={visible}\r\n                onOk={this.handleOk}\r\n                confirmLoading={isFormSubmitting}\r\n                width={1300}\r\n                onCancel={this.handleCancel}\r\n                footer={null}\r\n            >\r\n                {isLoadingViewData ? (\r\n                    <div className=\"gx-loader-view gx-loader-position\">\r\n                        <CircularProgress />\r\n                    </div>\r\n                ) : viewData == undefined ? (\r\n                    <p className=\"gx-text-red\">{error}</p>\r\n                ) : (\r\n                    <Form\r\n                        className=\"ant-col gx-my-1 ant-col-xs-24 gx-mt-0\"\r\n                        layout=\"vertical\"\r\n                        ref={this.formRef}\r\n                        onFinish={(data) => {\r\n                            this.submitForm(data);\r\n                        }}\r\n                        initialValues={this.state.editMode ? initialValues : {}}\r\n                    >\r\n                        <Tabs\r\n                            defaultActiveKey=\"sp_fields\"\r\n                            onChange={(activeKey) => {\r\n                                if (activeKey === 'profitLossTab') {\r\n                                    this.setState({\r\n                                        profitLossTab: true,\r\n                                    });\r\n                                }\r\n                            }}\r\n                        >\r\n                            <Tabs.TabPane tab=\"SP Fields\" key=\"sp_fields\">\r\n                                <FormBuilder\r\n                                    meta={this.getMeta()}\r\n                                    form={this.formRef}\r\n                                />\r\n                            </Tabs.TabPane>\r\n\r\n                            <Tabs.TabPane\r\n                                tab=\"Line Item\"\r\n                                key=\"line_item\"\r\n                                forceRender={true}\r\n                            >\r\n                                <FormBuilder\r\n                                    meta={this.getMetaFrLineItemConfiguration()}\r\n                                    form={this.formRef}\r\n                                />\r\n                            </Tabs.TabPane>\r\n                            {!this.isProjectNature() && (\r\n                                <Tabs.TabPane\r\n                                    tab=\"Subtask Types\"\r\n                                    key=\"subtask_types\"\r\n                                    forceRender={true}\r\n                                >\r\n                                    <FormBuilder\r\n                                        meta={this.getMetaFrSubtaskTypesConfiguration()}\r\n                                        form={this.formRef}\r\n                                    />\r\n                                </Tabs.TabPane>\r\n                            )}\r\n                            <Tabs.TabPane\r\n                                tab=\"Deployment\"\r\n                                key=\"deployment\"\r\n                                forceRender={true}\r\n                            >\r\n                                <FormBuilder\r\n                                    meta={this.getDeploymentConfigMeta(\r\n                                        initialValues\r\n                                    )}\r\n                                    form={this.formRef}\r\n                                />\r\n                            </Tabs.TabPane>\r\n\r\n                            <Tabs.TabPane\r\n                                tab=\"Daily updates\"\r\n                                key=\"daily_updates\"\r\n                            >\r\n                                <FormBuilder\r\n                                    meta={this.getDailyUpdatesMeta(\r\n                                        initialValues\r\n                                    )}\r\n                                    form={this.formRef}\r\n                                />\r\n                            </Tabs.TabPane>\r\n\r\n                            <Tabs.TabPane\r\n                                tab=\"Pricing Master\"\r\n                                key=\"pricing_master\"\r\n                                forceRender={false}\r\n                            >\r\n                                <FormBuilder\r\n                                    meta={this.getPricingMasterConfigMeta(\r\n                                        initialValues\r\n                                    )}\r\n                                    form={this.formRef}\r\n                                />\r\n                            </Tabs.TabPane>\r\n\r\n                            <Tabs.TabPane\r\n                                tab=\"Billing\"\r\n                                key=\"billing\"\r\n                                forceRender={true}\r\n                            >\r\n                                <FormBuilder\r\n                                    meta={this.getBillingMasterConfigMeta(\r\n                                        initialValues\r\n                                    )}\r\n                                    form={this.formRef}\r\n                                />\r\n                            </Tabs.TabPane>\r\n                            <Tabs.TabPane\r\n                                tab=\"SP Payouts\"\r\n                                key=\"sp_payouts\"\r\n                                forceRender={true}\r\n                            >\r\n                                <FormBuilder\r\n                                    meta={this.getSpPayoutsConfigMeta()}\r\n                                    form={this.formRef}\r\n                                />\r\n                            </Tabs.TabPane>\r\n                            {!this.isProjectNature() && (\r\n                                <Tabs.TabPane\r\n                                    tab=\"P&L\"\r\n                                    key=\"profitLossTab\"\r\n                                    forceRender={true}\r\n                                >\r\n                                    {profitLossTab && (\r\n                                        <ProfitLossConfig\r\n                                            roleList={\r\n                                                this.state.viewData?.role_list\r\n                                            }\r\n                                            form_data={\r\n                                                this.state.viewData?.form_data\r\n                                            }\r\n                                            form={this.formRef}\r\n                                            editMode={this.props.editMode}\r\n                                        />\r\n                                    )}\r\n                                </Tabs.TabPane>\r\n                            )}\r\n                            {this.isProjectNature() && (\r\n                                <Tabs.TabPane\r\n                                    tab=\"P&L\"\r\n                                    key=\"profitLossTab\"\r\n                                    forceRender={true}\r\n                                >\r\n                                    {profitLossTab && (\r\n                                        <ProjectPLConfig\r\n                                            form={this.formRef}\r\n                                            roleList={\r\n                                                this.state.viewData?.role_list\r\n                                            }\r\n                                            form_data={\r\n                                                this.state.viewData?.form_data\r\n                                            }\r\n                                            isVendorPayouts\r\n                                        />\r\n                                    )}\r\n                                </Tabs.TabPane>\r\n                            )}\r\n                            <Tabs.TabPane\r\n                                tab=\"Notification\"\r\n                                key=\"notification\"\r\n                                forceRender={true}\r\n                            >\r\n                                <FormBuilder\r\n                                    meta={this.getFieldsWiseNotificationConfigMeta(\r\n                                        initialValues\r\n                                    )}\r\n                                    form={this.formRef}\r\n                                />\r\n                            </Tabs.TabPane>\r\n\r\n                            <Tabs.TabPane\r\n                                tab=\"Tabular view\"\r\n                                key=\"tabular_view\"\r\n                                forceRender={true}\r\n                            >\r\n                                <FormBuilder\r\n                                    meta={this.getTabularViewFieldsConfigMeta(\r\n                                        initialValues\r\n                                    )}\r\n                                    form={this.formRef}\r\n                                />\r\n                            </Tabs.TabPane>\r\n\r\n                            <Tabs.TabPane\r\n                                tab=\"Views\"\r\n                                key=\"views\"\r\n                                forceRender={true}\r\n                            >\r\n                                <FormBuilder\r\n                                    meta={this.getViewFieldsConfigMeta()}\r\n                                    form={this.formRef}\r\n                                />\r\n                            </Tabs.TabPane>\r\n\r\n                            <Tabs.TabPane\r\n                                tab=\"SP Authorities\"\r\n                                key=\"sp_authorities\"\r\n                                forceRender={true}\r\n                            >\r\n                                <FormBuilder\r\n                                    meta={this.getSpAuthoritiesMeta()}\r\n                                    form={this.formRef}\r\n                                />\r\n                            </Tabs.TabPane>\r\n                            <Tabs.TabPane\r\n                                tab=\"Automations\"\r\n                                key=\"automations\"\r\n                                forceRender={true}\r\n                            >\r\n                                <FormBuilder\r\n                                    meta={this.getAutomationMeta()}\r\n                                    form={this.formRef}\r\n                                />\r\n                            </Tabs.TabPane>\r\n                            <Tabs.TabPane\r\n                                tab=\"SP Ratings\"\r\n                                key=\"sp_rating\"\r\n                                forceRender={true}\r\n                            >\r\n                                <FormBuilder\r\n                                    meta={this.getRatingConfigMeta(\r\n                                        initialValues\r\n                                    )}\r\n                                    form={this.formRef}\r\n                                />\r\n                            </Tabs.TabPane>\r\n                        </Tabs>\r\n\r\n                        <Form.Item className=\"gx-mt-3\">\r\n                            <Button\r\n                                type=\"primary\"\r\n                                htmlType=\"submit\"\r\n                                disabled={isFormSubmitting}\r\n                            >\r\n                                {editMode ? 'Save' : 'Submit'}\r\n                            </Button>\r\n                        </Form.Item>\r\n                        {isFormSubmitting ? (\r\n                            <div className=\"gx-loader-view gx-loader-position\">\r\n                                <CircularProgress />\r\n                            </div>\r\n                        ) : null}\r\n                        {error ? <p className=\"gx-text-red\">{error}</p> : null}\r\n                    </Form>\r\n                )}\r\n                {showFormPreview && (\r\n                    <FormPreviewMeta\r\n                        formPreviewMeta={formPreviewMeta}\r\n                        onCancel={() => {\r\n                            this.setState({ showFormPreview: false });\r\n                        }}\r\n                    />\r\n                )}\r\n            </Modal>\r\n        ) : (\r\n            <></>\r\n        );\r\n    }\r\n}\r\n\r\nexport default SpConfigEditor;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AACxE,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,UAAU,MAAM,6BAA6B;AACpD,OAAOC,gBAAgB,MAAM,yCAAyC;AACtE,OAAOC,eAAe,MAAM,gEAAgE;AAC5F,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,wBAAwB,MAAM,8DAA8D;AACnG,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,gBAAgB,MAAM,oDAAoD;AACjF,SACIC,YAAY,EACZC,0BAA0B,EAC1BC,WAAW,EACXC,+BAA+B,EAC/BC,iBAAiB,EACjBC,iBAAiB,QACd,0BAA0B;AACjC,SACIC,uBAAuB,EACvBC,yBAAyB,QACtB,wDAAwD;AAC/D,OAAOC,oCAAoC,MAAM,0EAA0E;AAC3H,OAAOC,8BAA8B,MAAM,oEAAoE;AAC/G,SACIC,4BAA4B,EAC5BC,+BAA+B,EAC/BC,iBAAiB,EACjBC,uCAAuC,QACpC,2BAA2B;AAClC,SAASC,6CAA6C,QAAQ,2BAA2B;AACzF,OAAOC,kBAAkB,MAAM,sDAAsD;AACrF,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,SAASC,uBAAuB,QAAQ,4BAA4B;AACpE,OAAOC,UAAU,MAAM,iDAAiD;AACxE,OAAOC,gBAAgB,MAAM,wBAAwB;AACrD,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,cAAc,MAAM,gDAAgD;AAE3E,MAAMC,QAAQ,GAAG,+BAA+B;AAChD,MAAMC,SAAS,GAAG,yBAAyB;AAC3C,MAAM;EAAEC;AAAS,CAAC,GAAGvC,KAAK;AAC1B,MAAMwC,UAAU,GAAG,SAAS;AAC5B,MAAMC,QAAQ,GAAG,SAAS;AAE1B,MAAMC,gBAAgB,GAAG,CACrB;EACIC,KAAK,EAAE,cAAc;EACrBC,KAAK,EAAE;AACX,CAAC,EACD;EACID,KAAK,EAAE,aAAa;EACpBC,KAAK,EAAE;AACX,CAAC,CACJ;AACD,MAAMC,cAAc,SAAShD,SAAS,CAAC;EACnCiD,WAAWA,CAACC,MAAK,EAAE;IACf,KAAK,CAACA,MAAK,CAAC;IAAC,KAIjBC,SAAS,GAAG;MACRC,aAAa,EAAE,KAAK;MACpBC,OAAO,EAAE,KAAK;MACdC,gBAAgB,EAAE,KAAK;MACvBC,QAAQ,EAAEC,SAAS;MACnBC,iBAAiB,EAAE,KAAK;MACxBC,QAAQ,EAAE,IAAI,CAACR,KAAK,CAACQ,QAAQ;MAC7BC,KAAK,EAAE,EAAE;MACTC,wBAAwB,EAAE,KAAK;MAC/BC,oCAAoC,EAAEL,SAAS;MAC/CM,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE;IAChB,CAAC;IAAA,KACDC,KAAK,GAAG,IAAI,CAACb,SAAS;IAAA,KAmEtBc,QAAQ,GAAG,MAAM;MACb,IAAI,CAACC,QAAQ,CAAC;QACVb,OAAO,EAAE,KAAK;QACdC,gBAAgB,EAAE;MACtB,CAAC,CAAC;MACF,IAAI,CAACa,qBAAqB,CAAC,CAAC;IAChC,CAAC;IAAA,KAkBDC,YAAY,GAAG,MAAM;MACjB,IAAI,CAACF,QAAQ,CAAC;QACVb,OAAO,EAAE;MACb,CAAC,CAAC;MACF,IAAI,CAACc,qBAAqB,CAAC,CAAC;IAChC,CAAC;IAAA,KAEDE,UAAU,GAAIC,IAAI,IAAK;MACnB,IAAI,CAACJ,QAAQ,CAAC;QACVZ,gBAAgB,EAAE;MACtB,CAAC,CAAC;MACF,IAAIiB,MAAM,GAAGD,IAAI;MACjB,IAAIA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,6BAA6B,EAAE;QACrC,MAAMA,6BAA6B,GAAGC,IAAI,CAACC,KAAK,CAC5CJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,6BACV,CAAC;QACD,IAAIA,6BAA6B,EAAE;UAC/BA,6BAA6B,CAACG,OAAO,CAAEC,SAAS,IAAK;YAAA,IAAAC,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;YACjDC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEX,SAAS,CAAC;YACxC,IACI,EAAAC,oBAAA,OAAI,CAACb,KAAK,CAACT,QAAQ,cAAAsB,oBAAA,wBAAAC,qBAAA,GAAnBD,oBAAA,CAAqBW,SAAS,cAAAV,qBAAA,wBAAAC,sBAAA,GAA9BD,qBAAA,CAAgCU,SAAS,cAAAT,sBAAA,uBAAzCA,sBAAA,CACI,OAAOH,SAAS,CAACa,MAAM,EAAE,CAC5B,KACD,EAACnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAG,OAAOM,SAAS,CAACa,MAAM,EAAE,CAAC,GACpC;cACElB,MAAM,CAAC,OAAOK,SAAS,CAACa,MAAM,EAAE,CAAC,GAC7B,CAAAb,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEc,GAAG,KAAI,EAAE;YAC5B;YACA,IACI,EAAAV,qBAAA,OAAI,CAAChB,KAAK,CAACT,QAAQ,cAAAyB,qBAAA,wBAAAC,qBAAA,GAAnBD,qBAAA,CAAqBQ,SAAS,cAAAP,qBAAA,wBAAAC,qBAAA,GAA9BD,qBAAA,CAAgCO,SAAS,cAAAN,qBAAA,uBAAzCA,qBAAA,CACI,aAAaN,SAAS,CAACa,MAAM,EAAE,CAClC,KACD,EAACnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAG,aAAaM,SAAS,CAACa,MAAM,EAAE,CAAC,GAC1C;cACElB,MAAM,CAAC,aAAaK,SAAS,CAACa,MAAM,EAAE,CAAC,GACnC,CAAAb,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEe,SAAS,KAAI,EAAE;YAClC;YACA,IACI,EAAAR,qBAAA,OAAI,CAACnB,KAAK,CAACT,QAAQ,cAAA4B,qBAAA,wBAAAC,qBAAA,GAAnBD,qBAAA,CAAqBK,SAAS,cAAAJ,qBAAA,wBAAAC,qBAAA,GAA9BD,qBAAA,CAAgCI,SAAS,cAAAH,qBAAA,uBAAzCA,qBAAA,CACI,OAAOT,SAAS,CAACa,MAAM,EAAE,CAC5B,KACD,EAACnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAG,OAAOM,SAAS,CAACa,MAAM,EAAE,CAAC,GACpC;cACElB,MAAM,CAAC,OAAOK,SAAS,CAACa,MAAM,EAAE,CAAC,GAC7B,CAAAb,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEgB,GAAG,KAAI,EAAE;YAC5B;UACJ,CAAC,CAAC;QACN;MACJ;MACA,IAAItB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuB,mBAAmB,EAAE;QAC3B,MAAMA,mBAAmB,GAAGpB,IAAI,CAACC,KAAK,CAACJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuB,mBAAmB,CAAC;QACjE,IAAIA,mBAAmB,EAAE;UACrBA,mBAAmB,CAAClB,OAAO,CAAEC,SAAS,IAAK;YAAA,IAAAkB,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;YACvC,IACI,EAAAR,qBAAA,OAAI,CAAC9B,KAAK,CAACT,QAAQ,cAAAuC,qBAAA,wBAAAC,qBAAA,GAAnBD,qBAAA,CAAqBN,SAAS,cAAAO,qBAAA,wBAAAC,qBAAA,GAA9BD,qBAAA,CAAgCP,SAAS,cAAAQ,qBAAA,uBAAzCA,qBAAA,CACI,OAAOpB,SAAS,CAACa,MAAM,EAAE,CAC5B,KACD,EAACnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAG,OAAOM,SAAS,CAACa,MAAM,EAAE,CAAC,GACpC;cACElB,MAAM,CAAC,OAAOK,SAAS,CAACa,MAAM,EAAE,CAAC,GAAG,EAAE;YAC1C;YACA,IACI,EAAAQ,qBAAA,OAAI,CAACjC,KAAK,CAACT,QAAQ,cAAA0C,qBAAA,wBAAAC,sBAAA,GAAnBD,qBAAA,CAAqBT,SAAS,cAAAU,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgCV,SAAS,cAAAW,sBAAA,uBAAzCA,sBAAA,CACI,aAAavB,SAAS,CAACa,MAAM,EAAE,CAClC,KACD,EAACnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAG,aAAaM,SAAS,CAACa,MAAM,EAAE,CAAC,GAC1C;cACElB,MAAM,CAAC,aAAaK,SAAS,CAACa,MAAM,EAAE,CAAC,GAAG,EAAE;YAChD;YACA,IACI,EAAAW,sBAAA,OAAI,CAACpC,KAAK,CAACT,QAAQ,cAAA6C,sBAAA,wBAAAC,sBAAA,GAAnBD,sBAAA,CAAqBZ,SAAS,cAAAa,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgCb,SAAS,cAAAc,sBAAA,uBAAzCA,sBAAA,CACI,OAAO1B,SAAS,CAACa,MAAM,EAAE,CAC5B,KACD,EAACnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAG,OAAOM,SAAS,CAACa,MAAM,EAAE,CAAC,GACpC;cACElB,MAAM,CAAC,OAAOK,SAAS,CAACa,MAAM,EAAE,CAAC,GAAG,EAAE;YAC1C;UACJ,CAAC,CAAC;QACN;MACJ;MAEA,MAAMc,UAAU,GAAIC,IAAI,IAAK;QACzB,IAAI,CAACtC,QAAQ,CAAC;UACVZ,gBAAgB,EAAE,KAAK;UACvBK,KAAK,EAAE,EAAE;UACTN,OAAO,EAAE;QACb,CAAC,CAAC;QACF,IAAI,CAACoD,uBAAuB,CAACD,IAAI,CAACE,QAAQ,CAAC;QAC3C,IAAI,CAACvC,qBAAqB,CAAC,CAAC;MAChC,CAAC;MACD,MAAMwC,OAAO,GAAIhD,KAAK,IAAK;QACvB;QACA,IAAI,CAACO,QAAQ,CAAC;UACVZ,gBAAgB,EAAE,KAAK;UACvBK,KAAK,EAAElD,UAAU,CAACmG,oBAAoB,CAACjD,KAAK;QAChD,CAAC,CAAC;MACN,CAAC;MACD,IAAI,IAAI,CAACK,KAAK,CAACN,QAAQ,EAAE;QACrBjD,UAAU,CAACoG,cAAc,CACrBpE,SAAS,GAAG,GAAG,GAAG,IAAI,CAACS,KAAK,CAAC4D,UAAU,CAACC,EAAE,EAC1CxC,MAAM,EACNgC,UAAU,EACVI,OACJ,CAAC;MACL,CAAC,MAAM;QACHlG,UAAU,CAACuG,eAAe,CAACvE,SAAS,EAAE8B,MAAM,EAAEgC,UAAU,EAAEI,OAAO,CAAC;MACtE;IACJ,CAAC;IAAA,KAsNDM,OAAO,GAAG,MAAM;MAAA,IAAAC,aAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACZ,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7C,IAAIC,mBAAmB,GAAGF,aAAa;MACvC,IACI,CAAAA,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEG,mBAAmB,KAAIlE,SAAS,IAC/C,CAAA+D,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEG,mBAAmB,KAAI,EAAE,EAC1C;QACED,mBAAmB,GAAG,CAAC,CAAC;MAC5B;MACA,IAAIE,YAAY,IAAAT,aAAA,GAAG,IAAI,CAACU,OAAO,cAAAV,aAAA,wBAAAC,qBAAA,GAAZD,aAAA,CAAcW,OAAO,cAAAV,qBAAA,uBAArBA,qBAAA,CAAuBW,aAAa,CACnD,qBACJ,CAAC;MACD,IAAIH,YAAY,IAAInE,SAAS,EAAE;QAC3BmE,YAAY,GAAGJ,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEG,mBAAmB;MACrD;MACA,MAAMK,yBAAyB,IAAAX,cAAA,GAAG,IAAI,CAACQ,OAAO,cAAAR,cAAA,wBAAAC,qBAAA,GAAZD,cAAA,CAAcS,OAAO,cAAAR,qBAAA,uBAArBA,qBAAA,CAAuBS,aAAa,CAClE,0BACJ,CAAC;MAED,MAAME,IAAI,GAAG;QACTC,OAAO,EAAE,CAAC;QACVC,cAAc,EAAE,IAAI;QACpBX,aAAa,EAAEE,mBAAmB;QAClCU,MAAM,EAAE,CACJ;UACIC,GAAG,EAAE,gBAAgB;UACrBrF,KAAK,EAAE,wBAAwB;UAC/BsF,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE,CACH;YACIC,GAAG,EAAE;UACT,CAAC;QAET,CAAC,EACD;UACIH,GAAG,EAAE,eAAe;UACpBrF,KAAK,EAAE,sBAAsB;UAC7ByF,MAAM,EAAE;QACZ,CAAC,EACD;UACIzF,KAAK,eAAEhD,KAAA,CAAA0I,aAAA;YAAAC,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAAM,qBAAyB,CAAC;UACvCX,GAAG,EAAE,iBAAiB;UACtBI,MAAM,EAAE,aAAa;UACrBH,QAAQ,EAAE,IAAI;UACdW,QAAQ,EAAEA,CAAA,KAAM;YACZ,IAAI,CAACpB,OAAO,CAACC,OAAO,CAACoB,cAAc,CAAC;cAChCC,YAAY,EAAE;YAClB,CAAC,CAAC;YACF,IAAI,CAACC,OAAO,CAAC,CAAC;UAClB,CAAC;UACD;UACA;UACA;UACAC,OAAO,EAAEhI,+BAA+B,CAAC;QAC7C,CAAC,EACD;UACIgH,GAAG,EAAE,cAAc;UACnBrF,KAAK,EAAE,0BAA0B;UACjCyF,MAAM,EAAE,QAAQ;UAChBa,WAAW,EAAE;YACTC,IAAI,EAAE,UAAU;YAChBC,gBAAgB,EAAE,UAAU;YAC5BP,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACG,OAAO,CAAC;UACjC,CAAC;UACDC,OAAO,EAAE,IAAI,CAACI,uBAAuB,CAAC,CAAC;UACvClB,KAAK,EAAE,CACH;YACID,QAAQ,EAAE,IAAI;YACdoB,OAAO,EAAE;UACb,CAAC,CACJ;UACDC,WAAW,EAAE;QACjB,CAAC,EACD;UACI3G,KAAK,EAAE,0BAA0B;UACjCqF,GAAG,EAAE,qBAAqB;UAC1BI,MAAM,EAAE,UAAU;UAClBkB,WAAW,EAAE,4BAA4B;UACzCpB,KAAK,EAAE,CACH;YACID,QAAQ,EAAE,IAAI;YACdoB,OAAO,EAAE;UACb,CAAC,CACJ;UACDT,QAAQ,EAAGW,KAAK,IAAK;YACjB,IAAI,CAACzF,QAAQ,CAAC;cACVd,aAAa,EAAE,CAAC,IAAI,CAACY,KAAK,CAACZ;YAC/B,CAAC,CAAC;UACN;QACJ,CAAC,EACD;UACIgF,GAAG,EAAE,uBAAuB;UAC5BwB,MAAM,EAAEA,CAAA,KAAM;YACV,oBACI7J,KAAA,CAAA0I,aAAA;cAAAC,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GACKpB,YAAY,IAAI,EAAE,iBACf5H,KAAA,CAAA0I,aAAA;cAAAC,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,gBACIhJ,KAAA,CAAA0I,aAAA,CAACrI,MAAM;cACHyJ,OAAO,EAAGC,CAAC,IACP,IAAI,CAACC,4BAA4B,CAC7BpC,YACJ,CACH;cACDqC,IAAI,eAAEjK,KAAA,CAAA0I,aAAA,CAAC7H,cAAc;gBAAA8H,MAAA;gBAAAC,QAAA;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAA,CAAE,CAAE;cAAAL,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAC5B,cAEO,CACP,CACR,eACDhJ,KAAA,CAAA0I,aAAA,CAAC5H,IAAI;cACDoJ,EAAE,EACE,uBAAuB,GACvBC,kBAAkB,CAACvC,YAAY,CAClC;cACDwC,MAAM,EAAC,QAAQ;cAAAzB,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAClB,qBACsB,EAAC,KAClB,CACL,CAAC;UAEd;QACJ,CAAC,EACD;UACIX,GAAG,EAAE,0BAA0B;UAC/BrF,KAAK,eACDhD,KAAA,CAAA0I,aAAA;YAAK2B,SAAS,EAAC,UAAU;YAAA1B,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAAC,4BAErB,CACR;UACDP,MAAM,EAAE,UAAU;UAClBQ,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACG,OAAO,CAAC;QACjC,CAAC,EACD,IAAIpB,yBAAyB,GACvB,CACI;UACIK,GAAG,EAAE,wCAAwC;UAC7CrF,KAAK,EAAE,gCAAgC;UACvC2G,WAAW,EAAE,YAAY;UACzBrB,QAAQ,EAAE,IAAI;UACdgB,WAAW,EAAE;YACTgB,UAAU,EAAE,IAAI;YAChBrB,QAAQ,EAAGlG,KAAK,IAAK;cACjBzB,iBAAiB,CACbyB,KAAK,EACL,IAAI,CAAC8E,OAAO,EACZ,wCACJ,CAAC;YACL;UACJ,CAAC;UACDU,KAAK,EAAE,CACH;YACIgC,SAAS,EAAEhJ,iBAAiB;YAC5BmI,OAAO,EACH;UACR,CAAC;QAET,CAAC,CACJ,GACD,EAAE,CAAC,EACT;UACI1G,KAAK,EAAE,iCAAiC;UACxCqF,GAAG,EAAE,2BAA2B;UAChCI,MAAM,EAAE,QAAQ;UAChBY,OAAO,EAAE,IAAI,CAACmB,yBAAyB,CAAC,CACpC;YACI/B,MAAM,EAAE;UACZ,CAAC,CACJ,CAAC;UACFa,WAAW,EAAE;YACTgB,UAAU,EAAE,IAAI;YAChBrB,QAAQ,EAAGlG,KAAK,IAAK;cACjBzB,iBAAiB,CACbyB,KAAK,EACL,IAAI,CAAC8E,OAAO,EACZ,2BACJ,CAAC;YACL;UACJ;QACJ,CAAC,EACD;UACI7E,KAAK,EAAE,gDAAgD;UACvDqF,GAAG,EAAE,mBAAmB;UACxBI,MAAM,EAAE,QAAQ;UAChBY,OAAO,EAAE,EAAA9B,sBAAA,OAAI,CAACtD,KAAK,CAACT,QAAQ,cAAA+D,sBAAA,uBAAnBA,sBAAA,CAAqBkD,iBAAiB,KAAI,EAAE;UACrDC,OAAO,EACH,2FAA2F;UAC/FpB,WAAW,EAAE;YACTC,IAAI,EAAE,UAAU;YAChBC,gBAAgB,EAAE,UAAU;YAC5Bc,UAAU,EAAE,IAAI;YAChBrB,QAAQ,EAAGlG,KAAK,IAAK;cACjBzB,iBAAiB,CACbyB,KAAK,EACL,IAAI,CAAC8E,OAAO,EACZ,mBACJ,CAAC;YACL;UACJ;QACJ,CAAC;QACD;QACA,GAAG,IAAI,CAAC8C,+BAA+B,CAAC,CAAC;MAEjD,CAAC;MACD,OAAO1C,IAAI;IACf,CAAC;IAAA,KA4ND2C,8BAA8B,GAAG,CAACC,WAAW,EAAEC,eAAe,KAAK;MAC/D,KAAK,IAAIC,cAAc,IAAID,eAAe,EAAE;QACxC,IACID,WAAW,CAACpC,MAAM,IAAIsC,cAAc,CAACtC,MAAM,IAC3CoC,WAAW,CAACvB,WAAW,CAACC,IAAI,IAAI,UAAU,EAC5C;UACE,OAAO,IAAI;QACf;MACJ;IACJ,CAAC;IA4CD;IAAA,KACAyB,kBAAkB,GAAIxD,aAAa,IAAK;MAAA,IAAAyD,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACpC,MAAMpD,IAAI,GAAG;QACTE,cAAc,EAAE,IAAI;QACpBX,aAAa,EAAEA,aAAa;QAC5BY,MAAM,EAAE,CACJ;UACIpF,KAAK,EAAE,gBAAgB;UACvBqF,GAAG,EAAE,0BAA0B;UAC/BI,MAAM,EAAE;QACZ,CAAC,EACD;UACIJ,GAAG,EAAE,wBAAwB;UAC7BrF,KAAK,EAAE,cAAc;UACrByF,MAAM,EAAE,QAAQ;UAChBa,WAAW,EAAE;YACTC,IAAI,EAAE;UACV,CAAC;UACDF,OAAO,EAAEnI;QACb,CAAC,EACD;UACImH,GAAG,EAAE,kDAAkD;UACvDrF,KAAK,EAAE,gDAAgD;UACvDyF,MAAM,EAAE,QAAQ;UAChBa,WAAW,EAAE;YACTC,IAAI,EAAE,UAAU;YAChB+B,UAAU,EAAE,IAAI;YAChB9B,gBAAgB,EAAE;UACtB,CAAC;UACDH,OAAO,EAAE,IAAI,CAACmB,yBAAyB,CAAC,CAAC,IAAI;QACjD,CAAC,EACD;UACInC,GAAG,EAAE,6CAA6C;UAClDrF,KAAK,EAAE,0CAA0C;UACjDyF,MAAM,EAAE,QAAQ;UAChBa,WAAW,EAAE;YACTC,IAAI,EAAE,UAAU;YAChB+B,UAAU,EAAE,IAAI;YAChB9B,gBAAgB,EAAE;UACtB,CAAC;UACDH,OAAO,EAAE,EAAA4B,sBAAA,OAAI,CAAChH,KAAK,CAACT,QAAQ,cAAAyH,sBAAA,uBAAnBA,sBAAA,CAAqBM,SAAS,KAAI;QAC/C,CAAC,EACD;UACIlD,GAAG,EAAE,qCAAqC;UAC1CrF,KAAK,EAAE,oCAAoC;UAC3CyF,MAAM,EAAE,QAAQ;UAChBa,WAAW,EAAE;YACTC,IAAI,EAAE,UAAU;YAChB+B,UAAU,EAAE,IAAI;YAChB9B,gBAAgB,EAAE;UACtB,CAAC;UACDH,OAAO,EAAE,EAAA6B,sBAAA,OAAI,CAACjH,KAAK,CAACT,QAAQ,cAAA0H,sBAAA,uBAAnBA,sBAAA,CAAqBK,SAAS,KAAI;QAC/C,CAAC,EACD;UACIlD,GAAG,EAAE,wCAAwC;UAC7CrF,KAAK,EAAE,oCAAoC;UAC3CyF,MAAM,EAAE,QAAQ;UAChBa,WAAW,EAAE;YACTC,IAAI,EAAE,UAAU;YAChB+B,UAAU,EAAE,IAAI;YAChB9B,gBAAgB,EAAE;UACtB,CAAC;UACDH,OAAO,EAAE,EAAA8B,sBAAA,OAAI,CAAClH,KAAK,CAACT,QAAQ,cAAA2H,sBAAA,uBAAnBA,sBAAA,CAAqBI,SAAS,KAAI;QAC/C,CAAC,EACD;UACIlD,GAAG,EAAE,6CAA6C;UAClDrF,KAAK,EAAE,mCAAmC;UAC1CyF,MAAM,EAAE,QAAQ;UAChBa,WAAW,EAAE;YACTC,IAAI,EAAE,UAAU;YAChB+B,UAAU,EAAE,IAAI;YAChB9B,gBAAgB,EAAE;UACtB,CAAC;UACDH,OAAO,EAAE,EAAA+B,sBAAA,OAAI,CAACnH,KAAK,CAACT,QAAQ,cAAA4H,sBAAA,uBAAnBA,sBAAA,CAAqBG,SAAS,KAAI;QAC/C,CAAC,EACD;UACIlD,GAAG,EAAE,yCAAyC;UAC9CrF,KAAK,EAAE,6CAA6C;UACpDyF,MAAM,EAAE,QAAQ;UAChBa,WAAW,EAAE;YACTC,IAAI,EAAE,UAAU;YAChB+B,UAAU,EAAE,IAAI;YAChB9B,gBAAgB,EAAE;UACtB,CAAC;UACDH,OAAO,EAAE,EAAAgC,sBAAA,OAAI,CAACpH,KAAK,CAACT,QAAQ,cAAA6H,sBAAA,uBAAnBA,sBAAA,CAAqBE,SAAS,KAAI;QAC/C,CAAC;MAET,CAAC;MACD,OAAOtD,IAAI;IACf,CAAC;IAAA,KAsBDuD,6BAA6B,GAAIhE,aAAa,IAAK;MAAA,IAAAiE,sBAAA;MAC/C,MAAMxD,IAAI,GAAG;QACTE,cAAc,EAAE,IAAI;QACpBX,aAAa,EAAEA,aAAa;QAC5BY,MAAM,EAAE,CACJ;UACIpF,KAAK,EAAE,oBAAoB;UAC3BqF,GAAG,EAAE,sCAAsC;UAC3CI,MAAM,EAAE;QACZ,CAAC,EACD;UACIzF,KAAK,eACDhD,KAAA,CAAA0I,aAAA;YAAAC,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GACK,GAAG,EAAC,+CACwC,EAAC,GAC5C,CACT;UACDX,GAAG,EAAE,mDAAmD;UACxDI,MAAM,EAAE,QAAQ;UAChBY,OAAO,EAAE,EAAAoC,sBAAA,OAAI,CAACxH,KAAK,CAACT,QAAQ,cAAAiI,sBAAA,uBAAnBA,sBAAA,CAAqBF,SAAS,KAAI,EAAE;UAC7CjC,WAAW,EAAE;YACTgB,UAAU,EAAE,IAAI;YAChBf,IAAI,EAAE;UACV;QACJ,CAAC,EACD;UACIlB,GAAG,EAAE,sCAAsC;UAC3CwB,MAAM,EAAEA,CAAA,KAAM;YAAA,IAAA6B,sBAAA;YACV,oBACI1L,KAAA,CAAA0I,aAAA,CAAC/G,8BAA8B;cAC3BsH,QAAQ,EAAG0C,MAAM,IAAK;gBAClB,IAAI,CAAC9D,OAAO,CAACC,OAAO,CAACoB,cAAc,CAAC;kBAChC0C,yCAAyC,EACrClH,IAAI,CAACmH,SAAS,CAACF,MAAM;gBAC7B,CAAC,CAAC;gBACF,IAAI,CAACvC,OAAO,CAAC,CAAC;cAClB,CAAE;cACF0C,SAAS,EAAE,IAAI,CAACC,wBAAwB,CAAC,CAAE;cAC3CC,gBAAgB,EACZ,EAAAN,sBAAA,OAAI,CAACzH,KAAK,CAACT,QAAQ,cAAAkI,sBAAA,uBAAnBA,sBAAA,CAAqBH,SAAS,KAAI,EACrC;cAAA5C,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CACJ,CAAC;UAEV;QACJ,CAAC;MAET,CAAC;MACD,OAAOf,IAAI;IACf,CAAC;IAAA,KAEDgE,4BAA4B,GAAIzE,aAAa,IAAK;MAC9C,MAAMS,IAAI,GAAG;QACTE,cAAc,EAAE,IAAI;QACpBX,aAAa,EAAEA,aAAa;QAC5BY,MAAM,EAAE,CACJ;UACIpF,KAAK,EAAE,2BAA2B;UAClCqF,GAAG,EAAE,qCAAqC;UAC1CI,MAAM,EAAE;QACZ,CAAC,EACD;UACIJ,GAAG,EAAE,qCAAqC;UAC1CwB,MAAM,EAAEA,CAAA,KAAM;YACV,oBACI7J,KAAA,CAAA0I,aAAA,CAAA1I,KAAA,CAAAkM,QAAA,qBACIlM,KAAA,CAAA0I,aAAA,CAAC3H,wBAAwB;cACrBkI,QAAQ,EAAG0C,MAAM,IAAK;gBAClB,IAAI,CAAC9D,OAAO,CAACC,OAAO,CAACoB,cAAc,CAAC;kBAChCiD,mCAAmC,EAC/BzH,IAAI,CAACmH,SAAS,CAACF,MAAM;gBAC7B,CAAC,CAAC;gBACF,IAAI,CAACvC,OAAO,CAAC,CAAC;cAClB,CAAE;cACF0C,SAAS,EAAE,IAAI,CAACM,wBAAwB,CAAC,CAAE;cAC3CC,OAAO;cAAA1D,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CACV,CACH,CAAC;UAEX;QACJ,CAAC;MAET,CAAC;MACD,OAAOf,IAAI;IACf,CAAC;IAAA,KAmJDqE,kBAAkB,GAAG,MAAM;MAAA,IAAAC,cAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACvB,IAAIC,eAAe,GAAG,EAAE;MACxB,IAAIC,YAAY,IAAAJ,cAAA,GAAG,IAAI,CAAC1E,OAAO,cAAA0E,cAAA,wBAAAC,qBAAA,GAAZD,cAAA,CAAczE,OAAO,cAAA0E,qBAAA,uBAArBA,qBAAA,CAAuBzE,aAAa,CAAC,cAAc,CAAC;MACvE,IAAI6E,QAAQ,IAAAH,sBAAA,GAAG,IAAI,CAACxI,KAAK,CAACT,QAAQ,cAAAiJ,sBAAA,uBAAnBA,sBAAA,CAAqBlB,SAAS;MAC7C,IAAIoB,YAAY,IAAIA,YAAY,CAACE,MAAM,GAAG,CAAC,EAAE;QACzCF,YAAY,CAAC/H,OAAO,CAAEkI,eAAe,IAAK;UAAA,IAAAC,gBAAA;UACtC,IAAIC,qBAAqB,IAAAD,gBAAA,GAAGH,QAAQ,CAACK,MAAM,CACtCC,UAAU,IAAKA,UAAU,CAACnK,KAAK,IAAI+J,eACxC,CAAC,cAAAC,gBAAA,uBAF2BA,gBAAA,CAExB,CAAC,CAAC;UACN,IAAIC,qBAAqB,EAAE;YACvBN,eAAe,CAACS,IAAI,CAACH,qBAAqB,CAAC;UAC/C;QACJ,CAAC,CAAC;MACN;MACA,OAAON,eAAe;IAC1B,CAAC;IAAA,KAEDU,2CAA2C,GAAI5F,aAAa,IAAK;MAC7D,MAAMS,IAAI,GAAG;QACTE,cAAc,EAAE,IAAI;QACpBX,aAAa,EAAEA,aAAa;QAC5BY,MAAM,EAAE,CACJ;UACIpF,KAAK,EAAE,gDAAgD;UACvDqF,GAAG,EAAE,6CAA6C;UAClDgF,aAAa,EAAE;YACXC,KAAK,EAAE;cACHC,OAAO,EAAE;YACb;UACJ;QACJ,CAAC,EACD;UACIlF,GAAG,EAAE,uCAAuC;UAC5CwB,MAAM,EAAEA,CAAA,KAAM;YAAA,IAAA2D,cAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,cAAA,EAAAC,qBAAA;YACV,oBACI5N,KAAA,CAAA0I,aAAA,CAAChH,oCAAoC;cACjCyH,YAAY,GAAAqE,cAAA,GAAE,IAAI,CAAC3F,OAAO,cAAA2F,cAAA,wBAAAC,qBAAA,GAAZD,cAAA,CAAc1F,OAAO,cAAA2F,qBAAA,uBAArBA,qBAAA,CAAuB1F,aAAa,CAC9C,cACJ,CAAE;cACFiE,gBAAgB,EAAE,IAAI,CAACM,kBAAkB,CAAC,CAAE;cAC5CuB,eAAe,GAAAH,sBAAA,GACX,IAAI,CAACzJ,KAAK,CAACT,QAAQ,cAAAkK,sBAAA,uBAAnBA,sBAAA,CACMI,0BACT;cACDC,YAAY,EAAE,IAAI,CAACC,iCAAiC,CAAC,CAAE;cACvD/E,QAAQ,EAAG0C,MAAM,IAAK;gBAClB;gBACA,IAAI,CAAC9D,OAAO,CAACC,OAAO,CAACoB,cAAc,CAAC;kBAChC+E,2CAA2C,EACvCvJ,IAAI,CAACmH,SAAS,CAACF,MAAM;gBAC7B,CAAC,CAAC;gBACF,IAAI,CAACvC,OAAO,CAAC,CAAC;cAClB,CAAE;cACF8E,cAAc;cACdC,kBAAkB,GAAAR,cAAA,GAAE,IAAI,CAAC9F,OAAO,cAAA8F,cAAA,wBAAAC,qBAAA,GAAZD,cAAA,CAAc7F,OAAO,cAAA8F,qBAAA,uBAArBA,qBAAA,CAAuB7F,aAAa,CACpD,qBACJ,CAAE;cAAAY,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CACL,CAAC;UAEV;QACJ,CAAC;MAET,CAAC;MACD,OAAOf,IAAI;IACf,CAAC;IAAA,KA6GDmG,uBAAuB,GAAI5G,aAAa,IAAK;MAAA,IAAA6G,sBAAA,EAAAC,sBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,cAAA,EAAAC,qBAAA;MACzC,IAAI,IAAI,CAAC9K,KAAK,CAACN,QAAQ,IAAI,CAAC,IAAI,CAACM,KAAK,CAACJ,wBAAwB,EAAE;QAC7D;QACA,IAAI,CAACM,QAAQ,CAAC;UAAEN,wBAAwB,EAAE;QAAK,CAAC,CAAC;QACjD;MACJ;MACA,MAAMmL,kBAAkB,IAAAX,sBAAA,GAAG,IAAI,CAACxG,OAAO,CAACC,OAAO,cAAAuG,sBAAA,uBAApBA,sBAAA,CAAsBtG,aAAa,CAC1D,kCACJ,CAAC;MACD,IAAIkH,sBAAsB,GAAG,EAAE;MAC/B,IAAIC,cAAc,GAAG,EAAAZ,sBAAA,OAAI,CAACrK,KAAK,CAACT,QAAQ,cAAA8K,sBAAA,uBAAnBA,sBAAA,CAAqB/C,SAAS,KAAI,EAAE;MACzD,IAAI4D,gBAAgB,GAChB,EAAAZ,cAAA,OAAI,CAAC1G,OAAO,cAAA0G,cAAA,wBAAAC,qBAAA,GAAZD,cAAA,CAAczG,OAAO,cAAA0G,qBAAA,uBAArBA,qBAAA,CAAuBzG,aAAa,CAAC,2BAA2B,CAAC,OAAA0G,sBAAA,GACjE,IAAI,CAACxK,KAAK,CAACT,QAAQ,cAAAiL,sBAAA,wBAAAC,sBAAA,GAAnBD,sBAAA,CAAqBhJ,SAAS,cAAAiJ,sBAAA,uBAA9BA,sBAAA,CAAgCU,yBAAyB;MAC7D,IAAID,gBAAgB,EAAE;QAClBA,gBAAgB,CAACvK,OAAO,CAAEyK,sBAAsB,IAAK;UAAA,IAAAC,sBAAA;UACjD,IAAIC,YAAY,GACZL,cAAc,CAACjC,MAAM,CAChBC,UAAU,IACPA,UAAU,CAACnK,KAAK,IAAIsM,sBAC5B,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;UAEdJ,sBAAsB,CAAC9B,IAAI,CAAC;YACxB9E,GAAG,EAAE,YAAYgH,sBAAsB,EAAE;YACzCrM,KAAK,EAAE,sBAAsBuM,YAAY,CAACvM,KAAK,EAAE;YACjDsF,QAAQ,EAAE,IAAI;YACdG,MAAM,EAAE,QAAQ;YAChBY,OAAO,EACH,EAAAiG,sBAAA,OAAI,CAACrL,KAAK,CAACT,QAAQ,cAAA8L,sBAAA,uBAAnBA,sBAAA,CAAqBE,wBAAwB,KAAI;UACzD,CAAC,CAAC;QACN,CAAC,CAAC;MACN;MACA,IACIhI,aAAa,IACbA,aAAa,CAAC,kCAAkC,CAAC,IAAI/D,SAAS,EAChE;QACE+D,aAAa,CAAC,kCAAkC,CAAC,GAAG5E,UAAU;QAC9D4E,aAAa,CAAC,kCAAkC,CAAC,GAAG3E,QAAQ;MAChE;MACA,MAAMoF,IAAI,GAAG;QACTC,OAAO,EAAE,CAAC;QACVC,cAAc,EAAE,IAAI;QACpBX,aAAa,EAAEA,aAAa;QAC5BY,MAAM,EAAE,CACJ;UACIC,GAAG,EAAE,4BAA4B;UACjCrF,KAAK,EAAE,2BAA2B;UAClCyF,MAAM,EAAE,QAAQ;UAChBa,WAAW,EAAE;YACTC,IAAI,EAAE,UAAU;YAChBe,UAAU,EAAE,IAAI;YAChBgB,UAAU,EAAE;UAChB,CAAC;UACDjC,OAAO,GAAAsF,sBAAA,GAAE,IAAI,CAAC1K,KAAK,CAACT,QAAQ,cAAAmL,sBAAA,uBAAnBA,sBAAA,CAAqBpD;QAClC,CAAC,EACD;UACIlD,GAAG,EAAE,kCAAkC;UACvCrF,KAAK,eACDhD,KAAA,CAAA0I,aAAA;YAAAC,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,gBACIhJ,KAAA,CAAA0I,aAAA;YAAG2B,SAAS,EAAC,8BAA8B;YAAA1B,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAI,CAAC,iCAE9C,CACT;UACDP,MAAM,EAAExH,gBAAgB;UACxBqI,WAAW,EAAE;YACTmG,UAAU,EAAE7M,UAAU;YACtB8M,QAAQ,EAAE7M,QAAQ;YAClB8M,IAAI,EAAE,EAAE;YACR1G,QAAQ,EAAGc,CAAC,IAAK;cACb,IAAI,CAACX,OAAO,CAAC,CAAC;YAClB;UACJ;QACJ,CAAC,EACD;UACIf,GAAG,EAAE,kCAAkC;UACvCrF,KAAK,eACDhD,KAAA,CAAA0I,aAAA;YAAAC,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,gBACIhJ,KAAA,CAAA0I,aAAA;YAAG2B,SAAS,EAAC,8BAA8B;YAAA1B,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAI,CAAC,+BAE9C,CACT;UACDP,MAAM,EAAExH,gBAAgB;UACxBqI,WAAW,EAAE;YACTmG,UAAU,EAAET,kBAAkB,IAAIpM,UAAU;YAC5C8M,QAAQ,EAAE7M,QAAQ;YAClB8M,IAAI,EAAE;UACV;QACJ,CAAC,EACD;UACItH,GAAG,EAAE,2CAA2C;UAChDrF,KAAK,EAAE,4CAA4C;UACnDyF,MAAM,EAAE,QAAQ;UAChBkB,WAAW,EAAE,cAAc;UAC3BV,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACG,OAAO,CAAC,CAAC;UAC9BE,WAAW,EAAE;YACTC,IAAI,EAAE,UAAU;YAChBe,UAAU,EAAE,IAAI;YAChBgB,UAAU,EAAE,IAAI;YAChB9B,gBAAgB,EAAE;UACtB,CAAC;UACDH,OAAO,EAAE,EAAAuF,sBAAA,OAAI,CAAC3K,KAAK,CAACT,QAAQ,cAAAoL,sBAAA,uBAAnBA,sBAAA,CAAqBrD,SAAS,KAAI;QAC/C,CAAC,EACD;UACIlD,GAAG,EAAE,2BAA2B;UAChCrF,KAAK,EAAE,+BAA+B;UACtCyF,MAAM,EAAE,QAAQ;UAChBkB,WAAW,EAAE,cAAc;UAC3BV,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACG,OAAO,CAAC,CAAC;UAC9BE,WAAW,EAAE;YACTC,IAAI,EAAE,UAAU;YAChBe,UAAU,EAAE,IAAI;YAChBgB,UAAU,EAAE,IAAI;YAChB9B,gBAAgB,EAAE;UACtB,CAAC;UACDH,OAAO,EAAE,EAAAwF,sBAAA,OAAI,CAAC5K,KAAK,CAACT,QAAQ,cAAAqL,sBAAA,uBAAnBA,sBAAA,CAAqBtD,SAAS,KAAI;QAC/C,CAAC,EACD;UACIlD,GAAG,EAAE,iCAAiC;UACtCrF,KAAK,EAAE,iCAAiC;UACxCyF,MAAM,EAAE,UAAU;UAClBQ,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACG,OAAO,CAAC;QACjC,CAAC,EACD;UACIf,GAAG,EAAE,gCAAgC;UACrCI,MAAM,EAAE,aAAa;UACrBY,OAAO,EAAE,CACL;YACItG,KAAK,EAAE,oBAAoB;YAC3BC,KAAK,EAAE;UACX,CAAC,EACD;YAAED,KAAK,EAAE,gBAAgB;YAAEC,KAAK,EAAE;UAAiB,CAAC,CACvD;UACD+K,YAAY,EAAE,oBAAoB;UAClCV,aAAa,EAAE;YACXC,KAAK,EAAE;cACHC,OAAO,EAAE,EAAAuB,cAAA,OAAI,CAACjH,OAAO,cAAAiH,cAAA,wBAAAC,qBAAA,GAAZD,cAAA,CAAchH,OAAO,cAAAiH,qBAAA,uBAArBA,qBAAA,CAAuBhH,aAAa,CACzC,iCACJ,CAAC,IACK,OAAO,GACP;YACV;UACJ;QACJ,CAAC,EACD,GAAGkH,sBAAsB;MAEjC,CAAC;MACD,OAAOhH,IAAI;IACf,CAAC;IAAA,KAED2H,mBAAmB,GAAIpI,aAAa,IAAK;MAAA,IAAAqI,sBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACrC,IAAInB,cAAc,GAAG,EAAAW,sBAAA,OAAI,CAAC5L,KAAK,CAACT,QAAQ,cAAAqM,sBAAA,uBAAnBA,sBAAA,CAAqBtE,SAAS,KAAI,EAAE;MACzD,IAAI+E,yBAAyB,IAAAR,cAAA,GAAG,IAAI,CAACjI,OAAO,cAAAiI,cAAA,wBAAAC,qBAAA,GAAZD,cAAA,CAAchI,OAAO,cAAAiI,qBAAA,uBAArBA,qBAAA,CAAuBhI,aAAa,CAChE,gCACJ,CAAC;MACD,IAAIuI,yBAAyB,IAAI7M,SAAS,EAAE;QACxC6M,yBAAyB,GACrB9I,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE+I,8BAA8B;MACrD;MACA,IAAIC,2BAA2B,IAAAR,cAAA,GAAG,IAAI,CAACnI,OAAO,cAAAmI,cAAA,wBAAAC,qBAAA,GAAZD,cAAA,CAAclI,OAAO,cAAAmI,qBAAA,uBAArBA,qBAAA,CAAuBlI,aAAa,CAClE,6BACJ,CAAC;MACD,IAAIyI,2BAA2B,IAAI/M,SAAS,EAAE;QAC1C+M,2BAA2B,GACvBhJ,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEgJ,2BAA2B;MAClD;MACA,MAAMC,iCAAiC,GACnC,EAAAP,cAAA,OAAI,CAACrI,OAAO,cAAAqI,cAAA,wBAAAC,qBAAA,GAAZD,cAAA,CAAcpI,OAAO,cAAAqI,qBAAA,uBAArBA,qBAAA,CAAuBpI,aAAa,CAChC,uCACJ,CAAC,OAAAqI,sBAAA,GACD,IAAI,CAACnM,KAAK,CAACT,QAAQ,cAAA4M,sBAAA,wBAAAC,sBAAA,GAAnBD,sBAAA,CAAqB3K,SAAS,cAAA4K,sBAAA,uBAA9BA,sBAAA,CACMK,qCAAqC;MAC/C,MAAMzI,IAAI,GAAG;QACTC,OAAO,EAAE,CAAC;QACVC,cAAc,EAAE,IAAI;QACpBX,aAAa,EAAEA,aAAa;QAC5BY,MAAM,EAAE,CACJ;UACIyB,MAAM,EAAEA,CAAA,kBACJ7J,KAAA,CAAA0I,aAAA;YAAI2B,SAAS,EAAC,SAAS;YAAA1B,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAAC,8BAEpB;QAEZ,CAAC,EACD;UACIX,GAAG,EAAE,2BAA2B;UAChCrF,KAAK,EAAE,8BAA8B;UACrCyF,MAAM,EAAE,QAAQ;UAChBa,WAAW,EAAE;YACTC,IAAI,EAAE;UACV,CAAC;UACDF,OAAO,EAAE6F;QACb,CAAC,EACD;UACI7G,GAAG,EAAE,sCAAsC;UAC3CrF,KAAK,EAAE,mCAAmC;UAC1CyF,MAAM,EAAE,QAAQ;UAChBa,WAAW,EAAE;YACTC,IAAI,EAAE,UAAU;YAChBe,UAAU,EAAE,IAAI;YAChBgB,UAAU,EAAE,IAAI;YAChB9B,gBAAgB,EAAE;UACtB,CAAC;UACDH,OAAO,EAAE6F;QACb,CAAC,EACD;UACIlM,KAAK,EAAE,0BAA0B;UACjCqF,GAAG,EAAE,6BAA6B;UAClCI,MAAM,EAAE,UAAU;UAClBkB,WAAW,EAAE,4BAA4B;UACzCV,QAAQ,EAAGW,KAAK,IAAK;YACjB,IAAI,CAAC+G,WAAW,CAAC,CAAC;UACtB;QACJ,CAAC,EACD;UACI9G,MAAM,EAAEA,CAAA,KAAM;YACV,oBACI7J,KAAA,CAAA0I,aAAA;cAAAC,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GACKwH,2BAA2B,IAAI,EAAE,iBAC9BxQ,KAAA,CAAA0I,aAAA;cAAAC,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,gBACIhJ,KAAA,CAAA0I,aAAA,CAACrI,MAAM;cACHyJ,OAAO,EAAGC,CAAC,IACP,IAAI,CAACC,4BAA4B,CAC7BwG,2BACJ,CACH;cACDvG,IAAI,eAAEjK,KAAA,CAAA0I,aAAA,CAAC7H,cAAc;gBAAA8H,MAAA;gBAAAC,QAAA;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAA,CAAE,CAAE;cAAAL,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAC5B,cAEO,CACP,CACR,eACDhJ,KAAA,CAAA0I,aAAA,CAAC5H,IAAI;cACDoJ,EAAE,EACE,uBAAuB,GACvBC,kBAAkB,CACdqG,2BACJ,CACH;cACDpG,MAAM,EAAC,QAAQ;cAAAzB,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAClB,qBACsB,EAAC,KAClB,CACL,CAAC;UAEd;QACJ,CAAC,EACD;UACIa,MAAM,EAAEA,CAAA,kBAAM7J,KAAA,CAAA0I,aAAA;YAAAC,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAQ;QAC1B,CAAC,EACD;UACIX,GAAG,EAAE,+BAA+B;UACpCrF,KAAK,EAAE,mBAAmB;UAC1ByF,MAAM,EAAE,aAAa;UACrBY,OAAO,EAAE,CAAC,KAAK,EAAE,IAAI;QACzB,CAAC,EACD;UACIrG,KAAK,EAAE,mBAAmB;UAC1BqF,GAAG,EAAE,gCAAgC;UACrCI,MAAM,EAAE,UAAU;UAClBkB,WAAW,EAAE,4BAA4B;UACzCV,QAAQ,EAAGW,KAAK,IAAK;YACjB,IAAI,CAAC+G,WAAW,CAAC,CAAC;UACtB;QACJ,CAAC,EACD;UACI9G,MAAM,EAAEA,CAAA,KAAM;YACV,oBACI7J,KAAA,CAAA0I,aAAA;cAAAC,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GACKsH,yBAAyB,IAAI,EAAE,iBAC5BtQ,KAAA,CAAA0I,aAAA;cAAAC,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,gBACIhJ,KAAA,CAAA0I,aAAA,CAACrI,MAAM;cACHyJ,OAAO,EAAGC,CAAC,IACP,IAAI,CAACC,4BAA4B,CAC7BsG,yBACJ,CACH;cACDrG,IAAI,eAAEjK,KAAA,CAAA0I,aAAA,CAAC7H,cAAc;gBAAA8H,MAAA;gBAAAC,QAAA;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAA,CAAE,CAAE;cAAAL,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAC5B,cAEO,CACP,CACR,eACDhJ,KAAA,CAAA0I,aAAA,CAAC5H,IAAI;cACDoJ,EAAE,EACE,uBAAuB,GACvBC,kBAAkB,CACdmG,yBACJ,CACH;cACDlG,MAAM,EAAC,QAAQ;cAAAzB,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAClB,qBACsB,EAAC,KAClB,CACL,CAAC;UAEd;QACJ,CAAC,EACD;UACIa,MAAM,EAAEA,CAAA,kBAAM7J,KAAA,CAAA0I,aAAA;YAAAC,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAQ;QAC1B,CAAC,EACD;UACIX,GAAG,EAAE,4BAA4B;UACjCrF,KAAK,EAAE,4BAA4B;UACnCyF,MAAM,EAAE,QAAQ;UAChBY,OAAO,EAAEvG;QACb,CAAC,EACD;UACIuF,GAAG,EAAE,uCAAuC;UAC5CrF,KAAK,EAAE,6CAA6C;UACpD0H,OAAO,EACH,kHAAkH;UACtHjC,MAAM,EAAE,aAAa;UACrBY,OAAO,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;UACtBJ,QAAQ,EAAEA,CAAA,KAAM;YACZ,IAAI,CAACG,OAAO,CAAC,CAAC;UAClB;QACJ,CAAC,EACD,IAAIqH,iCAAiC,KAAK,KAAK,GACzC,CACI;UACIpI,GAAG,EAAE,6BAA6B;UAClCI,MAAM,EAAGtF,KAAK,iBACVnD,KAAA,CAAA0I,aAAA,CAAClG,cAAc;YACXQ,KAAK,EAAC,8BAA8B;YACpC0H,OAAO,EAAC,gFAAgF;YACxF3H,KAAK,EAAEI,KAAK,CAACJ,KAAM;YACnBkG,QAAQ,EAAGc,CAAC,IACR5G,KAAK,CAAC8F,QAAQ,CAACc,CAAC,CAACK,MAAM,CAACwG,OAAO,CAClC;YAAAjI,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CACJ;QAET,CAAC,CACJ,GACD,EAAE,CAAC,EACT;UACIX,GAAG,EAAE,2CAA2C;UAChDrF,KAAK,EAAE,uCAAuC;UAC9C0H,OAAO,EACH,yHAAyH;UAC7HjC,MAAM,EAAE,aAAa;UACrBY,OAAO,EAAE,CAAC,KAAK,EAAE,IAAI;QACzB,CAAC;MAET,CAAC;MACD,OAAOpB,IAAI;IACf,CAAC;IAAA,KA2DD4I,gCAAgC,GAAG,MAAM;MAAA,IAAAC,cAAA,EAAAC,qBAAA;MACrC,IAAIC,eAAe,IAAAF,cAAA,GACf,IAAI,CAACjJ,OAAO,cAAAiJ,cAAA,wBAAAC,qBAAA,GAAZD,cAAA,CAAchJ,OAAO,cAAAiJ,qBAAA,uBAArBA,qBAAA,CAAuBhJ,aAAa,CAAC,cAAc,CAAC;MACxD,IAAIkJ,gBAAgB,GAAG,EAAE;MACzB,IAAID,eAAe,EAAE;QACjBA,eAAe,CAACpM,OAAO,CAAEsI,UAAU,IAAK;UAAA,IAAAgE,sBAAA;UACpC,MAAMC,QAAQ,IAAAD,sBAAA,GAAG,IAAI,CAACjN,KAAK,CAACT,QAAQ,cAAA0N,sBAAA,uBAAnBA,sBAAA,CAAqB3F,SAAS,CAAC6F,IAAI,CAC/CtE,eAAe,IAAKA,eAAe,CAAC/J,KAAK,KAAKmK,UACnD,CAAC;UACD,IAAIiE,QAAQ,EAAE;YACVF,gBAAgB,CAAC9D,IAAI,CAAC;cAClB9E,GAAG,EAAE8I,QAAQ,CAACpO,KAAK;cACnBC,KAAK,EAAEmO,QAAQ,CAACnO,KAAK;cACrBD,KAAK,EAAEoO,QAAQ,CAACpO;YACpB,CAAC,CAAC;UACN;QACJ,CAAC,CAAC;MACN;MACA,OAAOkO,gBAAgB;IAC3B,CAAC;IAAA,KAEDI,oBAAoB,GAAG,MAAM;MAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA;MACzB,MAAMnK,aAAa,GAAG,IAAI,CAACvD,KAAK,CAACN,QAAQ,IAAA2N,sBAAA,GACnC,IAAI,CAACrN,KAAK,CAACT,QAAQ,cAAA8N,sBAAA,uBAAnBA,sBAAA,CAAqB7L,SAAS,GAC9B,CAAC,CAAC;MAER,IAAImM,sBAAsB,GAAG,EAAE;MAC/B,IAAIC,uBAAuB,GAAG,EAAE;MAChC,IAAIC,qCAAqC,GAAG,EAAE;MAC9C,IAAIC,qBAAqB,IAAAR,sBAAA,GAAG,IAAI,CAACtN,KAAK,CAACT,QAAQ,cAAA+N,sBAAA,uBAAnBA,sBAAA,CAAqBhG,SAAS;MAC1D,IAAIyF,eAAe,IAAAQ,eAAA,GACf,IAAI,CAAC3J,OAAO,cAAA2J,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAc1J,OAAO,cAAA2J,qBAAA,uBAArBA,qBAAA,CAAuB1J,aAAa,CAAC,cAAc,CAAC;MACxD,IAAIiK,mBAAmB,IAAAN,eAAA,GAAG,IAAI,CAAC7J,OAAO,cAAA6J,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAc5J,OAAO,cAAA6J,qBAAA,uBAArBA,qBAAA,CAAuB5J,aAAa,CAC1D,yCACJ,CAAC;MACD,IAAIiJ,eAAe,EAAE;QACjBA,eAAe,CAACpM,OAAO,CAAEsI,UAAU,IAAK;UAAA,IAAA+E,qBAAA,EAAAC,sBAAA;UACpC,IAAIC,iBAAiB,GAAGJ,qBAAqB,aAArBA,qBAAqB,wBAAAE,qBAAA,GAArBF,qBAAqB,CAAE9E,MAAM,CAChDH,eAAe,IAAKA,eAAe,CAAC/J,KAAK,IAAImK,UAClD,CAAC,cAAA+E,qBAAA,wBAAAC,sBAAA,GAFuBD,qBAAA,CAEpB,CAAC,CAAC,cAAAC,sBAAA,uBAFkBA,sBAAA,CAEhBlP,KAAK;UAEb,IAAIoP,QAAQ,GAAG;YACXpP,KAAK,EAAEmP,iBAAiB;YACxB9J,GAAG,EAAE6E,UAAU,GAAG,yCAAyC;YAC3DzE,MAAM,EAAE;UACZ,CAAC;UACDqJ,qCAAqC,CAAC3E,IAAI,CAACiF,QAAQ,CAAC;UAEpD,IAAIC,yBAAyB,GAAG;YAC5BrP,KAAK,EACDmP,iBAAiB,GACjB,yBAAyB,GACzBA,iBAAiB,GACjB,yDAAyD;YAC7D9J,GAAG,EAAE,iBAAiB,GAAG6E,UAAU,GAAG,kBAAkB;YACxDzE,MAAM,EAAE,QAAQ;YAChBY,OAAO,EAAE,IAAI,CAACiJ,2CAA2C,CACrD,IAAI,EACJ,EAAE,EACF,IACJ,CAAC;YACDhJ,WAAW,EAAE;cACTC,IAAI,EAAE,UAAU;cAChBe,UAAU,EAAE,IAAI;cAChBgB,UAAU,EAAE,IAAI;cAChB9B,gBAAgB,EAAE;YACtB;YACA;YACA;YACA;YACA;UACJ,CAAC;UACDoI,sBAAsB,CAACzE,IAAI,CAACkF,yBAAyB,CAAC;QAC1D,CAAC,CAAC;QACF,IAAI,CAAArB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEnE,MAAM,IAAG,CAAC,EAAE;UAC7B,IAAI0F,GAAG,GAAG;YACNvP,KAAK,EAAE,yCAAyC;YAChDqF,GAAG,EAAE,yCAAyC;YAC9CI,MAAM,EAAE,UAAU;YAClBQ,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACG,OAAO,CAAC;UACjC,CAAC;UACDyI,uBAAuB,CAAC1E,IAAI,CAACoF,GAAG,CAAC;QACrC;QAEA,IAAIP,mBAAmB,IAAI,CAAAhB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEnE,MAAM,IAAG,CAAC,EAAE;UACpD,IAAItI,IAAI,GAAG;YACPvB,KAAK,EAAE,mCAAmC;YAC1CqF,GAAG,EAAE,8CAA8C;YACnDI,MAAM,EAAE,QAAQ;YAChBa,WAAW,EAAE;cACTC,IAAI,EAAE,UAAU;cAChBe,UAAU,EAAE,IAAI;cAChBd,gBAAgB,EAAE;YACtB,CAAC;YACDH,OAAO,EAAE,IAAI,CAACwH,gCAAgC,CAAC;UACnD,CAAC;UACDgB,uBAAuB,CAAC1E,IAAI,CAAC5I,IAAI,CAAC;QACtC;MACJ;MAEA,IAAIiO,yCAAyC,GAAG,EAAE;MAClD,IAAIV,qCAAqC,CAACjF,MAAM,GAAG,CAAC,EAAE;QAClD,IAAIuF,QAAQ,GAAG;UACXvI,MAAM,EAAEA,CAAA,KAAM;YACV,oBACI7J,KAAA,CAAA0I,aAAA,CAAClI,KAAK;cACFkJ,OAAO,EAAC,iDAAiD;cACzD+I,IAAI,EAAC,MAAM;cAAA9J,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CACd,CAAC;UAEV;QACJ,CAAC;QACDwJ,yCAAyC,CAACrF,IAAI,CAACiF,QAAQ,CAAC;MAC5D;MAEA,MAAMnK,IAAI,GAAG;QACTC,OAAO,EAAE,CAAC;QACVC,cAAc,EAAE,IAAI;QACpBX,aAAa,EAAEA,aAAa;QAC5BY,MAAM,EAAE,CACJ;UACIC,GAAG,EAAE,cAAc;UACnBrF,KAAK,EAAE,cAAc;UACrByF,MAAM,EAAE,QAAQ;UAChBa,WAAW,EAAE;YACTC,IAAI,EAAE,UAAU;YAChBe,UAAU,EAAE,IAAI;YAChBd,gBAAgB,EAAE;UACtB,CAAC;UACDH,OAAO,EAAE,IAAI,CAACpF,KAAK,CAACT,QAAQ,CAAC+H,SAAS;UACtC5B,WAAW,EAAE,oBAAoB;UACjCV,QAAQ,EAAGlG,KAAK,IAAK;YACjB,MAAM2P,iCAAiC,GACnC,IAAI,CAAC7K,OAAO,CAACC,OAAO,CAACC,aAAa,CAC9B,8CACJ,CAAC,IAAI,EAAE;YACX;YACA,MAAM4K,0BAA0B,GAC5BD,iCAAiC,CAACzF,MAAM,CAAE2F,IAAI,IAC1C7P,KAAK,CAAC8P,QAAQ,CAACD,IAAI,CACvB,CAAC;YACL;YACA,IAAI,CAAC/K,OAAO,CAACC,OAAO,CAACoB,cAAc,CAAC;cAChC4J,4CAA4C,EACxCH;YACR,CAAC,CAAC;YACF,IAAI,CAACvJ,OAAO,CAAC,CAAC;UAClB;QACJ,CAAC,EACD,GAAGoJ,yCAAyC,EAC5C,GAAGV,qCAAqC,EACxC,GAAGF,sBAAsB,EACzB,GAAGC,uBAAuB;MAElC,CAAC;MACD,OAAO5J,IAAI;IACf,CAAC;IAAA,KA+WD8K,6BAA6B,GAAG,MAAM;MAClC,OAAO;QACHvP,QAAQ,EAAE,IAAI,CAACS,KAAK,CAACT,QAAQ;QAC7BqE,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBlE,QAAQ,EAAE,IAAI,CAACM,KAAK,CAACN,QAAQ;QAC7B+F,OAAO,EAAE,wDAAwD;QACjEsJ,kCAAkC,EAAE,cAAc;QAClDC,WAAW,EAAE,WAAW;QACxBC,WAAW,EAAE,sBAAsB;QACnCC,OAAO,EAAE,WAAW;QACpBC,aAAa,EAAE,iBAAiB;QAChChK,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACA,OAAO,CAAC,CAAC;QAC7BiK,cAAc,EAAE,cAAc;QAC9BC,uBAAuB,EAAE,eAAe;QACxCC,aAAa,EAAE,iBAAiB;QAChCC,WAAW,EAAE;MACjB,CAAC;IACL,CAAC;IAAA,KAEDC,4BAA4B,GAAG,MAAM;MACjC,OAAO;QACHjQ,QAAQ,EAAE,IAAI,CAACS,KAAK,CAACT,QAAQ;QAC7BqE,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBlE,QAAQ,EAAE,IAAI,CAACM,KAAK,CAACN,QAAQ;QAC7B+F,OAAO,EAAE,IAAI,CAACgK,eAAe,CAAC,CAAC,GACzB,uDAAuD,GACvD,0DAA0D;QAChEV,kCAAkC,EAAE,2BAA2B;QAC/DC,WAAW,EAAE,mBAAmB;QAChCC,WAAW,EAAE,qBAAqB;QAClCC,OAAO,EAAE,UAAU;QACnBC,aAAa,EAAE,+BAA+B;QAC9ChK,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACA,OAAO,CAAC,CAAC;QAC7BiK,cAAc,EAAE,cAAc;QAC9BC,uBAAuB,EAAE,6BAA6B;QACtDK,6BAA6B,EACzB,qCAAqC;QACzCJ,aAAa,EAAE,+BAA+B;QAC9CC,WAAW,EAAE,kCAAkC;QAC/CI,cAAc,EAAE,IAAI,CAACF,eAAe,CAAC,CAAC;QACtCG,yBAAyB,EAAE,4BAA4B;QACvDC,0BAA0B,EAAE,6BAA6B;QACzDC,4BAA4B,EACxB,2CAA2C;QAC/CC,2BAA2B,EACvB;MACR,CAAC;IACL,CAAC;IAAA,KAQDC,iBAAiB,GAAG,MAAM;MAAA,IAAAC,eAAA,EAAAC,qBAAA;MACtB,IAAIC,oBAAoB,GAAG,EAAE;MAC7B,IACI,IAAI,CAACvM,OAAO,MAAAqM,eAAA,GACZ,IAAI,CAACrM,OAAO,cAAAqM,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAcpM,OAAO,cAAAqM,qBAAA,uBAArBA,qBAAA,CAAuBE,cAAc,CAAC,6BAA6B,CAAC,GACtE;QACED,oBAAoB,GAAG,IAAI,CAACE,qBAAqB,CAAC,CAAC;MACvD;MAEA,OAAO;QACHlM,MAAM,EAAE,CACJ;UACIC,GAAG,EAAE,2BAA2B;UAChCwB,MAAM,EAAEA,CAAA,KAAM;YAAA,IAAA0K,sBAAA;YACV,oBACIvU,KAAA,CAAA0I,aAAA,CAACnI,IAAI;cACDiU,gBAAgB,EAAC,WAAW;cAC5BnK,SAAS,EAAC,SAAS;cAAA1B,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,gBAEnBhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;cAACC,GAAG,EAAC,WAAW;cAACrM,GAAG,EAAC,WAAW;cAAAM,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,gBACzChJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;cACRkU,IAAI,EAAE,IAAI,CAAC9M,OAAQ;cACnBI,IAAI,EAAE;gBACFE,cAAc,EAAE,IAAI;gBACpBC,MAAM,EAAE,CACJ;kBACIpF,KAAK,EAAE,iBAAiB;kBACxBqF,GAAG,EAAE,6BAA6B;kBAClCI,MAAM,EAAE,QAAQ;kBAChBY,OAAO,EAAE,CACL,KAAAkL,sBAAA,GAAG,IAAI,CAACtQ,KAAK,CAACT,QAAQ,cAAA+Q,sBAAA,uBAAnBA,sBAAA,CACG/E,wBAAwB,EACjC;kBACDlG,WAAW,EAAE;oBACTC,IAAI,EAAE,UAAU;oBAChBe,UAAU,EAAE,IAAI;oBAChBgB,UAAU,EAAE,IAAI;oBAChB9B,gBAAgB,EACZ,UAAU;oBACdP,QAAQ,EAAEA,CAAA,KACN,IAAI,CAACG,OAAO,CAAC;kBACrB;gBACJ,CAAC,EACD,GAAGgL,oBAAoB;cAE/B,CAAE;cAAAzL,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CACL,CACS,CACZ,CAAC;UAEf;QACJ,CAAC;MAET,CAAC;IACL,CAAC;IAv5EG,IAAI,CAACnB,OAAO,GAAG7H,KAAK,CAAC4U,SAAS,CAAC,CAAC;EACpC;EAiBAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACC,YAAY,CAAC,CAAC;EACvB;EAEAA,YAAYA,CAAA,EAAG;IACX,IACK,IAAI,CAAC7Q,KAAK,CAACN,QAAQ,IAAI,IAAI,CAACM,KAAK,CAACX,OAAO,IACzC,CAAC,IAAI,CAACW,KAAK,CAACN,QAAQ,IACjB,IAAI,CAACM,KAAK,CAACT,QAAQ,IAAIC,SAAS,IAChC,CAAC,IAAI,CAACQ,KAAK,CAACP,iBAAkB,EACpC;MACE,IAAI,CAACS,QAAQ,CAAC;QACVT,iBAAiB,EAAE;MACvB,CAAC,CAAC;MACF,IAAIc,MAAM,GAAG,CAAC,CAAC;MACf,MAAMgC,UAAU,GAAIC,IAAI,IAAK;QACzB,IAAI,CAACtC,QAAQ,CAAC;UACVT,iBAAiB,EAAE,KAAK;UACxBF,QAAQ,EAAEiD,IAAI,CAAClC,IAAI;UACnBX,KAAK,EAAE;QACX,CAAC,CAAC;MACN,CAAC;MACD,MAAMgD,OAAO,GAAIhD,KAAK,IAAK;QACvB;QACA,IAAI,CAACO,QAAQ,CAAC;UACVT,iBAAiB,EAAE,KAAK;UACxBE,KAAK,EAAElD,UAAU,CAACmG,oBAAoB,CAACjD,KAAK;QAChD,CAAC,CAAC;MACN,CAAC;MACD,IAAImR,GAAG,GAAG,CAAC,IAAI,CAAC9Q,KAAK,CAACN,QAAQ,GACxBlB,QAAQ,GACRA,QAAQ,GAAG,GAAG,GAAG,IAAI,CAACU,KAAK,CAAC4D,UAAU,CAACC,EAAE;MAC/C;MACAtG,UAAU,CAACsU,cAAc,CAACD,GAAG,EAAEvQ,MAAM,EAAEgC,UAAU,EAAEI,OAAO,CAAC;IAC/D;EACJ;EAEAqO,kBAAkBA,CAACC,SAAS,EAAEC,SAAS,EAAE;IACrC,IACID,SAAS,CAACnO,UAAU,IAAI,IAAI,CAAC5D,KAAK,CAAC4D,UAAU,IAC7CmO,SAAS,CAACE,UAAU,IAAI,IAAI,CAACjS,KAAK,CAACiS,UAAU,EAC/C;MACE,IAAI,CAACjR,QAAQ,CACT;QACId,aAAa,EAAE,CAAC,IAAI,CAACY,KAAK,CAACZ,aAAa;QACxCC,OAAO,EAAE,IAAI,CAACH,KAAK,CAACiS;MACxB,CAAC,EACD,YAAY;QACR,IAAI,IAAI,CAACjS,KAAK,CAACiS,UAAU,IAAI,IAAI,CAACnR,KAAK,CAACN,QAAQ,EAAE;UAC9C,IAAI,CAACmR,YAAY,CAAC,CAAC;QACvB;MACJ,CACJ,CAAC;IACL,CAAC,MAAM;MACH,IAAI,IAAI,CAAC7Q,KAAK,CAACoR,eAAe,EAAE;QAC5B,IAAI,CAAClR,QAAQ,CACT;UACIkR,eAAe,EAAE;QACrB,CAAC,EACD,IAAI,CAACP,YAAY,CAAC,CACtB,CAAC;MACL;IACJ;EACJ;EAUA1Q,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACjB,KAAK,CAACmS,OAAO,IAAI7R,SAAS,EAAE;MACjC,IAAI,CAACN,KAAK,CAACmS,OAAO,CAAC,CAAC;IACxB;IACA,IAAI,CAACnR,QAAQ,CAAC;MACVkR,eAAe,EAAE,IAAI;MACrB,GAAG,IAAI,CAACjS;IACZ,CAAC,CAAC;EACN;EAEAsD,uBAAuBA,CAACC,QAAQ,EAAE;IAC9B,IAAI,IAAI,CAACxD,KAAK,CAACoS,cAAc,IAAI9R,SAAS,EAAE;MACxC,IAAI,CAACN,KAAK,CAACoS,cAAc,CAAC5O,QAAQ,CAAC;IACvC;EACJ;EA8GA6O,mBAAmBA,CAACC,UAAU,EAAE;IAC5B,IAAI,CAACtR,QAAQ,CAAC;MAAEH,UAAU,EAAEyR;IAAW,CAAC,CAAC;EAC7C;EAEAC,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,eAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAChB,KAAAJ,eAAA,GAAI,IAAI,CAAC9N,OAAO,cAAA8N,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAc7N,OAAO,cAAA8N,qBAAA,uBAArBA,qBAAA,CAAuB7N,aAAa,EAAE;MACtC,MAAMiO,SAAS,GAAG,IAAI,CAACnO,OAAO,CAACC,OAAO,CAACC,aAAa,CAChD,4BACJ,CAAC;MACD,IAAIiO,SAAS,EAAE;QACX,OAAOtR,IAAI,CAACC,KAAK,CAACqR,SAAS,CAAC;MAChC;IACJ;IACA,KAAAH,sBAAA,GACI,IAAI,CAAC5R,KAAK,CAACT,QAAQ,cAAAqS,sBAAA,wBAAAC,sBAAA,GAAnBD,sBAAA,CAAqBpQ,SAAS,cAAAqQ,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgCrQ,SAAS,cAAAsQ,sBAAA,uBAAzCA,sBAAA,CACME,0BAA0B,EAClC;MAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACE,OAAO1R,IAAI,CAACC,KAAK,EAAAuR,sBAAA,GACb,IAAI,CAACjS,KAAK,CAACT,QAAQ,cAAA0S,sBAAA,wBAAAC,sBAAA,GAAnBD,sBAAA,CAAqBzQ,SAAS,cAAA0Q,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgC1Q,SAAS,cAAA2Q,sBAAA,uBAAzCA,sBAAA,CACMH,0BACV,CAAC;IACL;IACA,OAAO,CAAC,CAAC;EACb;EAEAI,oBAAoBA,CAAA,EAAG;IAAA,IAAAC,eAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACnB;IACA,KAAAJ,eAAA,GAAI,IAAI,CAACzO,OAAO,cAAAyO,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAcxO,OAAO,cAAAyO,qBAAA,uBAArBA,qBAAA,CAAuBxO,aAAa,EAAE;MACtC,IAAIiO,SAAS,GAAG,IAAI,CAACnO,OAAO,CAACC,OAAO,CAACC,aAAa,CAC9C,6BACJ,CAAC;MACD;MACA,IAAIiO,SAAS,IAAIvS,SAAS,EAAE;QACxBuS,SAAS,GACL,2GAA2G;MACnH;MACA,IAAIA,SAAS,EAAE;QACX,OAAOtR,IAAI,CAACC,KAAK,CAACqR,SAAS,CAAC;MAChC;IACJ;IACA,KAAAQ,sBAAA,GACI,IAAI,CAACvS,KAAK,CAACT,QAAQ,cAAAgT,sBAAA,wBAAAC,sBAAA,GAAnBD,sBAAA,CAAqB/Q,SAAS,cAAAgR,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgChR,SAAS,cAAAiR,sBAAA,uBAAzCA,sBAAA,CACMC,2BAA2B,EACnC;MAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACE,OAAOpS,IAAI,CAACC,KAAK,EAAAiS,sBAAA,GACb,IAAI,CAAC3S,KAAK,CAACT,QAAQ,cAAAoT,sBAAA,wBAAAC,sBAAA,GAAnBD,sBAAA,CAAqBnR,SAAS,cAAAoR,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgCpR,SAAS,cAAAqR,sBAAA,uBAAzCA,sBAAA,CACMH,2BACV,CAAC;IACL;IACA,OAAO,CAAC,CAAC;EACb;EAEAI,oBAAoBA,CAAA,EAAG;IAAA,IAAAC,eAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACnB,IAAIpB,SAAS,IAAAgB,eAAA,GAAG,IAAI,CAACnP,OAAO,cAAAmP,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAclP,OAAO,cAAAmP,qBAAA,uBAArBA,qBAAA,CAAuBlP,aAAa,CAChD,4BACJ,CAAC;IAED,IAAI,CAACiO,SAAS,EAAE;MACZA,SAAS,GACL,iEAAiE;IACzE;IAEA,IAAIA,SAAS,EAAE;MACX,OAAOtR,IAAI,CAACC,KAAK,CAACqR,SAAS,CAAC;IAChC;IAEA,KAAAkB,sBAAA,GACI,IAAI,CAACjT,KAAK,CAACT,QAAQ,cAAA0T,sBAAA,wBAAAC,sBAAA,GAAnBD,sBAAA,CAAqBzR,SAAS,cAAA0R,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgC1R,SAAS,cAAA2R,sBAAA,uBAAzCA,sBAAA,CACMC,0BAA0B,EAClC;MAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACE,OAAO9S,IAAI,CAACC,KAAK,EAAA2S,sBAAA,GACb,IAAI,CAACrT,KAAK,CAACT,QAAQ,cAAA8T,sBAAA,wBAAAC,sBAAA,GAAnBD,sBAAA,CAAqB7R,SAAS,cAAA8R,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgC9R,SAAS,cAAA+R,sBAAA,uBAAzCA,sBAAA,CACMH,0BACV,CAAC;IACL;IACA,OAAO,CAAC,CAAC;EACb;EAEAI,8BAA8BA,CAAA,EAAG;IAC7B,MAAMC,cAAc,GAAG,IAAI,CAAChC,iBAAiB,CAAC,CAAC;IAC/C,IAAIiC,gCAAgC,GAAG,EAAE;IACzC,IAAIC,8BAA8B,GAAG,EAAE;IACvC,IAAIC,MAAM,CAACC,IAAI,CAACJ,cAAc,CAAC,CAAC7K,MAAM,GAAG,CAAC,EAAE;MAAA,IAAAkL,sBAAA,EAAAC,sBAAA;MACxCL,gCAAgC,CAACxK,IAAI,CAAC;QAClC9E,GAAG,EAAE,kCAAkC;QACvCrF,KAAK,eACDhD,KAAA,CAAA0I,aAAA;UAAM4E,KAAK,EAAE;YAAE2K,SAAS,EAAE;UAAG,CAAE;UAAAtP,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAC1B,GAAG,EAAC,mCAC4B,EAAC,GAChC,CACT;QACDP,MAAM,EAAE,QAAQ;QAChBa,WAAW,EAAE;UACTC,IAAI,EAAE,UAAU;UAChBe,UAAU,EAAE,IAAI;UAChBgB,UAAU,EAAE,IAAI;UAChB9B,gBAAgB,EAAE;QACtB,CAAC;QACDH,OAAO,EAAE,EAAA0O,sBAAA,OAAI,CAAC9T,KAAK,CAACT,QAAQ,cAAAuU,sBAAA,uBAAnBA,sBAAA,CAAqBxM,SAAS,KAAI;MAC/C,CAAC,CAAC;MACFqM,8BAA8B,CAACzK,IAAI,CAAC;QAChC9E,GAAG,EAAE,gCAAgC;QACrCrF,KAAK,eAAEhD,KAAA,CAAA0I,aAAA;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAM,iCAAqC,CAAC;QACnDb,cAAc,EAAE,IAAI;QACpBM,MAAM,EAAE,QAAQ;QAChBa,WAAW,EAAE;UACTC,IAAI,EAAE,UAAU;UAChBe,UAAU,EAAE,IAAI;UAChBgB,UAAU,EAAE,IAAI;UAChB9B,gBAAgB,EAAE;QACtB,CAAC;QACDH,OAAO,EAAE,EAAA2O,sBAAA,OAAI,CAAC/T,KAAK,CAACT,QAAQ,cAAAwU,sBAAA,uBAAnBA,sBAAA,CAAqBzM,SAAS,KAAI;MAC/C,CAAC,CAAC;IACN;IACA,MAAMtD,IAAI,GAAG;MACTC,OAAO,EAAE,CAAC;MACVC,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE,CACJ;QACIpF,KAAK,EAAE,oCAAoC;QAC3CqF,GAAG,EAAE,4BAA4B;QACjCgF,aAAa,EAAE;UACXC,KAAK,EAAE;YACHC,OAAO,EAAE;UACb;QACJ;MACJ,CAAC,EACD;QACIlF,GAAG,EAAE,YAAY;QACjBwB,MAAM,EAAEA,CAAA,kBACJ7J,KAAA,CAAA0I,aAAA,CAAA1I,KAAA,CAAAkM,QAAA,qBACIlM,KAAA,CAAA0I,aAAA,CAAC3H,wBAAwB;UACrB4T,IAAI,EAAE,IAAI,CAAC9M,OAAQ;UACnBoB,QAAQ,EAAG0C,MAAM,IAAK;YAClB,IAAI,CAAC9D,OAAO,CAACC,OAAO,CAACoB,cAAc,CAAC;cAChC+M,0BAA0B,EACtBvR,IAAI,CAACmH,SAAS,CAACF,MAAM;YAC7B,CAAC,CAAC;YACF,IAAI,CAACvC,OAAO,CAAC,CAAC;UAClB,CAAE;UACF0C,SAAS,EAAE4L,cAAe;UAAA/O,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAC7B,CACH;MAEV,CAAC,EACD,GAAG2O,gCAAgC,EACnC,GAAGC,8BAA8B,EACjC;QACIvP,GAAG,EAAE,aAAa;QAClBwB,MAAM,EAAEA,CAAA,kBACJ7J,KAAA,CAAA0I,aAAA;UAAK2B,SAAS,EAAC,oBAAoB;UAAA1B,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,gBAC/BhJ,KAAA,CAAA0I,aAAA;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAQ,CACP;MAEb,CAAC;IAET,CAAC;IACD,OAAOf,IAAI;EACf;EAEAR,gBAAgBA,CAAA,EAAG;IAAA,IAAAyQ,sBAAA,EAAAC,sBAAA;IACf,OAAO,IAAI,CAAClU,KAAK,CAACN,QAAQ,IAAAuU,sBAAA,GACpB,IAAI,CAACjU,KAAK,CAACT,QAAQ,cAAA0U,sBAAA,wBAAAC,sBAAA,GAAnBD,sBAAA,CAAqBzS,SAAS,cAAA0S,sBAAA,uBAA9BA,sBAAA,CAAgC1S,SAAS,GACzC,CAAC,CAAC;EACZ;EAEAgE,uBAAuBA,CAAA,EAAG;IAAA,IAAA2O,eAAA,EAAAC,qBAAA;IACtB,IAAIC,eAAe,IAAAF,eAAA,GACf,IAAI,CAACvQ,OAAO,cAAAuQ,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAActQ,OAAO,cAAAuQ,qBAAA,uBAArBA,qBAAA,CAAuBtQ,aAAa,CAAC,iBAAiB,CAAC;IAE3D,IAAIwQ,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACtU,KAAK,CAACT,QAAQ,CAAC+U,QAAQ,CAAC;IAChD,IAAIC,iBAAiB,GAAG,EAAE;IAC1BA,iBAAiB,GAAGD,QAAQ,CAACtL,MAAM,CAAEwL,oBAAoB,IAAK;MAC1D,OAAOA,oBAAoB,CAACC,gBAAgB,IAAIJ,eAAe;IACnE,CAAC,CAAC;IACF,OAAOE,iBAAiB;EAC5B;EACA7N,+BAA+BA,CAAA,EAAG;IAC9B,IAAIgO,MAAM,GAAG,sBAAsB;IACnC,OAAO,CACH;MACItQ,GAAG,EAAE,GAAGsQ,MAAM,YAAY;MAC1B3V,KAAK,EAAE,6BAA6B;MACpCqK,aAAa,EAAE;QACXC,KAAK,EAAE;UACHC,OAAO,EAAE;QACb;MACJ;IACJ,CAAC,EACD;MACIlF,GAAG,EAAE,GAAGsQ,MAAM,EAAE;MAChB3V,KAAK,EAAE,6BAA6B;MACpCyF,MAAM,EAAE,QAAQ;MAChBa,WAAW,EAAE;QACTC,IAAI,EAAE;MACV,CAAC;MACDF,OAAO,EAAE,IAAI,CAACuP,yBAAyB,CAAC,CAAC;MACzC3P,QAAQ,EAAEA,CAAC4P,aAAa,EAAEC,cAAc,KAAK;QACzC,IAAIC,iBAAiB,GAAG,EAAE;QAC1BD,cAAc,CAACE,GAAG,CAAEC,UAAU,IAAK;UAC/BF,iBAAiB,CAAC5L,IAAI,CAAC;YACnB+L,cAAc,EAAED,UAAU,CAACjW,KAAK;YAChCqF,GAAG,EAAE4Q,UAAU,CAAC5Q;UACpB,CAAC,CAAC;QACN,CAAC,CAAC;QACF,IAAI,CAACR,OAAO,CAACC,OAAO,CAACoB,cAAc,CAAC;UAChCiQ,8BAA8B,EAAEJ;QACpC,CAAC,CAAC;MACN;IACJ,CAAC,CACJ;EACL;EAgNAK,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACnV,KAAK,CAACN,QAAQ,EAAE;MAAA,IAAA0V,eAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACrB,KAAAJ,eAAA,GAAI,IAAI,CAACxR,OAAO,cAAAwR,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAcvR,OAAO,cAAAwR,qBAAA,uBAArBA,qBAAA,CAAuBvR,aAAa,EAAE;QACtC,MAAMiO,SAAS,GAAG,IAAI,CAACnO,OAAO,CAACC,OAAO,CAACC,aAAa,CAChD,wCACJ,CAAC;QACD,IAAIiO,SAAS,EAAE;UACX,OAAOtR,IAAI,CAACC,KAAK,CAACqR,SAAS,CAAC;QAChC;MACJ;MACA,MAAM0D,mBAAmB,IAAAH,sBAAA,GACrB,IAAI,CAACtV,KAAK,CAACT,QAAQ,cAAA+V,sBAAA,wBAAAC,sBAAA,GAAnBD,sBAAA,CAAqB9T,SAAS,cAAA+T,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgC/T,SAAS,cAAAgU,sBAAA,uBAAzCA,sBAAA,CACME,sCAAsC;MAChD,OAAOD,mBAAmB,GAAGhV,IAAI,CAACC,KAAK,CAAC+U,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACrE;IACA,OAAO,CAAC,CAAC;EACb;EAEAE,qBAAqBA,CAACrV,IAAI,EAAEsV,oBAAoB,EAAE;IAC9C,IAAIC,iBAAiB,GAAG,IAAI,CAACV,gBAAgB,CAAC,CAAC;IAC/CU,iBAAiB,CAACD,oBAAoB,CAAC,GAAGtV,IAAI;IAC9C,IAAI,CAACsD,OAAO,CAACC,OAAO,CAACoB,cAAc,CAAC;MAChCyQ,sCAAsC,EAClCjV,IAAI,CAACmH,SAAS,CAACiO,iBAAiB;IACxC,CAAC,CAAC;EACN;EAEAC,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAAC9V,KAAK,CAACN,QAAQ,EAAE;MAAA,IAAAqW,eAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACrB,KAAAJ,eAAA,GAAI,IAAI,CAACnS,OAAO,cAAAmS,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAclS,OAAO,cAAAmS,qBAAA,uBAArBA,qBAAA,CAAuBlS,aAAa,EAAE;QACtC,MAAMiO,SAAS,GAAG,IAAI,CAACnO,OAAO,CAACC,OAAO,CAACC,aAAa,CAChD,qCACJ,CAAC;QACD,IAAIiO,SAAS,EAAE;UACX,OAAOtR,IAAI,CAACC,KAAK,CAACqR,SAAS,CAAC;QAChC;MACJ;MACA,MAAM0D,mBAAmB,IAAAQ,sBAAA,GACrB,IAAI,CAACjW,KAAK,CAACT,QAAQ,cAAA0W,sBAAA,wBAAAC,sBAAA,GAAnBD,sBAAA,CAAqBzU,SAAS,cAAA0U,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgC1U,SAAS,cAAA2U,sBAAA,uBAAzCA,sBAAA,CACMC,mCAAmC;MAC7C,OAAOX,mBAAmB,GAAGhV,IAAI,CAACC,KAAK,CAAC+U,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACrE;IACA,OAAO,CAAC,CAAC;EACb;EAEAY,6BAA6BA,CAAC/V,IAAI,EAAE;IAChC,IAAIgW,yBAAyB,GAAG,IAAI,CAACR,wBAAwB,CAAC,CAAC;IAC/DQ,yBAAyB,CAAC,qCAAqC,CAAC,GAAGhW,IAAI;IACvE,IAAI,CAACsD,OAAO,CAACC,OAAO,CAACoB,cAAc,CAAC;MAChCmR,mCAAmC,EAAE3V,IAAI,CAACmH,SAAS,CAC/C0O,yBACJ;IACJ,CAAC,CAAC;EACN;EAEAC,6BAA6BA,CAAA,EAAG;IAAA,IAAAC,eAAA,EAAAC,qBAAA,EAAAC,qBAAA;IAC5B,IAAIC,YAAY,GAAG,EAAE;IACrB,IAAIC,WAAW,GACX,EAAAJ,eAAA,OAAI,CAAC5S,OAAO,cAAA4S,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAc3S,OAAO,cAAA4S,qBAAA,uBAArBA,qBAAA,CAAuB3S,aAAa,CAAC,cAAc,CAAC,OAAA4S,qBAAA,GACpD,IAAI,CAAClT,gBAAgB,CAAC,CAAC,cAAAkT,qBAAA,uBAAvBA,qBAAA,CAAyBxR,YAAY;IACzC,IAAIoP,QAAQ,GAAG,IAAI,CAACtU,KAAK,CAACT,QAAQ,CAAC+U,QAAQ;IAC3C,IAAIuC,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,IAAIA,WAAW,CAAChO,MAAM,GAAG,CAAC,EAAE;MACtDgO,WAAW,CAACjW,OAAO,CAAEoW,gBAAgB,IAAK;QAAA,IAAAC,gBAAA;QACtC,IAAIC,gBAAgB,IAAAD,gBAAA,GAAG1C,QAAQ,CAACtL,MAAM,CACjCkO,SAAS,IAAKA,SAAS,CAACpY,KAAK,IAAIiY,gBACtC,CAAC,cAAAC,gBAAA,uBAFsBA,gBAAA,CAEnB,CAAC,CAAC;QACNL,YAAY,CAACzN,IAAI,CAAC+N,gBAAgB,CAAC;MACvC,CAAC,CAAC;IACN;IACA,OAAON,YAAY;EACvB;EAEAQ,0BAA0BA,CAAC5T,aAAa,EAAE;IACtC,IAAI6T,kBAAkB,GAAG,IAAI,CAAC3F,iBAAiB,CAAC,CAAC;IACjD,IAAIoE,iBAAiB,GAAG,IAAI,CAACV,gBAAgB,CAAC,CAAC;IAC/C,IAAImB,yBAAyB,GAAG,IAAI,CAACR,wBAAwB,CAAC,CAAC;IAC/D,OAAO;MACH3R,MAAM,EAAE,CACJ;QACIpF,KAAK,EAAE,gDAAgD;QACvDqF,GAAG,EAAE,wCAAwC;QAC7CgF,aAAa,EAAE;UACXC,KAAK,EAAE;YACHC,OAAO,EAAE;UACb;QACJ;MACJ,CAAC,EACD;QACIvK,KAAK,EAAE,uDAAuD;QAC9DqF,GAAG,EAAE,qCAAqC;QAC1CgF,aAAa,EAAE;UACXC,KAAK,EAAE;YACHC,OAAO,EAAE;UACb;QACJ;MACJ,CAAC,EACD;QACIlF,GAAG,EAAE,oCAAoC;QACzCwB,MAAM,EAAEA,CAAA,KAAM;UAAA,IAAAyR,sBAAA;UACV,oBACItb,KAAA,CAAA0I,aAAA,CAACnI,IAAI;YAACiU,gBAAgB,EAAC,WAAW;YAAA7L,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAC7B6O,MAAM,CAACC,IAAI,CAACuD,kBAAkB,CAAC,CAACxO,MAAM,GAAG,CAAC,iBACvC7M,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;YACTC,GAAG,EAAC,YAAY;YAChBrM,GAAG,EAAC,WAAW;YAAAM,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,gBAEfhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI;YAACiU,gBAAgB,EAAC,WAAW;YAAA7L,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAC7B6O,MAAM,CAACC,IAAI,CACRuD,kBACJ,CAAC,CAACrC,GAAG,CACD,CACIa,oBAAoB,EACpB0B,KAAK;YAAA,IAAAC,qBAAA;YAAA,oBAELxb,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;cACTpM,GAAG,EAAEkT,KAAM;cACX7G,GAAG,EACC2G,kBAAkB,aAAlBA,kBAAkB,wBAAAG,qBAAA,GAAlBH,kBAAkB,CACdxB,oBAAoB,CACvB,cAAA2B,qBAAA,uBAFDA,qBAAA,CAEGxY,KACN;cAAA2F,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,gBAEDhJ,KAAA,CAAA0I,aAAA,CAAC1H,aAAa;cACVya,mBAAmB,EACf3B,iBAAiB,CACbD,oBAAoB,CAE3B;cACD6B,oBAAoB,EAChBlU,aACH;cACDmN,IAAI,EAAE,IAAI,CAAC9M,OAAQ;cACnB8T,WAAW,EACPN,kBAAkB,CACdxB,oBAAoB,CAE3B;cACD5Q,QAAQ,EACJ1E,IAAI,IACH;gBACD;gBACA,IAAI,CAACqV,qBAAqB,CACtBrV,IAAI,EACJsV,oBACJ,CAAC;cACL,CAAE;cACFlW,QAAQ,EACJ,IAAI,CAACM,KAAK,CACLN,QACR;cACD4U,QAAQ,EAAE,IAAI,CAACiC,6BAA6B,CAAC,CAAE;cAAA7R,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CAClD,CACS,CAAC;UAAA,CAEvB,CACE,CACI,CACjB,eACDhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;YAACC,GAAG,EAAC,QAAQ;YAAA/L,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,gBACtBhJ,KAAA,CAAA0I,aAAA,CAAC1H,aAAa;YACV4a,QAAQ;YACRH,mBAAmB,EACflB,yBAAyB,aAAzBA,yBAAyB,uBAAzBA,yBAAyB,CAAEF,mCAC9B;YACDqB,oBAAoB,EAAElU,aAAc;YACpCmN,IAAI,EAAE,IAAI,CAAC9M,OAAQ;YACnB8T,WAAW,GAAAL,sBAAA,GACP,IAAI,CAACrX,KAAK,CAACT,QAAQ,cAAA8X,sBAAA,uBAAnBA,sBAAA,CACM7Q,iBACT;YACDxB,QAAQ,EAAG1E,IAAI,IAAK;cAChB;cACA,IAAI,CAAC+V,6BAA6B,CAC9B/V,IACJ,CAAC;YACL,CAAE;YACFZ,QAAQ,EAAE,IAAI,CAACM,KAAK,CAACN,QAAS;YAC9B4U,QAAQ,EAAE,IAAI,CAACiC,6BAA6B,CAAC,CAAE;YAAA7R,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAClD,CACS,CACZ,CAAC;QAEf;MACJ,CAAC;IAET,CAAC;EACL;EAEAwB,yBAAyBA,CAACM,eAAe,GAAG,EAAE,EAAE;IAAA,IAAA+Q,eAAA,EAAAC,qBAAA;IAC5C,IAAIC,kBAAkB,GAAG,EAAE;IAC3B,IAAInU,YAAY,IAAAiU,eAAA,GAAG,IAAI,CAAChU,OAAO,cAAAgU,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAc/T,OAAO,cAAAgU,qBAAA,uBAArBA,qBAAA,CAAuB/T,aAAa,CACnD,qBACJ,CAAC;IACD,IAAIH,YAAY,IAAIA,YAAY,IAAI,EAAE,EAAE;MACpCA,YAAY,GAAGpG,uBAAuB,CAClCoG,YAAY,EACZnE,SAAS,EACT,IACJ,CAAC;MACDmE,YAAY,CAAChD,OAAO,CAAEiG,WAAW,IAAK;QAClC,IACIC,eAAe,CAAC+B,MAAM,IAAI,CAAC,IAC3B,IAAI,CAACjC,8BAA8B,CAC/BC,WAAW,EACXC,eACJ,CAAC,EACH;UACEiR,kBAAkB,CAAC5O,IAAI,CAAC;YACpBnK,KAAK,EAAE6H,WAAW,CAAC7H,KAAK;YACxBD,KAAK,EAAE8H,WAAW,CAACxC,GAAG;YACtBA,GAAG,EAAEwC,WAAW,CAACxC;UACrB,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;IACN;IACA,OAAO0T,kBAAkB;EAC7B;EAaAnD,yBAAyBA,CAAA,EAAG;IAAA,IAAAoD,eAAA,EAAAC,qBAAA;IACxB,IAAIF,kBAAkB,GAAG,EAAE;IAC3B,IAAInU,YAAY,IAAAoU,eAAA,GAAG,IAAI,CAACnU,OAAO,cAAAmU,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAclU,OAAO,cAAAmU,qBAAA,uBAArBA,qBAAA,CAAuBlU,aAAa,CACnD,qBACJ,CAAC;IACD,IAAIH,YAAY,IAAIA,YAAY,IAAI,EAAE,EAAE;MACpCA,YAAY,GAAGpG,uBAAuB,CAACoG,YAAY,CAAC;MACpDA,YAAY,CAAChD,OAAO,CAAEiG,WAAW,IAAK;QAClC,IACI,CAAAA,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqR,cAAc,KAAI,WAAW,IAC1C,CAAArR,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4H,IAAI,KAAI,iBAAiB,IACtC,CAAA5H,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqR,cAAc,KAAI,QAAQ,EACzC;UACE,IACI,CAAArR,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEpC,MAAM,KAAI,QAAQ,IAC/B,CAAAoC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEpC,MAAM,KAAI,aAAa,IACpC,CAAAoC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEpC,MAAM,KAAI,gBAAgB,IACvC,CAAAoC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsR,WAAW,KAAI,QAAQ,IACpCtR,WAAW,CAACpC,MAAM,IAAI,aAAa,EACrC;YACEsT,kBAAkB,CAAC5O,IAAI,CAAC;cACpBpK,KAAK,EAAE8H,WAAW,CAACxC,GAAG;cACtBrF,KAAK,EAAE6H,WAAW,CAAC7H,KAAK;cACxBoZ,OAAO,EAAEvR,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEvC;YAC1B,CAAC,CAAC;UACN;QACJ;MACJ,CAAC,CAAC;IACN;IACA,OAAOyT,kBAAkB;EAC7B;EAEAM,8BAA8BA,CAAA,EAAG;IAAA,IAAAC,eAAA,EAAAC,qBAAA;IAC7B,IAAIC,gBAAgB,IAAAF,eAAA,GAAG,IAAI,CAACzU,OAAO,cAAAyU,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAcxU,OAAO,cAAAyU,qBAAA,uBAArBA,qBAAA,CAAuBxU,aAAa,CACvD,qBACJ,CAAC;IACD,IAAIyU,gBAAgB,IAAIA,gBAAgB,IAAI,EAAE,EAAE;MAC5CA,gBAAgB,GAAG/a,yBAAyB,CAAC+a,gBAAgB,CAAC;IAClE;IACA,OAAOA,gBAAgB;EAC3B;EA6FAzQ,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAAC9H,KAAK,CAACN,QAAQ,EAAE;MAAA,IAAA8Y,eAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACrB,KAAAH,eAAA,GAAI,IAAI,CAAC5U,OAAO,cAAA4U,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAc3U,OAAO,cAAA4U,qBAAA,uBAArBA,qBAAA,CAAuB3U,aAAa,EAAE;QACtC,MAAMiO,SAAS,GAAG,IAAI,CAACnO,OAAO,CAACC,OAAO,CAACC,aAAa,CAChD,2CACJ,CAAC;QACD,IAAIiO,SAAS,EAAE;UACX,OAAOtR,IAAI,CAACC,KAAK,CAACqR,SAAS,CAAC;QAChC;MACJ;MACA,IAAI6G,gBAAgB,IAAAF,sBAAA,GAChB,IAAI,CAAC1Y,KAAK,CAACT,QAAQ,cAAAmZ,sBAAA,wBAAAC,sBAAA,GAAnBD,sBAAA,CAAqBlX,SAAS,cAAAmX,sBAAA,uBAA9BA,sBAAA,CACMhR,yCAAyC;MACnD,IAAIiR,gBAAgB,EAAE;QAClB,OAAOnY,IAAI,CAACC,KAAK,CAACkY,gBAAgB,CAAC;MACvC;IACJ;IACA,OAAO,CAAC,CAAC;EACb;EAuFAzQ,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACnI,KAAK,CAACN,QAAQ,EAAE;MAAA,IAAAmZ,eAAA,EAAAC,qBAAA;MACrB,KAAAD,eAAA,GAAI,IAAI,CAACjV,OAAO,cAAAiV,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAchV,OAAO,cAAAiV,qBAAA,uBAArBA,qBAAA,CAAuBhV,aAAa,EAAE;QACtC,MAAMiO,SAAS,GAAG,IAAI,CAACnO,OAAO,CAACC,OAAO,CAACC,aAAa,CAChD,qCACJ,CAAC;QACD,IAAIiO,SAAS,EAAE;UACX,OAAOtR,IAAI,CAACC,KAAK,CAACqR,SAAS,CAAC;QAChC;MACJ;MACA,OAAO;QACHgH,oBAAoB,EAAE;UAClB3U,GAAG,EAAE,sBAAsB;UAC3BrF,KAAK,EAAE;QACX;MACJ,CAAC;IACL;IACA,OAAO,CAAC,CAAC;EACb;EACAia,sBAAsBA,CAAA,EAAG;IAAA,IAAAC,eAAA,EAAAC,qBAAA;IACrB,MAAMC,iBAAiB,IAAAF,eAAA,GAAG,IAAI,CAACrV,OAAO,cAAAqV,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAcpV,OAAO,cAAAqV,qBAAA,uBAArBA,qBAAA,CAAuBpV,aAAa,CAC1D,qBACJ,CAAC;IAED,MAAMsV,eAAe,GAAG,IAAI,CAACtG,oBAAoB,CAAC,CAAC;IAEnD,OAAO;MACH5O,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE,CACJ;QACIC,GAAG,EAAE,qBAAqB;QAC1BrF,KAAK,EAAE,kBAAkB;QACzByF,MAAM,EAAE,UAAU;QAClBQ,QAAQ,EAAGc,CAAC,IAAK;UACb,IAAI,CAACX,OAAO,CAAC,CAAC;UACd,IACIW,CAAC,CAACK,MAAM,CAACwG,OAAO,IAAI,IAAI,IACxB,IAAI,CAAC/I,OAAO,CAACC,OAAO,CAACC,aAAa,CAC9B,4BACJ,CAAC,IAAItE,SAAS,EAChB;YACE,IAAI,CAACoE,OAAO,CAACC,OAAO,CAACoB,cAAc,CAAC;cAChCmO,0BAA0B,EAAE3S,IAAI,CAACmH,SAAS,CAAC;gBACvCyR,cAAc,EAAE;kBACZjV,GAAG,EAAE,gBAAgB;kBACrBrF,KAAK,EAAE,WAAW;kBAClBua,iBAAiB,EAAE;gBACvB;cACJ,CAAC;YACL,CAAC,CAAC;UACN;QACJ;MACJ,CAAC,EACD,IAAIH,iBAAiB,GACf,CACI;QACIpa,KAAK,EAAE,oCAAoC;QAC3CqF,GAAG,EAAE,4BAA4B;QACjCgF,aAAa,EAAE;UACXC,KAAK,EAAE;YACHC,OAAO,EAAE;UACb;QACJ;MACJ,CAAC,EACD;QACIlF,GAAG,EAAE,WAAW;QAChBwB,MAAM,EAAEA,CAAA,kBACJ7J,KAAA,CAAA0I,aAAA,CAAA1I,KAAA,CAAAkM,QAAA,qBACIlM,KAAA,CAAA0I,aAAA,CAAC3H,wBAAwB;UACrB4T,IAAI,EAAE,IAAI,CAAC9M,OAAQ;UACnBoB,QAAQ,EAAG0C,MAAM,IAAK;YAClB,IAAI,CAAC9D,OAAO,CAACC,OAAO,CAACoB,cAAc,CAC/B;cACImO,0BAA0B,EACtB3S,IAAI,CAACmH,SAAS,CACVF,MACJ;YACR,CACJ,CAAC;YACD,IAAI,CAACvC,OAAO,CAAC,CAAC;UAClB,CAAE;UACF0C,SAAS,EAAEuR,eAAgB;UAC3BG,WAAW;UACXnR,OAAO;UAAA1D,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CACV,CACH;MAEV,CAAC,CACJ,GACD,EAAE,CAAC;IAEjB,CAAC;EACL;EAEAyU,mCAAmCA,CAACjW,aAAa,EAAE;IAC/C,OAAO;MACHY,MAAM,EAAE,CACJ;QACIC,GAAG,EAAE,8CAA8C;QACnDwB,MAAM,EAAEA,CAAA,KAAM;UACV,oBACI7J,KAAA,CAAA0I,aAAA,CAACnI,IAAI;YAACiU,gBAAgB,EAAC,YAAY;YAAA7L,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,gBAC/BhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;YAACC,GAAG,EAAC,YAAY;YAACrM,GAAG,EAAC,YAAY;YAAAM,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,gBAC3ChJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;YACRkU,IAAI,EAAE,IAAI,CAAC9M,OAAQ;YACnBI,IAAI,EAAE,IAAI,CAACyV,iCAAiC,CACxClW,aACJ,CAAE;YAAAmB,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CACL,CACS,CACZ,CAAC;QAEf;MACJ,CAAC;IAET,CAAC;EACL;EAEA0U,iCAAiCA,CAAClW,aAAa,EAAE;IAC7C,OAAO;MACHY,MAAM,EAAE,CACJ;QACIC,GAAG,EAAE,uDAAuD;QAC5DwB,MAAM,EAAEA,CAAA,KAAM;UACV,oBACI7J,KAAA,CAAA0I,aAAA,CAACnI,IAAI;YACDiU,gBAAgB,EAAC,WAAW;YAC5BnK,SAAS,EAAC,SAAS;YAAA1B,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,gBAEnBhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;YAACC,GAAG,EAAC,WAAW;YAACrM,GAAG,EAAC,WAAW;YAAAM,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,gBACzChJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;YACRkU,IAAI,EAAE,IAAI,CAAC9M,OAAQ;YACnBI,IAAI,EAAE,IAAI,CAACmF,2CAA2C,CAClD5F,aACJ,CAAE;YAAAmB,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CACL,CACS,CACZ,CAAC;QAEf;MACJ,CAAC;IAET,CAAC;EACL;EAoEAgF,iCAAiCA,CAAA,EAAG;IAChC,IAAI,IAAI,CAAC/J,KAAK,CAACN,QAAQ,EAAE;MAAA,IAAAga,eAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACrB,KAAAH,eAAA,GAAI,IAAI,CAAC9V,OAAO,cAAA8V,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAc7V,OAAO,cAAA8V,qBAAA,uBAArBA,qBAAA,CAAuB7V,aAAa,EAAE;QACtC,MAAMiO,SAAS,GAAG,IAAI,CAACnO,OAAO,CAACC,OAAO,CAACC,aAAa,CAChD,6CACJ,CAAC;QACD,IAAIiO,SAAS,EAAE;UACX,OAAOtR,IAAI,CAACC,KAAK,CAACqR,SAAS,CAAC;QAChC;MACJ;MACA,IAAI6G,gBAAgB,IAAAgB,sBAAA,GAChB,IAAI,CAAC5Z,KAAK,CAACT,QAAQ,cAAAqa,sBAAA,wBAAAC,sBAAA,GAAnBD,sBAAA,CAAqBpY,SAAS,cAAAqY,sBAAA,uBAA9BA,sBAAA,CACM7P,2CAA2C;MACrD,IAAI4O,gBAAgB,EAAE;QAClB,OAAOnY,IAAI,CAACC,KAAK,CAACkY,gBAAgB,CAAC;MACvC;IACJ;IACA,OAAO,CAAC,CAAC;EACb;EAEAkB,0BAA0BA,CAACvW,aAAa,EAAE;IACtC,OAAO;MACHY,MAAM,EAAE,CACJ;QACI;QACApF,KAAK,EAAE,8CAA8C;QACrDqF,GAAG,EAAE,sCAAsC;QAC3CgF,aAAa,EAAE;UACXC,KAAK,EAAE;YACHC,OAAO,EAAE;UACb;QACJ;MACJ,CAAC,EACD;QACIvK,KAAK,EAAE,kDAAkD;QACzDqF,GAAG,EAAE,2CAA2C;QAChDgF,aAAa,EAAE;UACXC,KAAK,EAAE;YACHC,OAAO,EAAE;UACb;QACJ;MACJ,CAAC,EACD;QACIvK,KAAK,EAAE,iDAAiD;QACxDqF,GAAG,EAAE,qCAAqC;QAC1CgF,aAAa,EAAE;UACXC,KAAK,EAAE;YACHC,OAAO,EAAE;UACb;QACJ;MACJ,CAAC,EACD;QACIlF,GAAG,EAAE,iCAAiC;QACtCwB,MAAM,EAAEA,CAAA,KAAM;UACV,oBACI7J,KAAA,CAAA0I,aAAA,CAACnI,IAAI;YACDiU,gBAAgB,EAAC,SAAS;YAC1BnK,SAAS,EAAC,SAAS;YAAA1B,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,gBAEnBhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;YAACC,GAAG,EAAC,SAAS;YAACrM,GAAG,EAAC,SAAS;YAAAM,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,gBAErChJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;YACRkU,IAAI,EAAE,IAAI,CAAC9M,OAAQ;YACnBI,IAAI,EAAE,IAAI,CAAC+C,kBAAkB,CACzBxD,aACJ,CAAE;YAAAmB,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CACL,CACS,CAAC,eAEfhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;YACTC,GAAG,EAAC,aAAa;YACjBrM,GAAG,EAAC,aAAa;YAAAM,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,gBAEjBhJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;YACRkU,IAAI,EAAE,IAAI,CAAC9M,OAAQ;YACnBI,IAAI,EAAE,IAAI,CAACuD,6BAA6B,CACpChE,aACJ,CAAE;YAAAmB,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CACL,CACS,CAAC,eAEfhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;YAACC,GAAG,EAAC,YAAY;YAACrM,GAAG,EAAC,YAAY;YAAAM,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,gBAC3ChJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;YACRkU,IAAI,EAAE,IAAI,CAAC9M,OAAQ;YACnBI,IAAI,EAAE,IAAI,CAACgE,4BAA4B,CACnCzE,aACJ,CAAE;YAAAmB,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CACL,CACS,CAAC,eACfhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;YACTC,GAAG,EAAC,WAAW;YACfrM,GAAG,EAAC,eAAe;YAAAM,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,gBAEnBhJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;YACRkU,IAAI,EAAE,IAAI,CAAC9M,OAAQ;YACnBI,IAAI,EAAE,IAAI,CAACgV,sBAAsB,CAC7BzV,aACJ,CAAE;YAAAmB,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CACL,CACS,CACZ,CAAC;QAEf;MACJ,CAAC;IAET,CAAC;EACL;EA4VAI,OAAOA,CAAA,EAAG;IACN,IAAI,CAACjF,QAAQ,CAAC;MACVd,aAAa,EAAE,CAAC,IAAI,CAACY,KAAK,CAACZ;IAC/B,CAAC,CAAC;EACN;EAEA2G,4BAA4BA,CAACpC,YAAY,EAAE;IACvC,IAAI,CAACzD,QAAQ,CAAC;MACV6Z,eAAe,EAAE,IAAI;MACrBC,eAAe,EAAErW;IACrB,CAAC,CAAC;EACN;EAEAsW,8BAA8BA,CAAA,EAAG;IAC7B,OAAO;MACH/V,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE,CACJ;QACIpF,KAAK,EAAE,sBAAsB;QAC7BqF,GAAG,EAAE,gCAAgC;QACrCI,MAAM,EAAE,QAAQ;QAChBY,OAAO,EAAEtH,uCAAuC,CAC5C,IAAI,CAACyI,yBAAyB,CAAC,CACnC,CAAC;QACDlB,WAAW,EAAE;UACTC,IAAI,EAAE,UAAU;UAChBe,UAAU,EAAE,IAAI;UAChBgB,UAAU,EAAE,IAAI;UAChB9B,gBAAgB,EAAE;QACtB;MACJ,CAAC;IAET,CAAC;EACL;EAEA2U,uBAAuBA,CAAA,EAAG;IACtB,OAAO;MACHhW,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE,CACJ;QACIpF,KAAK,EAAE,iCAAiC;QACxCqF,GAAG,EAAE,wBAAwB;QAC7BI,MAAM,EAAE,QAAQ;QAChBY,OAAO,EAAErH,6CAA6C,CAClD,IAAI,CAACwI,yBAAyB,CAAC,CAAC,EAChC,IAAI,CAAC8B,kBAAkB,CAAC,CAC5B,CAAC;QACDhD,WAAW,EAAE;UACTC,IAAI,EAAE,UAAU;UAChBe,UAAU,EAAE,IAAI;UAChBgB,UAAU,EAAE,IAAI;UAChB9B,gBAAgB,EAAE;QACtB;MACJ,CAAC;IAET,CAAC;EACL;EA+JA8I,2CAA2CA,CACvC8L,KAAK,GAAG,KAAK,EACbC,oBAAoB,GAAG,EAAE,EACzBC,eAAe,GAAG,KAAK,EACzB;IAAA,IAAAC,eAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA;IACE;IACA,IAAIC,oBAAoB,GACpB,IAAI,CAAC1a,KAAK,CAACT,QAAQ,CAACsK,0BAA0B;IAClD,IAAI8Q,wCAAwC,GAAG,EAAE;IACjD,IAAIC,gBAAgB,IAAAN,eAAA,GAChB,IAAI,CAAC1W,OAAO,cAAA0W,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAczW,OAAO,cAAA0W,qBAAA,uBAArBA,qBAAA,CAAuBzW,aAAa,CAAC,cAAc,CAAC;IACxD,IAAI8W,gBAAgB,EAAE;MAClBA,gBAAgB,CAACja,OAAO,CAAEka,cAAc,IAAK;QAAA,IAAAC,qBAAA;QACzC,IAAIC,0BAA0B,GAAGL,oBAAoB,aAApBA,oBAAoB,wBAAAI,qBAAA,GAApBJ,oBAAoB,CAAE1R,MAAM,CACxDgS,gBAAgB,IACbA,gBAAgB,CAACC,eAAe,IAAIJ,cAC5C,CAAC,cAAAC,qBAAA,uBAHgCA,qBAAA,CAG7B,CAAC,CAAC;QAEN,IAAIC,0BAA0B,IAAIvb,SAAS,EAAE;UACzC;QACJ;QACA,IAAI0b,QAAQ,GAAGH,0BAA0B,aAA1BA,0BAA0B,uBAA1BA,0BAA0B,CAAEG,QAAQ;QACnD,IAAIC,kBAAkB,GAClBJ,0BAA0B,aAA1BA,0BAA0B,uBAA1BA,0BAA0B,CAAEI,kBAAkB;QAClD,IAAIC,yBAAyB,GACzBL,0BAA0B,aAA1BA,0BAA0B,uBAA1BA,0BAA0B,CAAEnR,eAAe;QAC/CtI,OAAO,CAACC,GAAG,CACP,2BAA2B,EAC3B6Z,yBACJ,CAAC;QACD,IAAIA,yBAAyB,EAAE;UAC3B;UACA;UACA,IAAIC,gBAAgB,GAAGlB,KAAK,GACtB5c,uBAAuB,CACnB6d,yBAAyB,EACzB5b,SAAS,EACT,IACJ,CAAC,GACDjC,uBAAuB,CAAC6d,yBAAyB,CAAC;UACxD,IAAIC,gBAAgB,EAAE;YAClBA,gBAAgB,CAAC1a,OAAO,CAAE2a,sBAAsB,IAAK;cACjD,IACI,CAAAA,sBAAsB,aAAtBA,sBAAsB,uBAAtBA,sBAAsB,CAAEvc,KAAK,MAC5B,CAACob,KAAK,IACFA,KAAK,IACF,EAACC,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAExL,QAAQ,CAC3B0M,sBAAsB,aAAtBA,sBAAsB,uBAAtBA,sBAAsB,CAAElX,GAC5B,CAAC,CAAC,CAAC,EACb;gBACE,IAAI+J,QAAQ,GAAG;kBACXpP,KAAK,EAAE,GAAGmc,QAAQ,MAAMC,kBAAkB,MAAMG,sBAAsB,aAAtBA,sBAAsB,uBAAtBA,sBAAsB,CAAEvc,KAAK,EAAE;kBAC/ED,KAAK,EAAEwc,sBAAsB,aAAtBA,sBAAsB,uBAAtBA,sBAAsB,CAAElX;gBACnC,CAAC;gBACD,IAAI,CAACiW,eAAe,EAAE;kBAClBM,wCAAwC,CAACzR,IAAI,CACzCiF,QACJ,CAAC;gBACL,CAAC,MAAM,IACH,CAACmN,sBAAsB,CAACrD,cAAc,IACtC,CAAAqD,sBAAsB,aAAtBA,sBAAsB,uBAAtBA,sBAAsB,CAAErD,cAAc,KAClC,OAAO,EACb;kBACE0C,wCAAwC,CAACzR,IAAI,CACzCiF,QACJ,CAAC;gBACL;cACJ;YACJ,CAAC,CAAC;UACN;QACJ;MACJ,CAAC,CAAC;IACN;IACA;IACA,IAAIoN,0BAA0B,IAAAf,eAAA,GAAG,IAAI,CAAC5W,OAAO,cAAA4W,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAc3W,OAAO,cAAA4W,qBAAA,uBAArBA,qBAAA,CAAuB3W,aAAa,CACjE,qBACJ,CAAC;IACD,IAAIyX,0BAA0B,EAAE;MAC5BA,0BAA0B,GAAGpB,KAAK,GAC5B5c,uBAAuB,CACnBge,0BAA0B,EAC1B/b,SAAS,EACT,IACJ,CAAC,GACDjC,uBAAuB,CAACge,0BAA0B,CAAC;IAC7D;IACA,IAAIA,0BAA0B,EAAE;MAC5BA,0BAA0B,CAAC5a,OAAO,CAAE6a,uBAAuB,IAAK;QAC5D,IACI,CAAAA,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAEzc,KAAK,MAC7B,CAACob,KAAK,IACFA,KAAK,IACF,EAACC,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAExL,QAAQ,CAC3B4M,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAEpX,GAC7B,CAAC,CAAC,CAAC,EACb;UACE,IAAIqX,uBAAuB,GAAG;YAC1B1c,KAAK,EAAE,UAAUyc,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAEzc,KAAK,EAAE;YACjDD,KAAK,EAAE0c,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAEpX;UACpC,CAAC;UACD,IAAI,CAACiW,eAAe,EAAE;YAClBM,wCAAwC,CAACzR,IAAI,CACzCuS,uBACJ,CAAC;UACL,CAAC,MAAM,IACH,CAACD,uBAAuB,CAACvD,cAAc,IACvC,CAAAuD,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAEvD,cAAc,KAAI,OAAO,EACpD;YACE0C,wCAAwC,CAACzR,IAAI,CACzCuS,uBACJ,CAAC;UACL;QACJ;MACJ,CAAC,CAAC;IACN;IACA,OAAOd,wCAAwC;EACnD;EAEAe,mBAAmBA,CAACnY,aAAa,EAAE;IAAA,IAAAoY,eAAA,EAAAC,qBAAA;IAC/B,MAAMC,iBAAiB,IAAAF,eAAA,GACnB,IAAI,CAAC/X,OAAO,cAAA+X,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAc9X,OAAO,cAAA+X,qBAAA,uBAArBA,qBAAA,CAAuB9X,aAAa,CAAC,kBAAkB,CAAC;IAC5D,OAAO;MACHK,MAAM,EAAE,CACJ;QACIC,GAAG,EAAE,kBAAkB;QACvBrF,KAAK,EAAE,gBAAgB;QACvByF,MAAM,EAAE,UAAU;QAClBQ,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACG,OAAO,CAAC;MACjC,CAAC,EACD,IAAI0W,iBAAiB,GACf,CACI;QACIzX,GAAG,EAAE,6BAA6B;QAClCwB,MAAM,EAAEA,CAAA,KAAM;UACV,oBACI7J,KAAA,CAAA0I,aAAA,CAACnI,IAAI;YACDiU,gBAAgB,EAAC,WAAW;YAC5BnK,SAAS,EAAC,SAAS;YAAA1B,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,gBAEnBhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;YACTC,GAAG,EAAC,aAAa;YACjBrM,GAAG,EAAC,aAAa;YAAAM,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,gBAEjBhJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;YACRkU,IAAI,EAAE,IAAI,CAAC9M,OAAQ;YACnBI,IAAI,EAAEnG,iBAAiB,CACnB0F,aAAa,EACb,IAAI,CAACuL,6BAA6B,CAAC,CACvC,CAAE;YAAApK,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CACL,CACS,CAAC,KAEf,eAAAhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;YACTC,GAAG,EAAC,WAAW;YACfrM,GAAG,EAAC,cAAc;YAAAM,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,gBAElBhJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;YACRkU,IAAI,EAAE,IAAI,CAAC9M,OAAQ;YACnBI,IAAI,EAAEnG,iBAAiB,CACnB0F,aAAa,EACb,IAAI,CAACiM,4BAA4B,CAAC,CACtC,CAAE;YAAA9K,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CACL,CACS,CAAC,KAEb,CAAC;QAEf;MACJ,CAAC,CACJ,GACD,EAAE,CAAC;IAEjB,CAAC;EACL;EAEA+W,sBAAsBA,CAAA,EAAG;IAAA,IAAAC,eAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,sBAAA;IACrB;IACA,MAAMC,eAAe,IAAAV,eAAA,GACjB,IAAI,CAACnY,OAAO,cAAAmY,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAclY,OAAO,cAAAmY,qBAAA,uBAArBA,qBAAA,CAAuBlY,aAAa,CAAC,mBAAmB,CAAC;IAC7D,MAAM4Y,qBAAqB,IAAAT,eAAA,GACvB,IAAI,CAACrY,OAAO,cAAAqY,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAcpY,OAAO,cAAAqY,qBAAA,uBAArBA,qBAAA,CAAuBpY,aAAa,CAAC,cAAc,CAAC;IACxD,MAAM6Y,WAAW,IAAAR,sBAAA,GAAG,IAAI,CAACnc,KAAK,CAACT,QAAQ,cAAA4c,sBAAA,uBAAnBA,sBAAA,CAAqB7U,SAAS;IAClD,MAAMsV,eAAe,IAAAR,eAAA,GAAG,IAAI,CAACxY,OAAO,cAAAwY,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAcvY,OAAO,cAAAwY,qBAAA,uBAArBA,qBAAA,CAAuBvY,aAAa,CACxD,2BACJ,CAAC;IACD,IAAI+Y,sBAAsB,GAAG,EAAE;IAC/B,IAAIH,qBAAqB,IAAIC,WAAW,EAAE;MACtCE,sBAAsB,GAAGF,WAAW,CAAC3T,MAAM,CAAE8T,IAAI,IAC7CJ,qBAAqB,CAAC9N,QAAQ,CAACkO,IAAI,CAAChe,KAAK,CAC7C,CAAC;IACL;IAEA,IAAIie,4BAA4B,GAAG,EAAE;IACrC,IAAIL,qBAAqB,IAAIE,eAAe,EAAE;MAC1C;MACA,MAAMI,mBAAmB,GAAG,CACxB,IAAIN,qBAAqB,IAAI,EAAE,CAAC,EAChC,IAAIE,eAAe,IAAI,EAAE,CAAC,CAC7B;;MAED;MACA,MAAMK,mBAAmB,GAAG,IAAIC,GAAG,CAACF,mBAAmB,CAAC;;MAExD;MACAD,4BAA4B,GAAGJ,WAAW,CAAC3T,MAAM,CAAE8T,IAAI,IACnDG,mBAAmB,CAACE,GAAG,CAACL,IAAI,CAAChe,KAAK,CACtC,CAAC;IACL;IAEA,MAAMse,eAAe,GAAG,IAAI,CAAChL,oBAAoB,CAAC,CAAC;IAEnD,MAAMiL,iBAAiB,IAAAf,eAAA,GAAG,IAAI,CAAC1Y,OAAO,cAAA0Y,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAczY,OAAO,cAAA0Y,qBAAA,uBAArBA,qBAAA,CAAuBzY,aAAa,CAC1D,2BACJ,CAAC;IAED,IAAIwZ,YAAY,GAAG;MACflZ,GAAG,EAAE,qCAAqC;MAC1CI,MAAM,EAAExG,kBAAkB;MAC1B0H,WAAW,EAAE,aAAa;MAC1BU,SAAS,EAAE,YAAY;MACvB/B,QAAQ,EAAE,IAAI;MACdgB,WAAW,EAAE;QACTC,IAAI,EAAE,UAAU;QAChBe,UAAU,EAAE;MAChB;IACJ,CAAC;IAED,IAAIkX,iBAAiB,GAAG;MACpBnZ,GAAG,EAAE,sCAAsC;MAC3CI,MAAM,EAAE,QAAQ;MAChBkB,WAAW,EAAE,kBAAkB;MAC/BU,SAAS,EAAE,YAAY;MACvB/B,QAAQ,EAAE,IAAI;MACdgB,WAAW,EAAE;QACTC,IAAI,EAAE,UAAU;QAChBe,UAAU,EAAE,IAAI;QAChBgB,UAAU,EAAE,IAAI;QAChB9B,gBAAgB,EAAE;MACtB,CAAC;MACDH,OAAO,EAAEyX;IACb,CAAC;IAED,MAAMW,6BAA6B,GAC/BH,iBAAiB,KAAK,WAAW,GAC3B,CAACE,iBAAiB,CAAC,GACnBF,iBAAiB,KAAK,aAAa,GACjC,CAACC,YAAY,CAAC,GACd,EAAE;IACd,OAAO;MACHnZ,MAAM,EAAE,CACJ;QACIC,GAAG,EAAE,mBAAmB;QACxBrF,KAAK,EAAE,eAAe;QACtByF,MAAM,EAAE,UAAU;QAClBQ,QAAQ,EAAGc,CAAC,IAAK;UACb,IAAI,CAACX,OAAO,CAAC,CAAC;UACd,IACIW,CAAC,CAACK,MAAM,CAACwG,OAAO,IAAI,IAAI,IACxB,IAAI,CAAC/I,OAAO,CAACC,OAAO,CAACC,aAAa,CAC9B,6BACJ,CAAC,IAAItE,SAAS,EAChB;YACE,IAAI,CAACoE,OAAO,CAACC,OAAO,CAACoB,cAAc,CAAC;cAChCyN,2BAA2B,EAAEjS,IAAI,CAACmH,SAAS,CAAC;gBACxC,sCAAsC,EAAE;kBACpCxD,GAAG,EAAE,sCAAsC;kBAC3CrF,KAAK,EAAE,SAAS;kBAChBua,iBAAiB,EAAE;gBACvB;cACJ,CAAC;YACL,CAAC,CAAC;UACN;QACJ;MACJ,CAAC,EACD,IAAImD,eAAe,GACb,CACI;QACI1d,KAAK,EAAE,qCAAqC;QAC5CqF,GAAG,EAAE,6BAA6B;QAClCgF,aAAa,EAAE;UACXC,KAAK,EAAE;YACHC,OAAO,EAAE;UACb;QACJ;MACJ,CAAC,EACD;QACIlF,GAAG,EAAE,YAAY;QACjBwB,MAAM,EAAEA,CAAA,kBACJ7J,KAAA,CAAA0I,aAAA,CAAA1I,KAAA,CAAAkM,QAAA,qBACIlM,KAAA,CAAA0I,aAAA,CAAC3H,wBAAwB;UACrB4T,IAAI,EAAE,IAAI,CAAC9M,OAAQ;UACnBoB,QAAQ,EAAG0C,MAAM,IAAK;YAClB,IAAI,CAAC9D,OAAO,CAACC,OAAO,CAACoB,cAAc,CAC/B;cACIyN,2BAA2B,EACvBjS,IAAI,CAACmH,SAAS,CACVF,MACJ;YACR,CACJ,CAAC;YACD,IAAI,CAACvC,OAAO,CAAC,CAAC;UAClB,CAAE;UACF0C,SAAS,EAAEuV,eAAgB;UAC3BK,eAAe;UACfrV,OAAO;UAAA1D,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CACV,CACH;MAEV,CAAC,EACD;QACIX,GAAG,EAAE,aAAa;QAClBwB,MAAM,EAAEA,CAAA,kBACJ7J,KAAA,CAAA0I,aAAA;UAAK2B,SAAS,EAAC,oBAAoB;UAAA1B,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAM;MAEjD,CAAC,EACD;QACIX,GAAG,EAAE,iBAAiB;QACtBrF,KAAK,EAAE,cAAc;QACrByF,MAAM,EAAE,QAAQ;QAChBH,QAAQ,EAAE,IAAI;QACdgB,WAAW,EAAE;UACTC,IAAI,EAAE,UAAU;UAChBe,UAAU,EAAE,IAAI;UAChBgB,UAAU,EAAE,IAAI;UAChB9B,gBAAgB,EAAE;QACtB,CAAC;QACDH,OAAO,EAAE2X;MACb,CAAC,EACD;QACI3Y,GAAG,EAAE,2BAA2B;QAChCrF,KAAK,EAAE,2BAA2B;QAClCyF,MAAM,EAAE,aAAa;QACrBQ,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACG,OAAO,CAAC,CAAC;QAC9Bd,QAAQ,EAAE,IAAI;QACde,OAAO,EAAE,CACL;UACItG,KAAK,EAAE,WAAW;UAClBC,KAAK,EAAE;QACX,CAAC,EACD;UACID,KAAK,EAAE,aAAa;UACpBC,KAAK,EAAE;QACX,CAAC;MAET,CAAC,EACD,GAAGye,6BAA6B,EAChC;QACIpZ,GAAG,EAAE,kDAAkD;QACvDrF,KAAK,EAAE,+CAA+C;QACtDyF,MAAM,EAAE,QAAQ;QAChBH,QAAQ,EAAE,IAAI;QACdgB,WAAW,EAAE;UACTC,IAAI,EAAE,UAAU;UAChBe,UAAU,EAAE,IAAI;UAChBgB,UAAU,EAAE,IAAI;UAChB9B,gBAAgB,EAAE;QACtB,CAAC;QACDH,OAAO,GAAAoX,sBAAA,GAAE,IAAI,CAACxc,KAAK,CAACT,QAAQ,cAAAid,sBAAA,uBAAnBA,sBAAA,CAAqBlV;MAClC,CAAC,CACJ,GACD,EAAE,CAAC;IAEjB,CAAC;EACL;EAkDAmI,eAAeA,CAAA,EAAG;IAAA,IAAAiO,eAAA,EAAAC,qBAAA;IACd,OACI,EAAAD,eAAA,OAAI,CAAC9Z,OAAO,cAAA8Z,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAc7Z,OAAO,cAAA8Z,qBAAA,uBAArBA,qBAAA,CAAuB7Z,aAAa,CAAC,iBAAiB,CAAC,KACvD5G,0BAA0B;EAElC;EA0DAmT,qBAAqBA,CAAA,EAAG;IAAA,IAAAuN,eAAA,EAAAC,qBAAA;IACpB,MAAMC,wBAAwB,GAAG,EAAE;IACnC,MAAMC,cAAc,GAAG,IAAI,aAAJ,IAAI,wBAAAH,eAAA,GAAJ,IAAI,CAAEha,OAAO,cAAAga,eAAA,wBAAAC,qBAAA,GAAbD,eAAA,CAAe/Z,OAAO,cAAAga,qBAAA,uBAAtBA,qBAAA,CAAwB/Z,aAAa,CACxD,6BACJ,CAAC;IACDia,cAAc,IACVA,cAAc,CAAChJ,GAAG,CAAEiJ,oBAAoB,IAAK;MACzC,MAAMC,mBAAmB,GACrB,IAAI,CAACje,KAAK,CAACT,QAAQ,CAACgM,wBAAwB,CAACvC,MAAM,CAC9CkV,YAAY,IACTA,YAAY,CAACpf,KAAK,IAAIkf,oBAC9B,CAAC;MACL,MAAMG,cAAc,GAAG;QACnBpf,KAAK,eACDhD,KAAA,CAAA0I,aAAA,CAAA1I,KAAA,CAAAkM,QAAA,QAAE,4BAC4B,EAAC,GAAG,eAC9BlM,KAAA,CAAA0I,aAAA;UAAM4E,KAAK,EAAE;YAAE+U,UAAU,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAE,CAAE;UAAA3Z,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAC7CkZ,mBAAmB,CAAC,CAAC,CAAC,CAAClf,KACtB,CACR,CACL;QACDqF,GAAG,EAAE,6BAA6B6Z,mBAAmB,CAAC,CAAC,CAAC,CAACnf,KAAK,EAAE;QAChE0F,MAAM,EAAE,QAAQ;QAChBH,QAAQ,EAAE,IAAI;QACde,OAAO,EAAEtH,uCAAuC,CAC5C,IAAI,CAACyI,yBAAyB,CAAC,CAAC,EAChC,CACI,cAAc,EACd,YAAY,EACZ,QAAQ,EACR,gBAAgB,CACnB,EACD,IACJ,CAAC;QACDlB,WAAW,EAAE;UACTC,IAAI,EAAE,UAAU;UAChBe,UAAU,EAAE,IAAI;UAChBgB,UAAU,EAAE,IAAI;UAChB9B,gBAAgB,EAAE;QACtB;MACJ,CAAC;MACDuY,wBAAwB,CAAC5U,IAAI,CAACiV,cAAc,CAAC;IACjD,CAAC,CAAC;IACN,OAAOL,wBAAwB;EACnC;EAEAQ,kCAAkCA,CAAA,EAAG;IACjC,MAAMta,IAAI,GAAG;MACTC,OAAO,EAAE,CAAC;MACVC,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE,CACJ;QACIC,GAAG,EAAE,sCAAsC;QAC3CrF,KAAK,EAAE,8DAA8D;QACrEyF,MAAM,EAAE,QAAQ;QAChBa,WAAW,EAAE;UACTC,IAAI,EAAE,UAAU;UAChBe,UAAU,EAAE;QAChB,CAAC;QACDjB,OAAO,EAAE,IAAI,CAACmZ,yBAAyB,CAAC,CAAC;QACzCvZ,QAAQ,EAAGlG,KAAK,IAAK;UACjB,IAAI,CAAC0f,wCAAwC,CAAC1f,KAAK,CAAC;UACpD,IAAI,CAACqG,OAAO,CAAC,CAAC;QAClB;MACJ,CAAC,EACD;QACIf,GAAG,EAAE,6BAA6B;QAClCrF,KAAK,EAAE,6BAA6B;QACpCyF,MAAM,EAAE,QAAQ;QAChBY,OAAO,EAAE,IAAI,CAACqZ,oCAAoC,CAAC,CAAC;QACpDpZ,WAAW,EAAE;UACTgB,UAAU,EAAE,IAAI;UAChBrB,QAAQ,EAAGlG,KAAK,IAAK;YACjBzB,iBAAiB,CACbyB,KAAK,EACL,IAAI,CAAC8E,OAAO,EACZ,6BACJ,CAAC;UACL;QACJ;MACJ,CAAC;IAET,CAAC;IACD,OAAOI,IAAI;EACf;EAEAua,yBAAyBA,CAAA,EAAG;IAAA,IAAAG,sBAAA;IACxB,OAAO,EAAAA,sBAAA,OAAI,CAAC1e,KAAK,CAACT,QAAQ,cAAAmf,sBAAA,uBAAnBA,sBAAA,CAAqBnT,wBAAwB,KAAI,EAAE;EAC9D;EACAkT,oCAAoCA,CAAA,EAAG;IAAA,IAAAE,eAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA;IACnC,MAAMC,8BAA8B,GAChC,EAAAJ,eAAA,OAAI,CAAC/a,OAAO,cAAA+a,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAAc9a,OAAO,cAAA+a,qBAAA,uBAArBA,qBAAA,CAAuB9a,aAAa,CAChC,sCACJ,CAAC,KAAI,EAAE;IACX,IAAIkb,sBAAsB,GACtB,EAAAH,sBAAA,OAAI,CAAC7e,KAAK,CAACT,QAAQ,cAAAsf,sBAAA,uBAAnBA,sBAAA,CAAqBtT,wBAAwB,KAAI,EAAE;IACvD,IAAI,CAAAwT,8BAA8B,aAA9BA,8BAA8B,uBAA9BA,8BAA8B,CAAEnW,MAAM,IAAG,CAAC,EAAE;MAC5CoW,sBAAsB,GAAGA,sBAAsB,CAAChW,MAAM,CACjDiW,2BAA2B,IACxBF,8BAA8B,CAACnQ,QAAQ,CACnCqQ,2BAA2B,CAACngB,KAChC,CACR,CAAC;IACL;IACA,MAAMogB,uBAAuB,GACzB,EAAAJ,qBAAA,GAAAE,sBAAsB,cAAAF,qBAAA,uBAAtBA,qBAAA,CAAwBlW,MAAM,IAAG,CAAC,GAC5BoW,sBAAsB,GACtB,IAAI,CAACT,yBAAyB,CAAC,CAAC;IAC1C,OAAOW,uBAAuB;EAClC;EACAV,wCAAwCA,CAAC1f,KAAK,EAAE;IAAA,IAAAqgB,eAAA,EAAAC,qBAAA;IAC5C,MAAMC,kBAAkB,IAAAF,eAAA,GAAG,IAAI,CAACvb,OAAO,cAAAub,eAAA,wBAAAC,qBAAA,GAAZD,eAAA,CAActb,OAAO,cAAAub,qBAAA,uBAArBA,qBAAA,CAAuBtb,aAAa,CAC3D,6BACJ,CAAC;IACD,IAAI,CAAChF,KAAK,CAAC8P,QAAQ,CAACyQ,kBAAkB,CAAC,EAAE;MACrChiB,iBAAiB,CACbmC,SAAS,EACT,IAAI,CAACoE,OAAO,EACZ,6BACJ,CAAC;IACL;EACJ;EACAgC,MAAMA,CAAA,EAAG;IAAA,IAAA0Z,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACL,MAAM;MAAE7c;IAAW,CAAC,GAAG,IAAI,CAAC5D,KAAK;IACjC,MAAM;MACFI,gBAAgB;MAChBD,OAAO;MACPI,iBAAiB;MACjBE,KAAK;MACLJ,QAAQ;MACRwa,eAAe;MACfC,eAAe;MACfla;IACJ,CAAC,GAAG,IAAI,CAACE,KAAK;IAEd,IAAI4f,WAAW,GAAG9c,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE+c,KAAK;IACnC,IAAIngB,QAAQ,GAAG,IAAI;IACnB,IAAIkgB,WAAW,IAAIpgB,SAAS,EAAE;MAC1BogB,WAAW,GAAG,uBAAuB;MACrClgB,QAAQ,GAAG,KAAK;IACpB,CAAC,MAAM;MACHkgB,WAAW,GAAG,uBAAuB,GAAGA,WAAW;IACvD;IAEA,MAAMrc,aAAa,GAAG,IAAI,CAACvD,KAAK,CAACN,QAAQ,IAAA4f,sBAAA,GACnC,IAAI,CAACtf,KAAK,CAACT,QAAQ,cAAA+f,sBAAA,wBAAAC,sBAAA,GAAnBD,sBAAA,CAAqB9d,SAAS,cAAA+d,sBAAA,uBAA9BA,sBAAA,CAAgC/d,SAAS,GACzC,CAAC,CAAC;IACR;IACA,IACI,CAAA+B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEuc,cAAc,CAAC,gCAAgC,CAAC,KAC/D,KAAK,EACP;MACEvc,aAAa,CAAC,gCAAgC,CAAC,GAC3C5F,4BAA4B,CAAC,CAAC;IACtC;IACA,OAAO0B,OAAO,gBACVtD,KAAA,CAAA0I,aAAA,CAACxI,KAAK;MACF4jB,KAAK,EAAE,GAAGD,WAAW,EAAG;MACxBvgB,OAAO,EAAEA,OAAQ;MACjB0gB,IAAI,EAAE,IAAI,CAAC9f,QAAS;MACpB+f,cAAc,EAAE1gB,gBAAiB;MACjC2gB,KAAK,EAAE,IAAK;MACZC,QAAQ,EAAE,IAAI,CAAC9f,YAAa;MAC5B+f,MAAM,EAAE,IAAK;MAAAzb,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAEZtF,iBAAiB,gBACd1D,KAAA,CAAA0I,aAAA;MAAK2B,SAAS,EAAC,mCAAmC;MAAA1B,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC9ChJ,KAAA,CAAA0I,aAAA,CAAC/H,gBAAgB;MAAAgI,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAClB,CAAC,GACNxF,QAAQ,IAAIC,SAAS,gBACrBzD,KAAA,CAAA0I,aAAA;MAAG2B,SAAS,EAAC,aAAa;MAAA1B,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAEpF,KAAS,CAAC,gBAEtC5D,KAAA,CAAA0I,aAAA,CAACvI,IAAI;MACDkK,SAAS,EAAC,uCAAuC;MACjDga,MAAM,EAAC,UAAU;MACjBC,GAAG,EAAE,IAAI,CAACzc,OAAQ;MAClB0c,QAAQ,EAAGhgB,IAAI,IAAK;QAChB,IAAI,CAACD,UAAU,CAACC,IAAI,CAAC;MACzB,CAAE;MACFiD,aAAa,EAAE,IAAI,CAACvD,KAAK,CAACN,QAAQ,GAAG6D,aAAa,GAAG,CAAC,CAAE;MAAAmB,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAExDhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI;MACDiU,gBAAgB,EAAC,WAAW;MAC5BvL,QAAQ,EAAGub,SAAS,IAAK;QACrB,IAAIA,SAAS,KAAK,eAAe,EAAE;UAC/B,IAAI,CAACrgB,QAAQ,CAAC;YACVJ,aAAa,EAAE;UACnB,CAAC,CAAC;QACN;MACJ,CAAE;MAAA4E,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAEFhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;MAACC,GAAG,EAAC,WAAW;MAACrM,GAAG,EAAC,WAAW;MAAAM,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACzChJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;MACRwH,IAAI,EAAE,IAAI,CAACf,OAAO,CAAC,CAAE;MACrByN,IAAI,EAAE,IAAI,CAAC9M,OAAQ;MAAAc,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACtB,CACS,CAAC,eAEfhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;MACTC,GAAG,EAAC,WAAW;MACfrM,GAAG,EAAC,WAAW;MACfoc,WAAW,EAAE,IAAK;MAAA9b,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAElBhJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;MACRwH,IAAI,EAAE,IAAI,CAACwP,8BAA8B,CAAC,CAAE;MAC5C9C,IAAI,EAAE,IAAI,CAAC9M,OAAQ;MAAAc,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACtB,CACS,CAAC,EACd,CAAC,IAAI,CAAC0K,eAAe,CAAC,CAAC,iBACpB1T,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;MACTC,GAAG,EAAC,eAAe;MACnBrM,GAAG,EAAC,eAAe;MACnBoc,WAAW,EAAE,IAAK;MAAA9b,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAElBhJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;MACRwH,IAAI,EAAE,IAAI,CAACsa,kCAAkC,CAAC,CAAE;MAChD5N,IAAI,EAAE,IAAI,CAAC9M,OAAQ;MAAAc,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACtB,CACS,CACjB,eACDhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;MACTC,GAAG,EAAC,YAAY;MAChBrM,GAAG,EAAC,YAAY;MAChBoc,WAAW,EAAE,IAAK;MAAA9b,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAElBhJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;MACRwH,IAAI,EAAE,IAAI,CAACmG,uBAAuB,CAC9B5G,aACJ,CAAE;MACFmN,IAAI,EAAE,IAAI,CAAC9M,OAAQ;MAAAc,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACtB,CACS,CAAC,eAEfhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;MACTC,GAAG,EAAC,eAAe;MACnBrM,GAAG,EAAC,eAAe;MAAAM,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAEnBhJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;MACRwH,IAAI,EAAE,IAAI,CAAC2H,mBAAmB,CAC1BpI,aACJ,CAAE;MACFmN,IAAI,EAAE,IAAI,CAAC9M,OAAQ;MAAAc,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACtB,CACS,CAAC,eAEfhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;MACTC,GAAG,EAAC,gBAAgB;MACpBrM,GAAG,EAAC,gBAAgB;MACpBoc,WAAW,EAAE,KAAM;MAAA9b,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAEnBhJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;MACRwH,IAAI,EAAE,IAAI,CAACmT,0BAA0B,CACjC5T,aACJ,CAAE;MACFmN,IAAI,EAAE,IAAI,CAAC9M,OAAQ;MAAAc,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACtB,CACS,CAAC,eAEfhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;MACTC,GAAG,EAAC,SAAS;MACbrM,GAAG,EAAC,SAAS;MACboc,WAAW,EAAE,IAAK;MAAA9b,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAElBhJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;MACRwH,IAAI,EAAE,IAAI,CAAC8V,0BAA0B,CACjCvW,aACJ,CAAE;MACFmN,IAAI,EAAE,IAAI,CAAC9M,OAAQ;MAAAc,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACtB,CACS,CAAC,eACfhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;MACTC,GAAG,EAAC,YAAY;MAChBrM,GAAG,EAAC,YAAY;MAChBoc,WAAW,EAAE,IAAK;MAAA9b,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAElBhJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;MACRwH,IAAI,EAAE,IAAI,CAAC8X,sBAAsB,CAAC,CAAE;MACpCpL,IAAI,EAAE,IAAI,CAAC9M,OAAQ;MAAAc,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACtB,CACS,CAAC,EACd,CAAC,IAAI,CAAC0K,eAAe,CAAC,CAAC,iBACpB1T,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;MACTC,GAAG,EAAC,KAAK;MACTrM,GAAG,EAAC,eAAe;MACnBoc,WAAW,EAAE,IAAK;MAAA9b,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAEjBjF,aAAa,iBACV/D,KAAA,CAAA0I,aAAA,CAACpG,gBAAgB;MACbsK,QAAQ,GAAA6W,sBAAA,GACJ,IAAI,CAACxf,KAAK,CAACT,QAAQ,cAAAigB,sBAAA,uBAAnBA,sBAAA,CAAqBlY,SACxB;MACD9F,SAAS,GAAAie,sBAAA,GACL,IAAI,CAACzf,KAAK,CAACT,QAAQ,cAAAkgB,sBAAA,uBAAnBA,sBAAA,CAAqBje,SACxB;MACDkP,IAAI,EAAE,IAAI,CAAC9M,OAAQ;MACnBlE,QAAQ,EAAE,IAAI,CAACR,KAAK,CAACQ,QAAS;MAAAgF,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACjC,CAEK,CACjB,EACA,IAAI,CAAC0K,eAAe,CAAC,CAAC,iBACnB1T,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;MACTC,GAAG,EAAC,KAAK;MACTrM,GAAG,EAAC,eAAe;MACnBoc,WAAW,EAAE,IAAK;MAAA9b,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAEjBjF,aAAa,iBACV/D,KAAA,CAAA0I,aAAA,CAACnG,eAAe;MACZoS,IAAI,EAAE,IAAI,CAAC9M,OAAQ;MACnB+E,QAAQ,GAAA+W,sBAAA,GACJ,IAAI,CAAC1f,KAAK,CAACT,QAAQ,cAAAmgB,sBAAA,uBAAnBA,sBAAA,CAAqBpY,SACxB;MACD9F,SAAS,GAAAme,sBAAA,GACL,IAAI,CAAC3f,KAAK,CAACT,QAAQ,cAAAogB,sBAAA,uBAAnBA,sBAAA,CAAqBne,SACxB;MACDic,eAAe;MAAA/Y,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAClB,CAEK,CACjB,eACDhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;MACTC,GAAG,EAAC,cAAc;MAClBrM,GAAG,EAAC,cAAc;MAClBoc,WAAW,EAAE,IAAK;MAAA9b,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAElBhJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;MACRwH,IAAI,EAAE,IAAI,CAACwV,mCAAmC,CAC1CjW,aACJ,CAAE;MACFmN,IAAI,EAAE,IAAI,CAAC9M,OAAQ;MAAAc,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACtB,CACS,CAAC,eAEfhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;MACTC,GAAG,EAAC,cAAc;MAClBrM,GAAG,EAAC,cAAc;MAClBoc,WAAW,EAAE,IAAK;MAAA9b,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAElBhJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;MACRwH,IAAI,EAAE,IAAI,CAACiW,8BAA8B,CACrC1W,aACJ,CAAE;MACFmN,IAAI,EAAE,IAAI,CAAC9M,OAAQ;MAAAc,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACtB,CACS,CAAC,eAEfhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;MACTC,GAAG,EAAC,OAAO;MACXrM,GAAG,EAAC,OAAO;MACXoc,WAAW,EAAE,IAAK;MAAA9b,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAElBhJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;MACRwH,IAAI,EAAE,IAAI,CAACkW,uBAAuB,CAAC,CAAE;MACrCxJ,IAAI,EAAE,IAAI,CAAC9M,OAAQ;MAAAc,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACtB,CACS,CAAC,eAEfhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;MACTC,GAAG,EAAC,gBAAgB;MACpBrM,GAAG,EAAC,gBAAgB;MACpBoc,WAAW,EAAE,IAAK;MAAA9b,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAElBhJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;MACRwH,IAAI,EAAE,IAAI,CAACoJ,oBAAoB,CAAC,CAAE;MAClCsD,IAAI,EAAE,IAAI,CAAC9M,OAAQ;MAAAc,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACtB,CACS,CAAC,eACfhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;MACTC,GAAG,EAAC,aAAa;MACjBrM,GAAG,EAAC,aAAa;MACjBoc,WAAW,EAAE,IAAK;MAAA9b,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAElBhJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;MACRwH,IAAI,EAAE,IAAI,CAACgM,iBAAiB,CAAC,CAAE;MAC/BU,IAAI,EAAE,IAAI,CAAC9M,OAAQ;MAAAc,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACtB,CACS,CAAC,eACfhJ,KAAA,CAAA0I,aAAA,CAACnI,IAAI,CAACkU,OAAO;MACTC,GAAG,EAAC,YAAY;MAChBrM,GAAG,EAAC,WAAW;MACfoc,WAAW,EAAE,IAAK;MAAA9b,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAElBhJ,KAAA,CAAA0I,aAAA,CAACjI,WAAW;MACRwH,IAAI,EAAE,IAAI,CAAC0X,mBAAmB,CAC1BnY,aACJ,CAAE;MACFmN,IAAI,EAAE,IAAI,CAAC9M,OAAQ;MAAAc,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACtB,CACS,CACZ,CAAC,eAEPhJ,KAAA,CAAA0I,aAAA,CAACvI,IAAI,CAACukB,IAAI;MAACra,SAAS,EAAC,SAAS;MAAA1B,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC1BhJ,KAAA,CAAA0I,aAAA,CAACrI,MAAM;MACHoS,IAAI,EAAC,SAAS;MACdkS,QAAQ,EAAC,QAAQ;MACjBC,QAAQ,EAAErhB,gBAAiB;MAAAoF,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAE1BrF,QAAQ,GAAG,MAAM,GAAG,QACjB,CACD,CAAC,EACXJ,gBAAgB,gBACbvD,KAAA,CAAA0I,aAAA;MAAK2B,SAAS,EAAC,mCAAmC;MAAA1B,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC9ChJ,KAAA,CAAA0I,aAAA,CAAC/H,gBAAgB;MAAAgI,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAClB,CAAC,GACN,IAAI,EACPpF,KAAK,gBAAG5D,KAAA,CAAA0I,aAAA;MAAG2B,SAAS,EAAC,aAAa;MAAA1B,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAEpF,KAAS,CAAC,GAAG,IAChD,CACT,EACAoa,eAAe,iBACZhe,KAAA,CAAA0I,aAAA,CAAC9H,eAAe;MACZqd,eAAe,EAAEA,eAAgB;MACjCkG,QAAQ,EAAEA,CAAA,KAAM;QACZ,IAAI,CAAChgB,QAAQ,CAAC;UAAE6Z,eAAe,EAAE;QAAM,CAAC,CAAC;MAC7C,CAAE;MAAArV,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACL,CAEF,CAAC,gBAERhJ,KAAA,CAAA0I,aAAA,CAAA1I,KAAA,CAAAkM,QAAA,MAAI,CACP;EACL;AACJ;AAEA,eAAejJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}