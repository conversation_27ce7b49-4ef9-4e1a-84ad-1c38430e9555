const mkwsrvcs = {
  email_id: "<EMAIL>",
  subject: "WIFY TMS Customer requests dump Mar-03-2025 12:46 PM",
  org_info: ["brand_name", "service_type", "vertical_fr_req_dump"],
  customer_info_fields: ["cust_full_name"],
  address_info_fields: [
    "cust_line_0",
    "cust_pincode",
    "cust_line_1",
    "cust_city",
    "cust_state",
    "location_group",
  ],
  request_info_fields: [
    "request_description",
    "request_req_date",
    "request_priority",
    "vertical_title",
  ],
  onfield_task_fields: [
    "assignee_name",
    "task_status",
    "task_start_date",
    "sp_first_task_date",
    "sp_last_task_date",
  ],
  SP_specific_fields: [
    "c0908131-3f05-4f0d-828f-603e02da8acc",
    "2a01e694-7ef0-4c57-9ad8-23a6a74b25ac",
    "4b18af09-b151-4d8d-8f7e-36d3fde588e7",
    "98a01513-6e18-4844-b7bf-921fd35e8327",
    "541463a5-45d0-4f59-94ee-7c3fd72f1e87",
    "4f941084-0c1d-4b9b-b579-7e32d867c667",
    "68721d91-a09d-47a0-9764-21a5fe92dfcd",
    "a0d645cb-3e6c-42f6-a9ab-97813dc0d91b",
    "fe63737a-eb47-48c5-990e-ff4da48292ea",
    "d5e3b9a4-e937-4c53-bc28-c6e8b1881390",
    "06eed560-9c03-43d4-8498-85a7bc0990ea",
    "00d436e9-70e4-44fd-b401-7978a86abf1c",
    "0307ff84-95be-4209-8311-84117a1018e3",
    "b1f3c7c7-e520-437f-ae72-627423b51fdf",
    "c132ed19-f201-4c7f-8522-11e09432ba12",
    "d9f55aad-eccd-48dc-b2ec-fba10181deac",
    "1efb7702-2fac-4f3f-b0cf-45458ad12d08",
    "5fa29751-2f2d-4c01-a653-c76889c4c698",
    "ec4739ab-dd7f-44ac-8b00-d3be04ad7d6d",
    "65506b5b-4f65-480b-ad85-51a512eb5aee",
    "d9d969fa-f0fe-40f2-9a49-e6e0134fbbf5",
    "b3f6d5f4-1245-48a5-9c95-40b75b494b5f",
    "7df134d7-ca02-4822-ad62-336a4ca07fcd",
  ],
  sp_authorities: [
    "authority_428",
    "authority_429",
    "authority_430",
    "authority_431",
    "authority_432",
    "authority_433",
    "authority_441",
    "authority_444",
  ],
  gai_info_fields: ["avg_gai_rating", "no_of_gai_rated_tasks"],
  locked_for_change: [
    "sp_locked_for_change",
    "sp_locked_for_change_by",
    "sp_locking_date",
  ],
  calculated_task_data: ["mandays", "sp_work_progress"],
  line_item_data: ["sp_total_quantity", "sp_total_price"],
  payout_info: ["total_quantity", "total_price", "total_payout"],
  pi_info: ["final_amount", "sp_sent_for_billing"],
  sp_attachment_field_info: [
    "********-1466-45ae-bb43-9ed68b529636",
    "486e1342-02bc-4871-ba76-bcbd63d57fcf",
    "35a50d36-b3da-4ddb-bf47-f07500a9da7c",
    "b7a619b6-7f5b-4a9a-ac2f-8b6e8b7fe4eb",
  ],
  field_label_mapping: {
    brand_name: "Brand Name",
    service_type: "Service Type",
    vertical_fr_req_dump: "Vertical",
    cust_mobile: "Mobile(+91)",
    cust_full_name: "Name",
    cust_email: "Email",
    cust_line_0: "Flat no",
    cust_line_1: "Building/Apartment name",
    cust_line_2: "Line 1",
    cust_line_3: "Line 2",
    cust_pincode: "Pincode",
    cust_city: "City",
    cust_state: "State",
    location_group: "Location Group",
    request_description: "Description",
    request_req_date: "Req. Service Date",
    request_priority: "Priority",
    request_labels: "Labels",
    request_cc_users: "CC users",
    creation_date: "Creation Date",
    vertical_title: "Request vertical",
    assignee_name: "Assignee name",
    task_status: "Task status",
    task_start_date: "Task start date",
    assigned_by: "Assigned by",
    assigned_date: "Assigned date",
    assigned_time: "Assigned time",
    task_status_remark: "Task status remark",
    task_geo_verification_status: "GEO verification status",
    "assignee_phone_no.": "Assignee Phone No.",
    sp_first_task_date: "SP First task date",
    sp_last_task_date: "SP Last task date",
    gai_remarks: "GAI remarks",
    "c0908131-3f05-4f0d-828f-603e02da8acc": "Client ID",
    "2a01e694-7ef0-4c57-9ad8-23a6a74b25ac": "Busniess Unit",
    "4b18af09-b151-4d8d-8f7e-36d3fde588e7": "Work Type",
    "98a01513-6e18-4844-b7bf-921fd35e8327": "Types of Measurements: ",
    "541463a5-45d0-4f59-94ee-7c3fd72f1e87": "Product Measurement:",
    "4f941084-0c1d-4b9b-b579-7e32d867c667": "Required Reports: ",
    "68721d91-a09d-47a0-9764-21a5fe92dfcd": "Site Note",
    "a0d645cb-3e6c-42f6-a9ab-97813dc0d91b": "Completion Comments:",
    "fe63737a-eb47-48c5-990e-ff4da48292ea": "Fulfillment By",
    "d5e3b9a4-e937-4c53-bc28-c6e8b1881390": "Cleaning Sqft",
    "06eed560-9c03-43d4-8498-85a7bc0990ea": "PO Received Date",
    "00d436e9-70e4-44fd-b401-7978a86abf1c": "PO Number",
    "0307ff84-95be-4209-8311-84117a1018e3": "Po Received Amount",
    "b1f3c7c7-e520-437f-ae72-627423b51fdf": "PO Billing Status",
    "c132ed19-f201-4c7f-8522-11e09432ba12": "Contractor Payout Date",
    "d9f55aad-eccd-48dc-b2ec-fba10181deac": "Contractor Payout Amt",
    "1efb7702-2fac-4f3f-b0cf-45458ad12d08": "FOC Status",
    "5fa29751-2f2d-4c01-a653-c76889c4c698": "FOC Comments",
    "ec4739ab-dd7f-44ac-8b00-d3be04ad7d6d": "Invoice Number",
    "65506b5b-4f65-480b-ad85-51a512eb5aee": "Invoice Amount",
    "d9d969fa-f0fe-40f2-9a49-e6e0134fbbf5": "Invoice Date",
    "b3f6d5f4-1245-48a5-9c95-40b75b494b5f": "Payment Received Amount",
    "7df134d7-ca02-4822-ad62-336a4ca07fcd": "Payment Received Date",
    authority_428: "MKW OPS - Executive",
    authority_429: "MKW Billing Incharge",
    authority_430: "MKW Key Account Manager",
    authority_431: "MKW Ops Manager",
    authority_432: "MKW City Manager",
    authority_433: "MKW Regional Head",
    authority_441: "MKW Project Manager",
    authority_444: "MKW Design",
    avg_gai_rating: "Avg. GAI rating",
    no_of_gai_rated_tasks: "No. of GAI rated tasks",
    sp_locked_for_change: "SP Locked for change (Yes/No)",
    sp_locked_for_change_by: "SP Locked for change by",
    sp_locking_date: "SP Locking Date",
    mandays: "Mandays",
    sp_work_progress: "SP Work progress percentage",
    sp_total_quantity: "SP Total quantity",
    sp_total_price: "SP Total price",
    total_quantity: "Vendor Bill Amount",
    total_price: "Deductions",
    total_payout: "Paid Amount",
    final_amount: "Final amount",
    sp_sent_for_billing: "SP sent for billing",
    "********-1466-45ae-bb43-9ed68b529636": "PO File",
    "486e1342-02bc-4871-ba76-bcbd63d57fcf": "Completion Certificate: ",
    "35a50d36-b3da-4ddb-bf47-f07500a9da7c": "Invoice File",
    "b7a619b6-7f5b-4a9a-ac2f-8b6e8b7fe4eb": "Pay.Rcvd File",
  },
  selected_columns: [
    "brand_name",
    "service_type",
    "vertical_fr_req_dump",
    "cust_full_name",
    "cust_line_0",
    "cust_line_1",
    "cust_pincode",
    "cust_city",
    "cust_state",
    "location_group",
    "request_description",
    "request_req_date",
    "request_priority",
    "vertical_title",
    "assignee_name",
    "task_status",
    "task_start_date",
    "sp_first_task_date",
    "sp_last_task_date",
    "c0908131-3f05-4f0d-828f-603e02da8acc",
    "2a01e694-7ef0-4c57-9ad8-23a6a74b25ac",
    "4b18af09-b151-4d8d-8f7e-36d3fde588e7",
    "98a01513-6e18-4844-b7bf-921fd35e8327",
    "541463a5-45d0-4f59-94ee-7c3fd72f1e87",
    "4f941084-0c1d-4b9b-b579-7e32d867c667",
    "68721d91-a09d-47a0-9764-21a5fe92dfcd",
    "a0d645cb-3e6c-42f6-a9ab-97813dc0d91b",
    "fe63737a-eb47-48c5-990e-ff4da48292ea",
    "d5e3b9a4-e937-4c53-bc28-c6e8b1881390",
    "06eed560-9c03-43d4-8498-85a7bc0990ea",
    "00d436e9-70e4-44fd-b401-7978a86abf1c",
    "0307ff84-95be-4209-8311-84117a1018e3",
    "b1f3c7c7-e520-437f-ae72-627423b51fdf",
    "c132ed19-f201-4c7f-8522-11e09432ba12",
    "d9f55aad-eccd-48dc-b2ec-fba10181deac",
    "1efb7702-2fac-4f3f-b0cf-45458ad12d08",
    "5fa29751-2f2d-4c01-a653-c76889c4c698",
    "ec4739ab-dd7f-44ac-8b00-d3be04ad7d6d",
    "65506b5b-4f65-480b-ad85-51a512eb5aee",
    "d9d969fa-f0fe-40f2-9a49-e6e0134fbbf5",
    "b3f6d5f4-1245-48a5-9c95-40b75b494b5f",
    "7df134d7-ca02-4822-ad62-336a4ca07fcd",
    "authority_428",
    "authority_429",
    "authority_430",
    "authority_431",
    "authority_432",
    "authority_433",
    "authority_441",
    "authority_444",
    "avg_gai_rating",
    "no_of_gai_rated_tasks",
    "sp_locked_for_change",
    "sp_locked_for_change_by",
    "sp_locking_date",
    "mandays",
    "sp_work_progress",
    "sp_total_quantity",
    "sp_total_price",
    "total_quantity",
    "total_price",
    "total_payout",
    "final_amount",
    "sp_sent_for_billing",
    "********-1466-45ae-bb43-9ed68b529636",
    "486e1342-02bc-4871-ba76-bcbd63d57fcf",
    "35a50d36-b3da-4ddb-bf47-f07500a9da7c",
    "b7a619b6-7f5b-4a9a-ac2f-8b6e8b7fe4eb",
  ],
  filters:
    '{"assgn_to_prvdr_date":["2024-12-01T07:09:36.200Z","2024-12-06T07:09:36.200Z"],"verticals_list":[5]}',
  pagination: "{}",
  org_id: 2,
  usr_id: "e4dbc3e2-62cf-4957-8ff4-848b3f36c44f",
  ip_addr: "::1",
  user_agent:
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  srvc_type_id: "0",
  is_customer_access: "0",
};

const mkwinst = {
  email_id: "<EMAIL>",
  subject: "WIFY TMS Customer requests dump Mar-03-2025 06:50 PM",
  org_info: ["brand_name", "service_type", "vertical_fr_req_dump"],
  address_info_fields: [
    "cust_line_0",
    "cust_line_1",
    "cust_line_2",
    "cust_line_3",
    "cust_pincode",
    "cust_city",
    "cust_state",
    "location_group",
  ],
  customer_info_fields: ["cust_full_name"],
  request_info_fields: [
    "request_description",
    "request_req_date",
    "request_priority",
    "vertical_title",
  ],
  onfield_task_fields: [
    "sp_first_task_date",
    "sp_last_task_date",
    "gai_remarks",
  ],
  SP_specific_fields: [
    "08ce8925-8923-4c80-923b-45e1dd17e0b0",
    "7033ab8c-22c1-4cf0-94e2-2c104b2bec26",
    "b274d89e-fafd-4789-999c-a848cd8e6483",
    "9ad89a24-27ab-44e1-9fe4-3f20be4316bf",
    "ceedcf1d-1f6e-4cb0-9cec-e6ae8e5c73e1",
    "ce8de42a-3a35-40b1-93f7-5871d6e5fe86",
    "9446ae26-5c77-496d-b87a-719b56573f4c",
    "4036b63d-ba39-4e00-9aa7-41351f763d97",
    "1b9e9baa-e830-455e-b1a7-9783aea5830b",
    "b2c8e073-49cc-4f92-a966-db94f3e0d310",
    "e1897795-58f6-4250-984c-00cb4cc6f7d2",
    "f14efb7f-6e52-4e2f-8807-36b6a1eef4d8",
    "f03a676a-6fae-420e-bf4f-644c089ee8ec",
    "e1a60e6e-20c3-45af-9063-48be25c1aa5b",
    "82264689-c5f4-4c76-b617-7ab134972a3d",
    "eadb139e-f076-4c4b-a815-42822fbd8c94",
    "660496d9-4bd9-453e-9bfb-6c64a699d785",
    "ecd2074e-57e1-483f-baba-df14d36863c0",
    "b8909ff9-edd2-4572-a82c-fe4720becbc0",
    "3584aede-4f10-4d07-9d9b-b64819546ace",
    "aa4b2005-c229-4c77-9b61-fe77d0ebe88b",
    "1ee680d8-7f92-49e8-94a3-0307661c084a",
    "0ddee6ff-5c6f-4747-b441-5b69d4ab71e7",
    "31eb4eee-d2df-434c-a2bd-c0e609cef195",
    "4c74dcc3-2ec4-4156-85e0-6a2d084292fd",
    "bc27d2ff-3664-445d-9516-c9a57c7339ba",
    "270cc594-bdc6-4d22-a467-f2e3b619e2fb",
    "9d30592d-deba-490e-887f-2ae78d094486",
    "50c978bb-4e59-4e90-9478-70ad580dab5c",
    "517d0b81-a559-47b7-a230-53d1ba46f211",
    "60c6f5ff-c03c-48ec-8f0b-7802802671ac",
    "ec029c6d-1460-4e69-bfbb-d17020213a4a",
    "7235dc23-6745-4780-971e-6dd6f9930407",
    "ed82cd58-7ca7-435b-ac62-56e513f01f3b",
    "cd989a04-9a9c-41d7-ab14-3763c5861a1d",
    "e75a6d5d-7ce9-4125-ae04-91899936f2e0",
    "e3d7eb05-1a66-41fe-8930-3e1be6367220",
    "519457b0-60fd-41a5-b1a7-02867d7a3b18",
    "c88cf034-8459-4dde-b0a2-5cadbd72a34e",
    "9f335621-9a6e-42bb-929f-561bf9a80c0f",
    "********-8a16-419d-b219-f138cce1699a",
    "3f95b6bc-1d46-40f4-95db-bf7803a7d16d",
    "9509eb46-3a2d-404b-aa4a-2be47fd14fcb",
    "f3b24901-830a-4d9e-b79c-bfa62ff66d30",
    "531d9100-5e46-43b2-ac09-e1185ab96ec9",
    "f1548ce1-70a7-41ee-9f37-c4bd6f006195",
    "23560ebf-d4b4-4582-9050-1293ff224898",
  ],
  sp_authorities: [
    "authority_428",
    "authority_429",
    "authority_430",
    "authority_431",
    "authority_432",
    "authority_433",
    "authority_441",
  ],
  gai_info_fields: ["avg_gai_rating", "no_of_gai_rated_tasks"],
  locked_for_change: [
    "sp_locked_for_change",
    "sp_locked_for_change_by",
    "sp_locking_date",
  ],
  calculated_task_data: ["mandays", "sp_work_progress"],
  line_item_data: ["sp_total_quantity", "sp_total_price"],
  payout_info: ["total_quantity", "total_price", "total_payout"],
  pi_info: ["final_amount", "sp_sent_for_billing"],
  sp_attachment_field_info: [
    "ec12c224-8435-43b8-867f-766cb02f59de",
    "770ddcf9-8ff2-4aa4-abd1-c20b8759baa5",
    "19e1a2b4-761f-40a9-ad95-665d5b154cd1",
    "f07c83eb-a051-43e9-8412-a502d7f9d078",
    "a96c41b2-e820-47c1-9ecc-4216deb3f528",
    "0aeccd19-e387-4461-b367-f0639abfd96b",
    "452f4794-50a8-47fd-a192-93ed0ae72498",
    "22f0e0d4-f5f1-4275-aaed-643a92b08475",
    "dfb5e81e-c854-4658-a20d-dce8006adb77",
    "0d9aef1c-f490-461c-af27-99b2afa02b14",
  ],
  field_label_mapping: {
    brand_name: "Brand Name",
    service_type: "Service Type",
    vertical_fr_req_dump: "Vertical",
    cust_mobile: "Mobile(+91)",
    cust_full_name: "Name",
    cust_email: "Email",
    cust_line_0: "Flat no",
    cust_line_1: "Building/Apartment name",
    cust_line_2: "Line 1",
    cust_line_3: "Line 2",
    cust_pincode: "Pincode",
    cust_city: "City",
    cust_state: "State",
    location_group: "Location Group",
    request_description: "Description",
    request_req_date: "Req. Service Date",
    request_priority: "Priority",
    request_labels: "Labels",
    request_cc_users: "CC users",
    creation_date: "Creation Date",
    vertical_title: "Request vertical",
    assignee_name: "Assignee name",
    task_status: "Task status",
    task_start_date: "Task start date",
    assigned_by: "Assigned by",
    assigned_date: "Assigned date",
    assigned_time: "Assigned time",
    task_status_remark: "Task status remark",
    task_geo_verification_status: "GEO verification status",
    "assignee_phone_no.": "Assignee Phone No.",
    sp_first_task_date: "SP First task date",
    sp_last_task_date: "SP Last task date",
    gai_remarks: "GAI remarks",
    "08ce8925-8923-4c80-923b-45e1dd17e0b0": "Client ID",
    "7033ab8c-22c1-4cf0-94e2-2c104b2bec26": "Business Unit",
    "b274d89e-fafd-4789-999c-a848cd8e6483": "Work Type ",
    "9ad89a24-27ab-44e1-9fe4-3f20be4316bf": "Site Type",
    "ceedcf1d-1f6e-4cb0-9cec-e6ae8e5c73e1": "Old Site Ref",
    "ce8de42a-3a35-40b1-93f7-5871d6e5fe86": "Work Description",
    "9446ae26-5c77-496d-b87a-719b56573f4c": "Drawing Received Date",
    "4036b63d-ba39-4e00-9aa7-41351f763d97": "Site Note",
    "1b9e9baa-e830-455e-b1a7-9783aea5830b": "Line Item Remark Date",
    "b2c8e073-49cc-4f92-a966-db94f3e0d310": "Line Item Remarks",
    "e1897795-58f6-4250-984c-00cb4cc6f7d2": "Dwell (Next Follow up) Date",
    "f14efb7f-6e52-4e2f-8807-36b6a1eef4d8": "Dwell Status",
    "f03a676a-6fae-420e-bf4f-644c089ee8ec": "Dwell Comments",
    "e1a60e6e-20c3-45af-9063-48be25c1aa5b": "Completion Status",
    "82264689-c5f4-4c76-b617-7ab134972a3d": "Completion Comments",
    "eadb139e-f076-4c4b-a815-42822fbd8c94": "SOW",
    "660496d9-4bd9-453e-9bfb-6c64a699d785": "SOW Date",
    "ecd2074e-57e1-483f-baba-df14d36863c0": "Handover Document Received Date :",
    "b8909ff9-edd2-4572-a82c-fe4720becbc0": "Fulfillment By",
    "3584aede-4f10-4d07-9d9b-b64819546ace": "Contractor Total Payout",
    "aa4b2005-c229-4c77-9b61-fe77d0ebe88b": "Contractor Payout Date",
    "1ee680d8-7f92-49e8-94a3-0307661c084a": "QC Status (Dwell):",
    "0ddee6ff-5c6f-4747-b441-5b69d4ab71e7": "QC Comments (Dwell):",
    "31eb4eee-d2df-434c-a2bd-c0e609cef195": "QC Received Date (Dwell):",
    "4c74dcc3-2ec4-4156-85e0-6a2d084292fd": "QC Status (Final) ",
    "bc27d2ff-3664-445d-9516-c9a57c7339ba": "QC Comments (Final):",
    "270cc594-bdc6-4d22-a467-f2e3b619e2fb": "QC Received Date (Final):",
    "9d30592d-deba-490e-887f-2ae78d094486": "PO Number",
    "50c978bb-4e59-4e90-9478-70ad580dab5c": "PO Recieved Date",
    "517d0b81-a559-47b7-a230-53d1ba46f211": "Mode of PO Received",
    "60c6f5ff-c03c-48ec-8f0b-7802802671ac": "SQFT PO Received",
    "ec029c6d-1460-4e69-bfbb-d17020213a4a": "Billable Amt (PO Recieved)",
    "7235dc23-6745-4780-971e-6dd6f9930407": "PO Billing Status",
    "ed82cd58-7ca7-435b-ac62-56e513f01f3b": "PO Comments:",
    "cd989a04-9a9c-41d7-ab14-3763c5861a1d": "FOC Status",
    "e75a6d5d-7ce9-4125-ae04-91899936f2e0": "FOC Comments",
    "e3d7eb05-1a66-41fe-8930-3e1be6367220": "Accounts Comment:",
    "519457b0-60fd-41a5-b1a7-02867d7a3b18": "Invoice Date",
    "c88cf034-8459-4dde-b0a2-5cadbd72a34e": "Invoice Number",
    "9f335621-9a6e-42bb-929f-561bf9a80c0f": "Invoice Amount",
    "********-8a16-419d-b219-f138cce1699a": "Mode of Payment",
    "3f95b6bc-1d46-40f4-95db-bf7803a7d16d": "Payment Received Amt",
    "9509eb46-3a2d-404b-aa4a-2be47fd14fcb": "Payment Received Date",
    "f3b24901-830a-4d9e-b79c-bfa62ff66d30": "Proforma Invoice No",
    "531d9100-5e46-43b2-ac09-e1185ab96ec9": "Proforma Invoice Sent Date",
    "f1548ce1-70a7-41ee-9f37-c4bd6f006195": "Completion Cretificate Rating:",
    "23560ebf-d4b4-4582-9050-1293ff224898":
      "Completion Certificate Feedback Remarks:",
    authority_428: "MKW OPS - Executive",
    authority_429: "MKW Billing Incharge",
    authority_430: "MKW Key Account Manager",
    authority_431: "MKW Ops Manager",
    authority_432: "MKW City Manager",
    authority_433: "MKW Regional Head",
    authority_441: "MKW Project Manager",
    avg_gai_rating: "Avg. GAI rating",
    no_of_gai_rated_tasks: "No. of GAI rated tasks",
    sp_locked_for_change: "SP Locked for change (Yes/No)",
    sp_locked_for_change_by: "SP Locked for change by",
    sp_locking_date: "SP Locking Date",
    mandays: "Mandays",
    sp_work_progress: "SP Work progress percentage",
    sp_total_quantity: "SP Total quantity",
    sp_total_price: "SP Total price",
    total_quantity: "Vendor Bill Amount",
    total_price: "Deductions",
    total_payout: "Paid Amount",
    final_amount: "Final amount",
    sp_sent_for_billing: "SP sent for billing",
    "ec12c224-8435-43b8-867f-766cb02f59de": "Drawing File",
    "770ddcf9-8ff2-4aa4-abd1-c20b8759baa5": "Take Over Document",
    "19e1a2b4-761f-40a9-ad95-665d5b154cd1": "Handover documents",
    "f07c83eb-a051-43e9-8412-a502d7f9d078": "SOW File",
    "a96c41b2-e820-47c1-9ecc-4216deb3f528": "PO File",
    "0aeccd19-e387-4461-b367-f0639abfd96b": "Completion Certificate",
    "452f4794-50a8-47fd-a192-93ed0ae72498": "QC File",
    "22f0e0d4-f5f1-4275-aaed-643a92b08475": "Proforma Invoice File",
    "dfb5e81e-c854-4658-a20d-dce8006adb77": "Invoice File",
    "0d9aef1c-f490-461c-af27-99b2afa02b14": "Payment Received Document:",
  },
  selected_columns: [
    "brand_name",
    "service_type",
    "vertical_fr_req_dump",
    "cust_full_name",
    "cust_line_0",
    "cust_line_1",
    "cust_line_2",
    "cust_line_3",
    "cust_pincode",
    "cust_city",
    "cust_state",
    "location_group",
    "request_description",
    "request_req_date",
    "request_priority",
    "vertical_title",
    "sp_first_task_date",
    "sp_last_task_date",
    "gai_remarks",
    "08ce8925-8923-4c80-923b-45e1dd17e0b0",
    "7033ab8c-22c1-4cf0-94e2-2c104b2bec26",
    "b274d89e-fafd-4789-999c-a848cd8e6483",
    "9ad89a24-27ab-44e1-9fe4-3f20be4316bf",
    "ceedcf1d-1f6e-4cb0-9cec-e6ae8e5c73e1",
    "ce8de42a-3a35-40b1-93f7-5871d6e5fe86",
    "9446ae26-5c77-496d-b87a-719b56573f4c",
    "4036b63d-ba39-4e00-9aa7-41351f763d97",
    "1b9e9baa-e830-455e-b1a7-9783aea5830b",
    "b2c8e073-49cc-4f92-a966-db94f3e0d310",
    "e1897795-58f6-4250-984c-00cb4cc6f7d2",
    "f14efb7f-6e52-4e2f-8807-36b6a1eef4d8",
    "f03a676a-6fae-420e-bf4f-644c089ee8ec",
    "e1a60e6e-20c3-45af-9063-48be25c1aa5b",
    "82264689-c5f4-4c76-b617-7ab134972a3d",
    "eadb139e-f076-4c4b-a815-42822fbd8c94",
    "660496d9-4bd9-453e-9bfb-6c64a699d785",
    "ecd2074e-57e1-483f-baba-df14d36863c0",
    "b8909ff9-edd2-4572-a82c-fe4720becbc0",
    "3584aede-4f10-4d07-9d9b-b64819546ace",
    "aa4b2005-c229-4c77-9b61-fe77d0ebe88b",
    "1ee680d8-7f92-49e8-94a3-0307661c084a",
    "0ddee6ff-5c6f-4747-b441-5b69d4ab71e7",
    "31eb4eee-d2df-434c-a2bd-c0e609cef195",
    "4c74dcc3-2ec4-4156-85e0-6a2d084292fd",
    "bc27d2ff-3664-445d-9516-c9a57c7339ba",
    "270cc594-bdc6-4d22-a467-f2e3b619e2fb",
    "9d30592d-deba-490e-887f-2ae78d094486",
    "50c978bb-4e59-4e90-9478-70ad580dab5c",
    "517d0b81-a559-47b7-a230-53d1ba46f211",
    "60c6f5ff-c03c-48ec-8f0b-7802802671ac",
    "ec029c6d-1460-4e69-bfbb-d17020213a4a",
    "7235dc23-6745-4780-971e-6dd6f9930407",
    "ed82cd58-7ca7-435b-ac62-56e513f01f3b",
    "cd989a04-9a9c-41d7-ab14-3763c5861a1d",
    "e75a6d5d-7ce9-4125-ae04-91899936f2e0",
    "e3d7eb05-1a66-41fe-8930-3e1be6367220",
    "519457b0-60fd-41a5-b1a7-02867d7a3b18",
    "c88cf034-8459-4dde-b0a2-5cadbd72a34e",
    "9f335621-9a6e-42bb-929f-561bf9a80c0f",
    "********-8a16-419d-b219-f138cce1699a",
    "3f95b6bc-1d46-40f4-95db-bf7803a7d16d",
    "9509eb46-3a2d-404b-aa4a-2be47fd14fcb",
    "f3b24901-830a-4d9e-b79c-bfa62ff66d30",
    "531d9100-5e46-43b2-ac09-e1185ab96ec9",
    "f1548ce1-70a7-41ee-9f37-c4bd6f006195",
    "23560ebf-d4b4-4582-9050-1293ff224898",
    "authority_428",
    "authority_429",
    "authority_430",
    "authority_431",
    "authority_432",
    "authority_433",
    "authority_441",
    "avg_gai_rating",
    "no_of_gai_rated_tasks",
    "sp_locked_for_change",
    "sp_locked_for_change_by",
    "sp_locking_date",
    "mandays",
    "sp_work_progress",
    "sp_total_quantity",
    "sp_total_price",
    "total_quantity",
    "total_price",
    "total_payout",
    "final_amount",
    "sp_sent_for_billing",
    "ec12c224-8435-43b8-867f-766cb02f59de",
    "770ddcf9-8ff2-4aa4-abd1-c20b8759baa5",
    "19e1a2b4-761f-40a9-ad95-665d5b154cd1",
    "f07c83eb-a051-43e9-8412-a502d7f9d078",
    "a96c41b2-e820-47c1-9ecc-4216deb3f528",
    "0aeccd19-e387-4461-b367-f0639abfd96b",
    "452f4794-50a8-47fd-a192-93ed0ae72498",
    "22f0e0d4-f5f1-4275-aaed-643a92b08475",
    "dfb5e81e-c854-4658-a20d-dce8006adb77",
    "0d9aef1c-f490-461c-af27-99b2afa02b14",
  ],
  filters:
    '{"assgn_to_prvdr_date":["2024-11-30T18:30:00.000Z","2024-12-05T18:30:00.000Z"],"verticals_list":[4]}',
  pagination: "{}",
  org_id: 2,
  usr_id: "e4dbc3e2-62cf-4957-8ff4-848b3f36c44f",
  ip_addr: "::1",
  user_agent:
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  srvc_type_id: "0",
  is_customer_access: "0",
};

const mkwOnline = {
  email_id: "<EMAIL>",
  subject: "WIFY TMS Customer requests dump Mar-03-2025 06:59 PM",
  org_info: ["service_type", "vertical_fr_req_dump", "brand_name"],
  customer_info_fields: ["cust_mobile", "cust_full_name", "cust_email"],
  address_info_fields: [
    "cust_line_0",
    "cust_line_1",
    "cust_line_2",
    "cust_line_3",
    "cust_pincode",
    "cust_city",
    "cust_state",
    "location_group",
  ],
  request_info_fields: [
    "request_description",
    "request_req_date",
    "vertical_title",
  ],
  onfield_task_fields: [
    "assignee_name",
    "task_status",
    "task_start_date",
    "assigned_by",
    "assigned_date",
    "assigned_time",
    "task_status_remark",
    "task_geo_verification_status",
    "assignee_phone_no.",
    "sp_first_task_date",
    "sp_last_task_date",
    "gai_remarks",
  ],
  SP_specific_fields: [
    "c0908131-3f05-4f0d-828f-603e02da8acc",
    "4f941084-0c1d-4b9b-b579-7e32d867c667",
    "68721d91-a09d-47a0-9764-21a5fe92dfcd",
    "a0d645cb-3e6c-42f6-a9ab-97813dc0d91b",
    "fe63737a-eb47-48c5-990e-ff4da48292ea",
    "d5e3b9a4-e937-4c53-bc28-c6e8b1881390",
    "06eed560-9c03-43d4-8498-85a7bc0990ea",
    "00d436e9-70e4-44fd-b401-7978a86abf1c",
    "0307ff84-95be-4209-8311-84117a1018e3",
    "c132ed19-f201-4c7f-8522-11e09432ba12",
    "d9f55aad-eccd-48dc-b2ec-fba10181deac",
    "1efb7702-2fac-4f3f-b0cf-45458ad12d08",
    "5fa29751-2f2d-4c01-a653-c76889c4c698",
    "ec4739ab-dd7f-44ac-8b00-d3be04ad7d6d",
    "65506b5b-4f65-480b-ad85-51a512eb5aee",
    "d9d969fa-f0fe-40f2-9a49-e6e0134fbbf5",
    "b3f6d5f4-1245-48a5-9c95-40b75b494b5f",
    "7df134d7-ca02-4822-ad62-336a4ca07fcd",
    "978177b9-1b99-4d21-abab-a6834c1cd8a3",
    "17fd266e-3d9e-4a98-92d1-3e89ec20dfdf",
    "760578fc-482f-4a3a-b1b8-dc8bd6e5b3b3",
  ],
  sp_authorities: [
    "authority_428",
    "authority_429",
    "authority_430",
    "authority_431",
    "authority_432",
    "authority_433",
  ],
  gai_info_fields: ["avg_gai_rating", "no_of_gai_rated_tasks"],
  locked_for_change: [
    "sp_locked_for_change",
    "sp_locked_for_change_by",
    "sp_locking_date",
  ],
  calculated_task_data: ["mandays", "sp_work_progress"],
  line_item_data: ["sp_total_quantity", "sp_total_price"],
  pi_info: ["final_amount", "sp_sent_for_billing"],
  sp_attachment_field_info: [
    "********-1466-45ae-bb43-9ed68b529636",
    "486e1342-02bc-4871-ba76-bcbd63d57fcf",
    "35a50d36-b3da-4ddb-bf47-f07500a9da7c",
    "b7a619b6-7f5b-4a9a-ac2f-8b6e8b7fe4eb",
  ],
  field_label_mapping: {
    brand_name: "Brand Name",
    service_type: "Service Type",
    vertical_fr_req_dump: "Vertical",
    cust_mobile: "Mobile(+91)",
    cust_full_name: "Name",
    cust_email: "Email",
    cust_line_0: "Flat no",
    cust_line_1: "Building/Apartment name",
    cust_line_2: "Line 1",
    cust_line_3: "Line 2",
    cust_pincode: "Pincode",
    cust_city: "City",
    cust_state: "State",
    location_group: "Location Group",
    request_description: "Description",
    request_req_date: "Req. Service Date",
    request_priority: "Priority",
    request_labels: "Labels",
    request_cc_users: "CC users",
    creation_date: "Creation Date",
    vertical_title: "Request vertical",
    assignee_name: "Assignee name",
    task_status: "Task status",
    task_start_date: "Task start date",
    assigned_by: "Assigned by",
    assigned_date: "Assigned date",
    assigned_time: "Assigned time",
    task_status_remark: "Task status remark",
    task_geo_verification_status: "GEO verification status",
    "assignee_phone_no.": "Assignee Phone No.",
    sp_first_task_date: "SP First task date",
    sp_last_task_date: "SP Last task date",
    gai_remarks: "GAI remarks",
    "c0908131-3f05-4f0d-828f-603e02da8acc": "Client ID",
    "4f941084-0c1d-4b9b-b579-7e32d867c667": "Required Reports: ",
    "68721d91-a09d-47a0-9764-21a5fe92dfcd": "Site Note",
    "a0d645cb-3e6c-42f6-a9ab-97813dc0d91b": "Completion Comments:",
    "fe63737a-eb47-48c5-990e-ff4da48292ea": "Fulfillment By",
    "d5e3b9a4-e937-4c53-bc28-c6e8b1881390": "Cleaning Sqft",
    "06eed560-9c03-43d4-8498-85a7bc0990ea": "PO Received Date",
    "00d436e9-70e4-44fd-b401-7978a86abf1c": "PO Number",
    "0307ff84-95be-4209-8311-84117a1018e3": "Po Received Amount",
    "c132ed19-f201-4c7f-8522-11e09432ba12": "Contractor Payout Date",
    "d9f55aad-eccd-48dc-b2ec-fba10181deac": "Contractor Payout Amt",
    "1efb7702-2fac-4f3f-b0cf-45458ad12d08": "FOC Status",
    "5fa29751-2f2d-4c01-a653-c76889c4c698": "FOC Comments",
    "ec4739ab-dd7f-44ac-8b00-d3be04ad7d6d": "Invoice Number",
    "65506b5b-4f65-480b-ad85-51a512eb5aee": "Invoice Amount",
    "d9d969fa-f0fe-40f2-9a49-e6e0134fbbf5": "Invoice Date",
    "b3f6d5f4-1245-48a5-9c95-40b75b494b5f": "Payment Received Amount",
    "7df134d7-ca02-4822-ad62-336a4ca07fcd": "Payment Received Date",
    "978177b9-1b99-4d21-abab-a6834c1cd8a3": "Accounts Comments:",
    "17fd266e-3d9e-4a98-92d1-3e89ec20dfdf": "TDS Amt:",
    "760578fc-482f-4a3a-b1b8-dc8bd6e5b3b3": "Transaction Amt:",
    authority_428: "MKW OPS - Executive",
    authority_429: "MKW Billing Incharge",
    authority_430: "MKW Key Account Manager",
    authority_431: "MKW Ops Manager",
    authority_432: "MKW City Manager",
    authority_433: "MKW Regional Head",
    avg_gai_rating: "Avg. GAI rating",
    no_of_gai_rated_tasks: "No. of GAI rated tasks",
    sp_locked_for_change: "SP Locked for change (Yes/No)",
    sp_locked_for_change_by: "SP Locked for change by",
    sp_locking_date: "SP Locking Date",
    mandays: "Mandays",
    sp_work_progress: "SP Work progress percentage",
    sp_total_quantity: "SP Total quantity",
    sp_total_price: "SP Total price",
    final_amount: "Final amount",
    sp_sent_for_billing: "SP sent for billing",
    "********-1466-45ae-bb43-9ed68b529636": "PO File",
    "486e1342-02bc-4871-ba76-bcbd63d57fcf": "Completion Certificate: ",
    "35a50d36-b3da-4ddb-bf47-f07500a9da7c": "Invoice File",
    "b7a619b6-7f5b-4a9a-ac2f-8b6e8b7fe4eb": "Pay.Rcvd File",
  },
  selected_columns: [
    "brand_name",
    "service_type",
    "vertical_fr_req_dump",
    "cust_mobile",
    "cust_full_name",
    "cust_email",
    "cust_line_0",
    "cust_line_1",
    "cust_line_2",
    "cust_line_3",
    "cust_pincode",
    "cust_city",
    "cust_state",
    "location_group",
    "request_description",
    "request_req_date",
    "vertical_title",
    "assignee_name",
    "task_status",
    "task_start_date",
    "assigned_by",
    "assigned_date",
    "assigned_time",
    "task_status_remark",
    "task_geo_verification_status",
    "assignee_phone_no.",
    "sp_first_task_date",
    "sp_last_task_date",
    "gai_remarks",
    "c0908131-3f05-4f0d-828f-603e02da8acc",
    "4f941084-0c1d-4b9b-b579-7e32d867c667",
    "68721d91-a09d-47a0-9764-21a5fe92dfcd",
    "a0d645cb-3e6c-42f6-a9ab-97813dc0d91b",
    "fe63737a-eb47-48c5-990e-ff4da48292ea",
    "d5e3b9a4-e937-4c53-bc28-c6e8b1881390",
    "06eed560-9c03-43d4-8498-85a7bc0990ea",
    "00d436e9-70e4-44fd-b401-7978a86abf1c",
    "0307ff84-95be-4209-8311-84117a1018e3",
    "c132ed19-f201-4c7f-8522-11e09432ba12",
    "d9f55aad-eccd-48dc-b2ec-fba10181deac",
    "1efb7702-2fac-4f3f-b0cf-45458ad12d08",
    "5fa29751-2f2d-4c01-a653-c76889c4c698",
    "ec4739ab-dd7f-44ac-8b00-d3be04ad7d6d",
    "65506b5b-4f65-480b-ad85-51a512eb5aee",
    "d9d969fa-f0fe-40f2-9a49-e6e0134fbbf5",
    "b3f6d5f4-1245-48a5-9c95-40b75b494b5f",
    "7df134d7-ca02-4822-ad62-336a4ca07fcd",
    "978177b9-1b99-4d21-abab-a6834c1cd8a3",
    "17fd266e-3d9e-4a98-92d1-3e89ec20dfdf",
    "760578fc-482f-4a3a-b1b8-dc8bd6e5b3b3",
    "authority_428",
    "authority_429",
    "authority_430",
    "authority_431",
    "authority_432",
    "authority_433",
    "avg_gai_rating",
    "no_of_gai_rated_tasks",
    "sp_locked_for_change",
    "sp_locked_for_change_by",
    "sp_locking_date",
    "mandays",
    "sp_work_progress",
    "sp_total_quantity",
    "sp_total_price",
    "final_amount",
    "sp_sent_for_billing",
    "********-1466-45ae-bb43-9ed68b529636",
    "486e1342-02bc-4871-ba76-bcbd63d57fcf",
    "35a50d36-b3da-4ddb-bf47-f07500a9da7c",
    "b7a619b6-7f5b-4a9a-ac2f-8b6e8b7fe4eb",
  ],
  filters:
    '{"assgn_to_prvdr_date":["2024-11-30T18:30:00.000Z","2024-12-05T18:30:00.000Z"],"verticals_list":[831]}',
  pagination: "{}",
  org_id: 2,
  usr_id: "e4dbc3e2-62cf-4957-8ff4-848b3f36c44f",
  ip_addr: "::1",
  user_agent:
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  srvc_type_id: "0",
  is_customer_access: "0",
};
