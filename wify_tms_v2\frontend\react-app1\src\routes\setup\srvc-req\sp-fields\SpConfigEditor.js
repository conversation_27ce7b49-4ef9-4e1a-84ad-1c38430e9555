import React, { Component } from 'react';
import { Modal, Form, Input, Button, Collapse, Tabs, Alert } from 'antd';
import FormBuilder from 'antd-form-builder';
import http_utils from '../../../../util/http_utils';
import CircularProgress from '../../../../components/CircularProgress';
import FormPreviewMeta from '../../../../components/wify-utils/FieldCreator/FormPreviewMeta';
import { ZoomInOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import LineItemManagementModule from '../service-types/project-components/LineItemManagementModule';
import PricingMaster from '../service-types/PricingMaster';
import TimePickerWidget from '../../../../components/wify-utils/TimePickerWidget';
import {
    BILLING_TYPE,
    PROJECT_BASED_SERVICE_TYPE,
    RATING_TYPE,
    getOptionsFrNatureOfServiceType,
    handleClearSelect,
    validateLambdaArn,
} from '../../../../util/helpers';
import {
    decodeFieldsMetaFrmJson,
    decodeFileSectionsFrmJson,
} from '../../../../components/wify-utils/FieldCreator/helpers';
import SpecificFieldsWiseNotificationModule from '../service-types/project-components/SpecificFieldsWiseNotificationModule';
import BillingDiscountingConfigModule from '../service-types/project-components/BillingDiscountingConfigModule';
import {
    addDefaultKeysForTabularView,
    getDefaultColumnsForTabularView,
    getRatingFormMeta,
    getSrvcReqStaticAndCustomPossibleFields,
} from '../../../services/helpers';
import { getSrvcReqStaticAndCustomPossibleFieldsFilter } from '../../../services/helpers';
import UserSelectorWidget from '../../../../components/wify-utils/UserSelectorWidget';
import { getUsersInfoMeta } from '../../../users/helper';
import ConfigHelpers from '../../../../util/ConfigHelpers';
import { SP_USER_EXCLUDED_FIELDS } from '../../../../util/constants';
import InputTable from '../../../../components/WIFY/subtasks/InputTable';
import ProfitLossConfig from './ProfitLossConfig.tsx';
import ProjectPLConfig from './ProjectPLConfig.tsx';
import InlineCheckbox from '../../../../components/WIFY/InlineCheckbox.tsx';

const protoUrl = '/setup/sp-custom-fields/proto';
const submitUrl = '/setup/sp-custom-fields';
const { TextArea } = Input;
const startOfDay = '12:00AM';
const endOfDay = '11:45PM';

const DailyUpdateModes = [
    {
        value: 'lenient_mode',
        label: 'Lenient mode(Progress is autofilled till the previous date and no restriction in progress slider movement)',
    },
    {
        value: 'strict_mode',
        label: 'Strict mode(Progress is autofilled till the previous date and cannot be modified out of permissible range)',
    },
];
class SpConfigEditor extends Component {
    constructor(props) {
        super(props);
        this.formRef = React.createRef();
    }

    initState = {
        render_helper: false,
        visible: false,
        isFormSubmitting: false,
        viewData: undefined,
        isLoadingViewData: false,
        editMode: this.props.editMode,
        error: '',
        editModeForceRefreshDone: false,
        spSrvcTypeSpecificFieldsNotification: undefined,
        profitLossTab: false,
        revenuecol: [],
    };
    state = this.initState;

    componentDidMount() {
        this.initViewData();
    }

    initViewData() {
        if (
            (this.state.editMode && this.state.visible) ||
            (!this.state.editMode &&
                this.state.viewData == undefined &&
                !this.state.isLoadingViewData)
        ) {
            this.setState({
                isLoadingViewData: true,
            });
            var params = {};
            const onComplete = (resp) => {
                this.setState({
                    isLoadingViewData: false,
                    viewData: resp.data,
                    error: '',
                });
            };
            const onError = (error) => {
                // console.log(error.response.status);
                this.setState({
                    isLoadingViewData: false,
                    error: http_utils.decodeErrorToMessage(error),
                });
            };
            var url = !this.state.editMode
                ? protoUrl
                : protoUrl + '/' + this.props.editorItem.id;
            // console.log(url);
            http_utils.performGetCall(url, params, onComplete, onError);
        }
    }

    componentDidUpdate(prevProps, prevState) {
        if (
            prevProps.editorItem != this.props.editorItem ||
            prevProps.showEditor != this.props.showEditor
        ) {
            this.setState(
                {
                    render_helper: !this.state.render_helper,
                    visible: this.props.showEditor,
                },
                function () {
                    if (this.props.showEditor && this.state.editMode) {
                        this.initViewData();
                    }
                }
            );
        } else {
            if (this.state.refreshOnUpdate) {
                this.setState(
                    {
                        refreshOnUpdate: false,
                    },
                    this.initViewData()
                );
            }
        }
    }

    handleOk = () => {
        this.setState({
            visible: false,
            isFormSubmitting: false,
        });
        this.updateClosureToParent();
    };

    updateClosureToParent() {
        if (this.props.onClose != undefined) {
            this.props.onClose();
        }
        this.setState({
            refreshOnUpdate: true,
            ...this.initState,
        });
    }

    tellParentToRefreshList(entry_id) {
        if (this.props.onDataModified != undefined) {
            this.props.onDataModified(entry_id);
        }
    }

    handleCancel = () => {
        this.setState({
            visible: false,
        });
        this.updateClosureToParent();
    };

    submitForm = (data) => {
        this.setState({
            isFormSubmitting: true,
        });
        var params = data;
        if (data?.srvc_type_revenue_column_meta) {
            const srvc_type_revenue_column_meta = JSON.parse(
                data?.srvc_type_revenue_column_meta
            );
            if (srvc_type_revenue_column_meta) {
                srvc_type_revenue_column_meta.forEach((singleObj) => {
                    console.log('yeti singleObj', singleObj);
                    if (
                        this.state.viewData?.form_data?.form_data?.[
                            `sku_${singleObj.row_id}`
                        ] &&
                        !data?.[`sku_${singleObj.row_id}`]
                    ) {
                        params[`sku_${singleObj.row_id}`] =
                            singleObj?.sku || '';
                    }
                    if (
                        this.state.viewData?.form_data?.form_data?.[
                            `item_name_${singleObj.row_id}`
                        ] &&
                        !data?.[`item_name_${singleObj.row_id}`]
                    ) {
                        params[`item_name_${singleObj.row_id}`] =
                            singleObj?.item_name || '';
                    }
                    if (
                        this.state.viewData?.form_data?.form_data?.[
                            `qty_${singleObj.row_id}`
                        ] &&
                        !data?.[`qty_${singleObj.row_id}`]
                    ) {
                        params[`qty_${singleObj.row_id}`] =
                            singleObj?.qty || '';
                    }
                });
            }
        }
        if (data?.revenue_column_meta) {
            const revenue_column_meta = JSON.parse(data?.revenue_column_meta);
            if (revenue_column_meta) {
                revenue_column_meta.forEach((singleObj) => {
                    if (
                        this.state.viewData?.form_data?.form_data?.[
                            `sku_${singleObj.row_id}`
                        ] &&
                        !data?.[`sku_${singleObj.row_id}`]
                    ) {
                        params[`sku_${singleObj.row_id}`] = '';
                    }
                    if (
                        this.state.viewData?.form_data?.form_data?.[
                            `item_name_${singleObj.row_id}`
                        ] &&
                        !data?.[`item_name_${singleObj.row_id}`]
                    ) {
                        params[`item_name_${singleObj.row_id}`] = '';
                    }
                    if (
                        this.state.viewData?.form_data?.form_data?.[
                            `qty_${singleObj.row_id}`
                        ] &&
                        !data?.[`qty_${singleObj.row_id}`]
                    ) {
                        params[`qty_${singleObj.row_id}`] = '';
                    }
                });
            }
        }

        const onComplete = (resp) => {
            this.setState({
                isFormSubmitting: false,
                error: '',
                visible: false,
            });
            this.tellParentToRefreshList(resp.entry_id);
            this.updateClosureToParent();
        };
        const onError = (error) => {
            // compare statuses here
            this.setState({
                isFormSubmitting: false,
                error: http_utils.decodeErrorToMessage(error),
            });
        };
        if (this.state.editMode) {
            http_utils.performPutCall(
                submitUrl + '/' + this.props.editorItem.id,
                params,
                onComplete,
                onError
            );
        } else {
            http_utils.performPostCall(submitUrl, params, onComplete, onError);
        }
    };
    handleRowDataChange(newRowData) {
        this.setState({ revenuecol: newRowData });
    }

    getLineItemConfig() {
        if (this.formRef?.current?.getFieldValue) {
            const fieldJson = this.formRef.current.getFieldValue(
                'srvc_type_line_item_config'
            );
            if (fieldJson) {
                return JSON.parse(fieldJson);
            }
        }
        if (
            this.state.viewData?.form_data?.form_data
                ?.srvc_type_line_item_config
        ) {
            return JSON.parse(
                this.state.viewData?.form_data?.form_data
                    ?.srvc_type_line_item_config
            );
        }
        return {};
    }

    getConfigFrSpPayouts() {
        //here
        if (this.formRef?.current?.getFieldValue) {
            let fieldJson = this.formRef.current.getFieldValue(
                'srvc_type_sp_payouts_config'
            );
            //Set sp payouts initial values
            if (fieldJson == undefined) {
                fieldJson =
                    '{"1236c271-876d-4352-a044-157acbeee076":{"key":"1236c271-876d-4352-a044-157acbeee076","label":"Payouts"}}';
            }
            if (fieldJson) {
                return JSON.parse(fieldJson);
            }
        }
        if (
            this.state.viewData?.form_data?.form_data
                ?.srvc_type_sp_payouts_config
        ) {
            return JSON.parse(
                this.state.viewData?.form_data?.form_data
                    ?.srvc_type_sp_payouts_config
            );
        }
        return {};
    }

    getConfigFrDeduction() {
        let fieldJson = this.formRef?.current?.getFieldValue(
            'srvc_type_deduction_config'
        );

        if (!fieldJson) {
            fieldJson =
                '{"deduction_item":{"key":"deduction_item","label":"Deduction"}}';
        }

        if (fieldJson) {
            return JSON.parse(fieldJson);
        }

        if (
            this.state.viewData?.form_data?.form_data
                ?.srvc_type_deduction_config
        ) {
            return JSON.parse(
                this.state.viewData?.form_data?.form_data
                    ?.srvc_type_deduction_config
            );
        }
        return {};
    }

    getMetaFrLineItemConfiguration() {
        const lineItemConfig = this.getLineItemConfig();
        let who_cannot_download_sp_line_item = [];
        let who_cannot_view_line_items_tab = [];
        if (Object.keys(lineItemConfig).length > 0) {
            who_cannot_download_sp_line_item.push({
                key: 'who_cannot_download_sp_line_item',
                label: (
                    <span style={{ marginTop: 20 }}>
                        {' '}
                        Who cannot download SP line items{' '}
                    </span>
                ),
                widget: 'select',
                widgetProps: {
                    mode: 'multiple',
                    allowClear: true,
                    showSearch: true,
                    optionFilterProp: 'children',
                },
                options: this.state.viewData?.role_list || [],
            });
            who_cannot_view_line_items_tab.push({
                key: 'who_cannot_view_line_items_tab',
                label: <span> Who cannot view line items tab</span>,
                formItemLayout: null,
                widget: 'select',
                widgetProps: {
                    mode: 'multiple',
                    allowClear: true,
                    showSearch: true,
                    optionFilterProp: 'children',
                },
                options: this.state.viewData?.role_list || [],
            });
        }
        const meta = {
            columns: 1,
            formItemLayout: null,
            fields: [
                {
                    label: 'Service type line item config json',
                    key: 'srvc_type_line_item_config',
                    formItemProps: {
                        style: {
                            display: 'none',
                        },
                    },
                },
                {
                    key: 'Line items',
                    render: () => (
                        <>
                            <LineItemManagementModule
                                form={this.formRef}
                                onChange={(newObj) => {
                                    this.formRef.current.setFieldsValue({
                                        srvc_type_line_item_config:
                                            JSON.stringify(newObj),
                                    });
                                    this.refresh();
                                }}
                                currValue={lineItemConfig}
                            />
                        </>
                    ),
                },
                ...who_cannot_download_sp_line_item,
                ...who_cannot_view_line_items_tab,
                {
                    key: 'seperator_6',
                    render: () => (
                        <div className="gx-mb-3 gx-bg-grey">
                            <hr></hr>
                        </div>
                    ),
                },
            ],
        };
        return meta;
    }

    getInitialValues() {
        return this.state.editMode
            ? this.state.viewData?.form_data?.form_data
            : {};
    }

    getOrgListBasedOnNature() {
        let vertical_nature =
            this.formRef?.current?.getFieldValue('vertical_nature');

        let org_list = [...this.state.viewData.org_list];
        let filtered_org_list = [];
        filtered_org_list = org_list.filter((singleOrgSrvcTypeObj) => {
            return singleOrgSrvcTypeObj.srvc_type_nature == vertical_nature;
        });
        return filtered_org_list;
    }
    getMetaFrSearchableCustomFields() {
        let prefix = 'searchable_sp_fields';
        return [
            {
                key: `${prefix}_form_data`,
                label: 'Select searchable SP fields',
                formItemProps: {
                    style: {
                        display: 'none',
                    },
                },
            },
            {
                key: `${prefix}`,
                label: 'Select searchable SP fields',
                widget: 'select',
                widgetProps: {
                    mode: 'multiple',
                },
                options: this.getSearchableCustomFields(),
                onChange: (selectedField, selectedOption) => {
                    let filterConfigValue = [];
                    selectedOption.map((singleElem) => {
                        filterConfigValue.push({
                            search_keyword: singleElem.label,
                            key: singleElem.key,
                        });
                    });
                    this.formRef.current.setFieldsValue({
                        searchable_sp_fields_form_data: filterConfigValue,
                    });
                },
            },
        ];
    }

    getMeta = () => {
        const initialValues = this.getInitialValues();
        let FieldsInitialValues = initialValues;
        if (
            initialValues?.sp_cust_fields_json == undefined ||
            initialValues?.sp_cust_fields_json == ''
        ) {
            FieldsInitialValues = {};
        }
        var customFields = this.formRef?.current?.getFieldValue(
            'sp_cust_fields_json'
        );
        if (customFields == undefined) {
            customFields = initialValues?.sp_cust_fields_json;
        }
        const isCustFieldDynamicEnabled = this.formRef?.current?.getFieldValue(
            'is_custom_fields_dynamic'
        );

        const meta = {
            columns: 1,
            formItemLayout: null,
            initialValues: FieldsInitialValues,
            fields: [
                {
                    key: 'vertical_title',
                    label: 'Vertical title(Unique)',
                    required: true,
                    rules: [
                        {
                            max: 50,
                        },
                    ],
                },
                {
                    key: 'vertical_desc',
                    label: 'Vertical description',
                    widget: 'textarea',
                },
                {
                    label: <span>Nature of requests </span>,
                    key: 'vertical_nature',
                    widget: 'radio-group',
                    required: true,
                    onChange: () => {
                        this.formRef.current.setFieldsValue({
                            srvc_type_id: [],
                        });
                        this.refresh();
                    },
                    // widgetProps:{
                    //     defaultValue:'task_based'
                    // },
                    options: getOptionsFrNatureOfServiceType(),
                },
                {
                    key: 'srvc_type_id',
                    label: 'Select organization name',
                    widget: 'select',
                    widgetProps: {
                        mode: 'multiple',
                        optionFilterProp: 'children',
                        onChange: () => this.refresh(),
                    },
                    options: this.getOrgListBasedOnNature(),
                    rules: [
                        {
                            required: true,
                            message: 'Please select organization name',
                        },
                    ],
                    placeholder: 'Please select org name',
                },
                {
                    label: 'Enter custom fields json',
                    key: 'sp_cust_fields_json',
                    widget: 'textarea',
                    placeholder: 'Please paste the JSON here',
                    rules: [
                        {
                            required: true,
                            message: 'Please enter custom fields json',
                        },
                    ],
                    onChange: (event) => {
                        this.setState({
                            render_helper: !this.state.render_helper,
                        });
                    },
                },
                {
                    key: 'link_to_field_creator',
                    render: () => {
                        return (
                            <div>
                                {customFields != '' && (
                                    <div>
                                        <Button
                                            onClick={(e) =>
                                                this.handleShowFieldsPreviewClick(
                                                    customFields
                                                )
                                            }
                                            icon={<ZoomInOutlined />}
                                        >
                                            Form preview
                                        </Button>
                                    </div>
                                )}
                                <Link
                                    to={
                                        '/fields-creator?edit=' +
                                        encodeURIComponent(customFields)
                                    }
                                    target="_blank"
                                >
                                    Open field creator {'-->'}
                                </Link>
                            </div>
                        );
                    },
                },
                {
                    key: `is_custom_fields_dynamic`,
                    label: (
                        <div className="gx-mt-2 ">
                            Are custom fields dynamic?
                        </div>
                    ),
                    widget: 'checkbox',
                    onChange: () => this.refresh(),
                },
                ...(isCustFieldDynamicEnabled
                    ? [
                          {
                              key: `sp_cust_fields_dynamic_form_lambda_arn`,
                              label: `Enter custom fields lambda arn`,
                              placeholder: 'Lambda ARN',
                              required: true,
                              widgetProps: {
                                  allowClear: true,
                                  onChange: (value) => {
                                      handleClearSelect(
                                          value,
                                          this.formRef,
                                          'sp_cust_fields_dynamic_form_lambda_arn'
                                      );
                                  },
                              },
                              rules: [
                                  {
                                      validator: validateLambdaArn,
                                      message:
                                          'Please enter a valid Lambda ARN',
                                  },
                              ],
                          },
                      ]
                    : []),
                {
                    label: 'Service provider category field',
                    key: `srvc_prvdr_category_field`,
                    widget: 'select',
                    options: this.getPossibleSpecificFields([
                        {
                            widget: 'select',
                        },
                    ]),
                    widgetProps: {
                        allowClear: true,
                        onChange: (value) => {
                            handleClearSelect(
                                value,
                                this.formRef,
                                'srvc_prvdr_category_field'
                            );
                        },
                    },
                },
                {
                    label: 'Specific location groups (Leave empty for all)',
                    key: `specific_loc_grps`,
                    widget: 'select',
                    options: this.state.viewData?.location_grp_list || [],
                    tooltip:
                        'If you want to restrict location groups that will be tagged to requests for this vertical',
                    widgetProps: {
                        mode: 'multiple',
                        optionFilterProp: 'children',
                        allowClear: true,
                        onChange: (value) => {
                            handleClearSelect(
                                value,
                                this.formRef,
                                'specific_loc_grps'
                            );
                        },
                    },
                },
                // ...this.getMetaFrLineItemConfiguration(),
                ...this.getMetaFrSearchableCustomFields(),
            ],
        };
        return meta;
    };

    getPricingConfig() {
        if (this.state.editMode) {
            if (this.formRef?.current?.getFieldValue) {
                const fieldJson = this.formRef.current.getFieldValue(
                    'srvc_type_pricing_config_for_line_item'
                );
                if (fieldJson) {
                    return JSON.parse(fieldJson);
                }
            }
            const config_in_form_data =
                this.state.viewData?.form_data?.form_data
                    ?.srvc_type_pricing_config_for_line_item;
            return config_in_form_data ? JSON.parse(config_in_form_data) : {};
        }
        return {};
    }

    onSaveOfPricingConfig(data, singleLineItemGrpKey) {
        let pricingConfigData = this.getPricingConfig();
        pricingConfigData[singleLineItemGrpKey] = data;
        this.formRef.current.setFieldsValue({
            srvc_type_pricing_config_for_line_item:
                JSON.stringify(pricingConfigData),
        });
    }

    getPricingConfigFrManday() {
        if (this.state.editMode) {
            if (this.formRef?.current?.getFieldValue) {
                const fieldJson = this.formRef.current.getFieldValue(
                    'srvc_type_pricing_config_for_manday'
                );
                if (fieldJson) {
                    return JSON.parse(fieldJson);
                }
            }
            const config_in_form_data =
                this.state.viewData?.form_data?.form_data
                    ?.srvc_type_pricing_config_for_manday;
            return config_in_form_data ? JSON.parse(config_in_form_data) : {};
        }
        return {};
    }

    onSaveOfPricingConfigFrManday(data) {
        let pricingConfigDataFrManday = this.getPricingConfigFrManday();
        pricingConfigDataFrManday['srvc_type_pricing_config_for_manday'] = data;
        this.formRef.current.setFieldsValue({
            srvc_type_pricing_config_for_manday: JSON.stringify(
                pricingConfigDataFrManday
            ),
        });
    }

    getSelectedOrgWithSrvcTypeIds() {
        let selectedOrgs = [];
        let srvcTypeIds =
            this.formRef?.current?.getFieldValue('srvc_type_id') ||
            this.getInitialValues()?.srvc_type_id;
        let org_list = this.state.viewData.org_list;
        if (Array.isArray(srvcTypeIds) && srvcTypeIds.length > 0) {
            srvcTypeIds.forEach((singlesrvcTypeId) => {
                let selectedOrgsList = org_list.filter(
                    (singleOrg) => singleOrg.value == singlesrvcTypeId
                )?.[0];
                selectedOrgs.push(selectedOrgsList);
            });
        }
        return selectedOrgs;
    }

    getPricingMasterConfigMeta(initialValues) {
        let lineItemConfigData = this.getLineItemConfig();
        let pricingConfigData = this.getPricingConfig();
        let pricingConfigDataFrManday = this.getPricingConfigFrManday();
        return {
            fields: [
                {
                    label: 'Service provider line item pricing config json',
                    key: 'srvc_type_pricing_config_for_line_item',
                    formItemProps: {
                        style: {
                            display: 'none',
                        },
                    },
                },
                {
                    label: 'Service type line item pricing config json for manday',
                    key: 'srvc_type_pricing_config_for_manday',
                    formItemProps: {
                        style: {
                            display: 'none',
                        },
                    },
                },
                {
                    key: `sp_srvc_type_pricing_master_config`,
                    render: () => {
                        return (
                            <Tabs defaultActiveKey="line_item">
                                {Object.keys(lineItemConfigData).length > 0 && (
                                    <Tabs.TabPane
                                        tab="Line Items"
                                        key="line_item"
                                    >
                                        <Tabs defaultActiveKey="line_item">
                                            {Object.keys(
                                                lineItemConfigData
                                            ).map(
                                                (
                                                    singleLineItemGrpKey,
                                                    index
                                                ) => (
                                                    <Tabs.TabPane
                                                        key={index}
                                                        tab={
                                                            lineItemConfigData?.[
                                                                singleLineItemGrpKey
                                                            ]?.label
                                                        }
                                                    >
                                                        <PricingMaster
                                                            pricingInitialValue={
                                                                pricingConfigData[
                                                                    singleLineItemGrpKey
                                                                ]
                                                            }
                                                            fullFormInitialValue={
                                                                initialValues
                                                            }
                                                            form={this.formRef}
                                                            config_data={
                                                                lineItemConfigData[
                                                                    singleLineItemGrpKey
                                                                ]
                                                            }
                                                            onChange={(
                                                                data
                                                            ) => {
                                                                // WE GET pricing for single line item group here
                                                                this.onSaveOfPricingConfig(
                                                                    data,
                                                                    singleLineItemGrpKey
                                                                );
                                                            }}
                                                            editMode={
                                                                this.state
                                                                    .editMode
                                                            }
                                                            org_list={this.getSelectedOrgWithSrvcTypeIds()}
                                                        />
                                                    </Tabs.TabPane>
                                                )
                                            )}
                                        </Tabs>
                                    </Tabs.TabPane>
                                )}
                                <Tabs.TabPane tab="Manday">
                                    <PricingMaster
                                        isManday
                                        pricingInitialValue={
                                            pricingConfigDataFrManday?.srvc_type_pricing_config_for_manday
                                        }
                                        fullFormInitialValue={initialValues}
                                        form={this.formRef}
                                        config_data={
                                            this.state.viewData
                                                ?.location_grp_list
                                        }
                                        onChange={(data) => {
                                            // WE GET pricing for single line item group here
                                            this.onSaveOfPricingConfigFrManday(
                                                data
                                            );
                                        }}
                                        editMode={this.state.editMode}
                                        org_list={this.getSelectedOrgWithSrvcTypeIds()}
                                    />
                                </Tabs.TabPane>
                            </Tabs>
                        );
                    },
                },
            ],
        };
    }

    getPossibleSpecificFields(essentialFields = []) {
        let labelValuePairData = [];
        let customFields = this.formRef?.current?.getFieldValue(
            'sp_cust_fields_json'
        );
        if (customFields && customFields != '') {
            customFields = decodeFieldsMetaFrmJson(
                customFields,
                undefined,
                true
            );
            customFields.forEach((singleField) => {
                if (
                    essentialFields.length == 0 ||
                    this.checkIfPresentInEssentialField(
                        singleField,
                        essentialFields
                    )
                ) {
                    labelValuePairData.push({
                        label: singleField.label,
                        value: singleField.key,
                        key: singleField.key,
                    });
                }
            });
        }
        return labelValuePairData;
    }

    checkIfPresentInEssentialField = (singleField, essentialFields) => {
        for (let essentialField of essentialFields) {
            if (
                singleField.widget == essentialField.widget &&
                singleField.widgetProps.mode != 'multiple'
            ) {
                return true;
            }
        }
    };

    getSearchableCustomFields() {
        let labelValuePairData = [];
        let customFields = this.formRef?.current?.getFieldValue(
            'sp_cust_fields_json'
        );
        if (customFields && customFields != '') {
            customFields = decodeFieldsMetaFrmJson(customFields);
            customFields.forEach((singleField) => {
                if (
                    singleField?.cust_component != 'linebreak' &&
                    singleField?.type != 'Barcode_scanner' &&
                    singleField?.cust_component != 'legend'
                ) {
                    if (
                        singleField?.widget != 'select' &&
                        singleField?.widget != 'radio-group' &&
                        singleField?.widget != 'checkbox-group' &&
                        singleField?.cust_widget != 'Rating' &&
                        singleField.widget != 'date-picker'
                    ) {
                        labelValuePairData.push({
                            value: singleField.key,
                            label: singleField.label,
                            require: singleField?.required,
                        });
                    }
                }
            });
        }
        return labelValuePairData;
    }

    getPossibleSpecificFilesFields() {
        let customFileFields = this.formRef?.current?.getFieldValue(
            'sp_cust_fields_json'
        );
        if (customFileFields && customFileFields != '') {
            customFileFields = decodeFileSectionsFrmJson(customFileFields);
        }
        return customFileFields;
    }

    //Create a new component for Billing to be done later
    getBillingformMeta = (initialValues) => {
        const meta = {
            formItemLayout: null,
            initialValues: initialValues,
            fields: [
                {
                    label: 'Enable Billing',
                    key: 'srvc_type_enable_billing',
                    widget: 'checkbox',
                },
                {
                    key: 'srvc_type_billing_type',
                    label: 'Billing Type',
                    widget: 'select',
                    widgetProps: {
                        mode: 'single',
                    },
                    options: BILLING_TYPE,
                },
                {
                    key: 'srvc_type_categorize_specific_fields_for_billing',
                    label: 'Categorize specific fields for billing section',
                    widget: 'select',
                    widgetProps: {
                        mode: 'multiple',
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                    options: this.getPossibleSpecificFields() || [],
                },
                {
                    key: 'srvc_type_who_can_lock_srvc_req_for_billing',
                    label: 'Who can lock service request for billing',
                    widget: 'select',
                    widgetProps: {
                        mode: 'multiple',
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                    options: this.state.viewData?.role_list || [],
                },
                {
                    key: 'srvc_type_who_can_sync_srvc_req_prc',
                    label: 'Who can sync service request price',
                    widget: 'select',
                    widgetProps: {
                        mode: 'multiple',
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                    options: this.state.viewData?.role_list || [],
                },
                {
                    key: 'srvc_type_who_can_send_req_for_billing',
                    label: 'Who can send a Request for billing',
                    widget: 'select',
                    widgetProps: {
                        mode: 'multiple',
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                    options: this.state.viewData?.role_list || [],
                },
                {
                    key: 'srvc_type_who_will_get_notified_for_billing',
                    label: 'Who will get notified for billing',
                    widget: 'select',
                    widgetProps: {
                        mode: 'multiple',
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                    options: this.state.viewData?.role_list || [],
                },
                {
                    key: 'hide_sp_billing_tab_from_specific_roles',
                    label: 'Who will not be able to see the billing tab',
                    widget: 'select',
                    widgetProps: {
                        mode: 'multiple',
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                    options: this.state.viewData?.role_list || [],
                },
            ],
        };
        return meta;
    };

    getBillingDiscountConfig() {
        if (this.state.editMode) {
            if (this.formRef?.current?.getFieldValue) {
                const fieldJson = this.formRef.current.getFieldValue(
                    'srvc_type_billing_discounting_rule_config'
                );
                if (fieldJson) {
                    return JSON.parse(fieldJson);
                }
            }
            let initialFieldJson =
                this.state.viewData?.form_data
                    ?.srvc_type_billing_discounting_rule_config;
            if (initialFieldJson) {
                return JSON.parse(initialFieldJson);
            }
        }
        return {};
    }

    getDiscountingBillingformMeta = (initialValues) => {
        const meta = {
            formItemLayout: null,
            initialValues: initialValues,
            fields: [
                {
                    label: 'Enable Discounting',
                    key: 'srvc_type_enable_billing_discounting',
                    widget: 'checkbox',
                },
                {
                    label: (
                        <span>
                            {' '}
                            When discount approval status changes, notify{' '}
                        </span>
                    ),
                    key: `srvc_type_discount_approval_status_changes_notify`,
                    widget: 'select',
                    options: this.state.viewData?.role_list || [],
                    widgetProps: {
                        allowClear: true,
                        mode: 'multiple',
                    },
                },
                {
                    key: `srvc_type_discounting_billing_master`,
                    render: () => {
                        return (
                            <BillingDiscountingConfigModule
                                onChange={(newObj) => {
                                    this.formRef.current.setFieldsValue({
                                        srvc_type_billing_discounting_rule_config:
                                            JSON.stringify(newObj),
                                    });
                                    this.refresh();
                                }}
                                currValue={this.getBillingDiscountConfig()}
                                authorities_list={
                                    this.state.viewData?.role_list || []
                                }
                            />
                        );
                    },
                },
            ],
        };
        return meta;
    };

    getAdditionalBillingformMeta = (initialValues) => {
        const meta = {
            formItemLayout: null,
            initialValues: initialValues,
            fields: [
                {
                    label: 'Enable Additional Billing',
                    key: 'srvc_type_enable_additional_billing',
                    widget: 'checkbox',
                },
                {
                    key: `srvc_type_additional_billing_master`,
                    render: () => {
                        return (
                            <>
                                <LineItemManagementModule
                                    onChange={(newObj) => {
                                        this.formRef.current.setFieldsValue({
                                            srvc_type_additional_billing_config:
                                                JSON.stringify(newObj),
                                        });
                                        this.refresh();
                                    }}
                                    currValue={this.getAdditionalBillingJson()}
                                    hideBtn
                                />
                            </>
                        );
                    },
                },
            ],
        };
        return meta;
    };

    getAdditionalBillingJson() {
        if (this.state.editMode) {
            if (this.formRef?.current?.getFieldValue) {
                const fieldJson = this.formRef.current.getFieldValue(
                    'srvc_type_additional_billing_config'
                );
                if (fieldJson) {
                    return JSON.parse(fieldJson);
                }
            }
            return {
                additional_line_item: {
                    key: 'additional_line_item',
                    label: 'Additional line items',
                },
            };
        }
        return {};
    }
    getDeductionConfigMeta() {
        const isDeductionEnable = this.formRef?.current?.getFieldValue(
            'is_enable_deduction'
        );

        const deductionConfig = this.getConfigFrDeduction();

        return {
            formItemLayout: null,
            fields: [
                {
                    key: 'is_enable_deduction',
                    label: 'Enable Deduction',
                    widget: 'checkbox',
                    onChange: (e) => {
                        this.refresh();
                        if (
                            e.target.checked == true &&
                            this.formRef.current.getFieldValue(
                                'srvc_type_deduction_config'
                            ) == undefined
                        ) {
                            this.formRef.current.setFieldsValue({
                                srvc_type_deduction_config: JSON.stringify({
                                    deduction_item: {
                                        key: 'deduction_item',
                                        label: 'Deduction',
                                        total_field_label: '',
                                    },
                                }),
                            });
                        }
                    },
                },
                ...(isDeductionEnable
                    ? [
                          {
                              label: 'Service type deduction config json',
                              key: 'srvc_type_deduction_config',
                              formItemProps: {
                                  style: {
                                      display: 'none',
                                  },
                              },
                          },
                          {
                              key: 'Deduction',
                              render: () => (
                                  <>
                                      <LineItemManagementModule
                                          form={this.formRef}
                                          onChange={(newObj) => {
                                              this.formRef.current.setFieldsValue(
                                                  {
                                                      srvc_type_deduction_config:
                                                          JSON.stringify(
                                                              newObj
                                                          ),
                                                  }
                                              );
                                              this.refresh();
                                          }}
                                          currValue={deductionConfig}
                                          isDeduction
                                          hideBtn
                                      />
                                  </>
                              ),
                          },
                      ]
                    : []),
            ],
        };
    }

    getFieldsWiseNotificationConfigMeta(initialValues) {
        return {
            fields: [
                {
                    key: `sp_srvc_type_fields_wise_notification_config`,
                    render: () => {
                        return (
                            <Tabs defaultActiveKey="field_wise">
                                <Tabs.TabPane tab="Field Wise" key="field_wise">
                                    <FormBuilder
                                        form={this.formRef}
                                        meta={this.getFieldWiseAuthorityNotification(
                                            initialValues
                                        )}
                                    />
                                </Tabs.TabPane>
                            </Tabs>
                        );
                    },
                },
            ],
        };
    }

    getFieldWiseAuthorityNotification(initialValues) {
        return {
            fields: [
                {
                    key: `sp_srvc_type_field_wise_authority_notification_config`,
                    render: () => {
                        return (
                            <Tabs
                                defaultActiveKey="authority"
                                className="gx-mb-2"
                            >
                                <Tabs.TabPane tab="Authority" key="authority">
                                    <FormBuilder
                                        form={this.formRef}
                                        meta={this.getSpecificFieldsWiseNotificationFieldsMeta(
                                            initialValues
                                        )}
                                    />
                                </Tabs.TabPane>
                            </Tabs>
                        );
                    },
                },
            ],
        };
    }

    getAuthoritiesList = () => {
        let authoritiesList = [];
        let authority_id = this.formRef?.current?.getFieldValue('authority_id');
        let roleList = this.state.viewData?.role_list;
        if (authority_id && authority_id.length > 0) {
            authority_id.forEach((singleAuthority) => {
                let filteredAuthorityList = roleList.filter(
                    (singleRole) => singleRole.value == singleAuthority
                )?.[0];
                if (filteredAuthorityList) {
                    authoritiesList.push(filteredAuthorityList);
                }
            });
        }
        return authoritiesList;
    };

    getSpecificFieldsWiseNotificationFieldsMeta = (initialValues) => {
        const meta = {
            formItemLayout: null,
            initialValues: initialValues,
            fields: [
                {
                    label: 'Service type specific fields wise notification',
                    key: 'srvc_type_specific_fields_wise_notification',
                    formItemProps: {
                        style: {
                            display: 'none',
                        },
                    },
                },
                {
                    key: `sp_srvc_type_fields_wise_notification`,
                    render: () => {
                        return (
                            <SpecificFieldsWiseNotificationModule
                                srvc_type_id={this.formRef?.current?.getFieldValue(
                                    'srvc_type_id'
                                )}
                                authorities_list={this.getAuthoritiesList()}
                                specific_fields={
                                    this.state.viewData
                                        ?.brand_wise_specific_fields
                                }
                                initialValue={this.getSpecificFieldsNotificationJson()}
                                onChange={(newObj) => {
                                    // console.log("newObj",newObj);
                                    this.formRef.current.setFieldsValue({
                                        srvc_type_specific_fields_wise_notification:
                                            JSON.stringify(newObj),
                                    });
                                    this.refresh();
                                }}
                                isSrvcPrvdrTab
                                sp_specific_fields={this.formRef?.current?.getFieldValue(
                                    'sp_cust_fields_json'
                                )}
                            />
                        );
                    },
                },
            ],
        };
        return meta;
    };

    getSpecificFieldsNotificationJson() {
        if (this.state.editMode) {
            if (this.formRef?.current?.getFieldValue) {
                const fieldJson = this.formRef.current.getFieldValue(
                    'srvc_type_specific_fields_wise_notification'
                );
                if (fieldJson) {
                    return JSON.parse(fieldJson);
                }
            }
            let initialFieldJson =
                this.state.viewData?.form_data
                    ?.srvc_type_specific_fields_wise_notification;
            if (initialFieldJson) {
                return JSON.parse(initialFieldJson);
            }
        }
        return {};
    }

    getBillingMasterConfigMeta(initialValues) {
        return {
            fields: [
                {
                    //All data of Billing section will be saved under this key and this is to be done later
                    label: 'Service type billing config json for billing',
                    key: 'srvc_type_billing_config_for_billing',
                    formItemProps: {
                        style: {
                            display: 'none',
                        },
                    },
                },
                {
                    label: 'Service type discounting config json for billing',
                    key: 'srvc_type_billing_discounting_rule_config',
                    formItemProps: {
                        style: {
                            display: 'none',
                        },
                    },
                },
                {
                    label: 'Service type additional config json for billing',
                    key: 'srvc_type_additional_billing_config',
                    formItemProps: {
                        style: {
                            display: 'none',
                        },
                    },
                },
                {
                    key: `srvc_type_billing_master_config`,
                    render: () => {
                        return (
                            <Tabs
                                defaultActiveKey="billing"
                                className="gx-mb-2"
                            >
                                <Tabs.TabPane tab="Billing" key="billing">
                                    {/* Create a new component for Billing to be done later */}
                                    <FormBuilder
                                        form={this.formRef}
                                        meta={this.getBillingformMeta(
                                            initialValues
                                        )}
                                    />
                                </Tabs.TabPane>

                                <Tabs.TabPane
                                    tab="Discounting"
                                    key="discounting"
                                >
                                    <FormBuilder
                                        form={this.formRef}
                                        meta={this.getDiscountingBillingformMeta(
                                            initialValues
                                        )}
                                    />
                                </Tabs.TabPane>

                                <Tabs.TabPane tab="Additional" key="additional">
                                    <FormBuilder
                                        form={this.formRef}
                                        meta={this.getAdditionalBillingformMeta(
                                            initialValues
                                        )}
                                    />
                                </Tabs.TabPane>
                                <Tabs.TabPane
                                    tab="Deduction"
                                    key="deduction_tab"
                                >
                                    <FormBuilder
                                        form={this.formRef}
                                        meta={this.getDeductionConfigMeta(
                                            initialValues
                                        )}
                                    />
                                </Tabs.TabPane>
                            </Tabs>
                        );
                    },
                },
            ],
        };
    }
    getDeploymentConfigMeta = (initialValues) => {
        if (this.state.editMode && !this.state.editModeForceRefreshDone) {
            // refreshing state to get form ref
            this.setState({ editModeForceRefreshDone: true });
            return;
        }
        const startTimeFrEndTime = this.formRef.current?.getFieldValue(
            'deployment_time_slot_lower_limit'
        );
        let role_vs_subtask_fields = [];
        let possible_roles = this.state.viewData?.role_list || [];
        var deployment_roles =
            this.formRef?.current?.getFieldValue('deployment_possible_roles') ||
            this.state.viewData?.form_data?.deployment_possible_roles;
        if (deployment_roles) {
            deployment_roles.forEach((singleDeploymentRoleId) => {
                let role_details =
                    possible_roles.filter(
                        (singleRole) =>
                            singleRole.value == singleDeploymentRoleId
                    )[0] || {};

                role_vs_subtask_fields.push({
                    key: `sbtsk_fr_${singleDeploymentRoleId}`,
                    label: `Select subtask for ${role_details.label}`,
                    required: true,
                    widget: 'select',
                    options:
                        this.state.viewData?.applicable_subtasks_list || [],
                });
            });
        }
        if (
            initialValues &&
            initialValues['deployment_time_slot_lower_limit'] == undefined
        ) {
            initialValues['deployment_time_slot_lower_limit'] = startOfDay;
            initialValues['deployment_time_slot_upper_limit'] = endOfDay;
        }
        const meta = {
            columns: 1,
            formItemLayout: null,
            initialValues: initialValues,
            fields: [
                {
                    key: 'sp_deployment_who_can_edit',
                    label: 'Who can edit deployment ?',
                    widget: 'select',
                    widgetProps: {
                        mode: 'multiple',
                        allowClear: true,
                        showSearch: true,
                    },
                    options: this.state.viewData?.role_list,
                },
                {
                    key: 'deployment_time_slot_lower_limit',
                    label: (
                        <span>
                            <i className="icon icon-timepicker gx-mr-2"></i>
                            Start Time Slot (Lower Limit)
                        </span>
                    ),
                    widget: TimePickerWidget,
                    widgetProps: {
                        beginLimit: startOfDay,
                        endLimit: endOfDay,
                        step: 15,
                        onChange: (e) => {
                            this.refresh();
                        },
                    },
                },
                {
                    key: 'deployment_time_slot_upper_limit',
                    label: (
                        <span>
                            <i className="icon icon-timepicker gx-mr-2"></i>End
                            Time Slot (Upper Limit)
                        </span>
                    ),
                    widget: TimePickerWidget,
                    widgetProps: {
                        beginLimit: startTimeFrEndTime || startOfDay,
                        endLimit: endOfDay,
                        step: 15,
                    },
                },
                {
                    key: `who_cannot_download_site_level_attendance`,
                    label: 'Who cannot download site level attendance ',
                    widget: 'select',
                    placeholder: 'Select roles',
                    onChange: () => this.refresh(),
                    widgetProps: {
                        mode: 'multiple',
                        allowClear: true,
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                    options: this.state.viewData?.role_list || [],
                },
                {
                    key: `deployment_possible_roles`,
                    label: 'Please select roles to deploy',
                    widget: 'select',
                    placeholder: 'Select roles',
                    onChange: () => this.refresh(),
                    widgetProps: {
                        mode: 'multiple',
                        allowClear: true,
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                    options: this.state.viewData?.role_list || [],
                },
                {
                    key: 'show_role_indicator_on_calendar',
                    label: 'Show role indicator on calendar',
                    widget: 'checkbox',
                    onChange: () => this.refresh(),
                },
                {
                    key: 'show_deployed_role_on_calendar',
                    widget: 'radio-group',
                    options: [
                        {
                            value: 'show_deployed_role',
                            label: 'Show deployed role',
                        },
                        { value: 'show_role_code', label: 'Show role code' },
                    ],
                    initialValue: 'show_deployed_role',
                    formItemProps: {
                        style: {
                            display: this.formRef?.current?.getFieldValue(
                                'show_role_indicator_on_calendar'
                            )
                                ? 'block'
                                : 'none',
                        },
                    },
                },
                ...role_vs_subtask_fields,
            ],
        };
        return meta;
    };

    getDailyUpdatesMeta = (initialValues) => {
        let possible_roles = this.state.viewData?.role_list || [];
        var daily_update_issue_fields = this.formRef?.current?.getFieldValue(
            'daily_update_issue_form_fields'
        );
        if (daily_update_issue_fields == undefined) {
            daily_update_issue_fields =
                initialValues?.daily_update_issue_form_fields;
        }
        var sp_daily_update_form_fields = this.formRef?.current?.getFieldValue(
            'sp_daily_update_form_fields'
        );
        if (sp_daily_update_form_fields == undefined) {
            sp_daily_update_form_fields =
                initialValues?.sp_daily_update_form_fields;
        }
        const isTrackingAllLineItemWiseProgress =
            this.formRef?.current?.getFieldValue(
                'daily_update_track_line_item_progress'
            ) ||
            this.state.viewData?.form_data
                ?.daily_update_track_line_item_progress;
        const meta = {
            columns: 1,
            formItemLayout: null,
            initialValues: initialValues,
            fields: [
                {
                    render: () => (
                        <h3 className="gx-mb-2">
                            Configure daily updates form
                        </h3>
                    ),
                },
                {
                    key: 'daily_update_who_can_edit',
                    label: 'Who can edit daily updates ?',
                    widget: 'select',
                    widgetProps: {
                        mode: 'multiple',
                    },
                    options: possible_roles,
                },
                {
                    key: 'who_cannot_download_sp_daily_updates',
                    label: 'Who cannot download daily updates',
                    widget: 'select',
                    widgetProps: {
                        mode: 'multiple',
                        allowClear: true,
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                    options: possible_roles,
                },
                {
                    label: 'Daily update form fields',
                    key: 'sp_daily_update_form_fields',
                    widget: 'textarea',
                    placeholder: 'Please paste the JSON here',
                    onChange: (event) => {
                        this.forceUpdate();
                    },
                },
                {
                    render: () => {
                        return (
                            <div>
                                {sp_daily_update_form_fields != '' && (
                                    <div>
                                        <Button
                                            onClick={(e) =>
                                                this.handleShowFieldsPreviewClick(
                                                    sp_daily_update_form_fields
                                                )
                                            }
                                            icon={<ZoomInOutlined />}
                                        >
                                            Form preview
                                        </Button>
                                    </div>
                                )}
                                <Link
                                    to={
                                        '/fields-creator?edit=' +
                                        encodeURIComponent(
                                            sp_daily_update_form_fields
                                        )
                                    }
                                    target="_blank"
                                >
                                    Open field creator {'-->'}
                                </Link>
                            </div>
                        );
                    },
                },
                {
                    render: () => <hr></hr>,
                },
                {
                    key: `daily_update_will_have_issues`,
                    label: 'Will have issues?',
                    widget: 'radio-group',
                    options: ['Yes', 'No'],
                },
                {
                    label: 'Issue form fields',
                    key: 'daily_update_issue_form_fields',
                    widget: 'textarea',
                    placeholder: 'Please paste the JSON here',
                    onChange: (event) => {
                        this.forceUpdate();
                    },
                },
                {
                    render: () => {
                        return (
                            <div>
                                {daily_update_issue_fields != '' && (
                                    <div>
                                        <Button
                                            onClick={(e) =>
                                                this.handleShowFieldsPreviewClick(
                                                    daily_update_issue_fields
                                                )
                                            }
                                            icon={<ZoomInOutlined />}
                                        >
                                            Form preview
                                        </Button>
                                    </div>
                                )}
                                <Link
                                    to={
                                        '/fields-creator?edit=' +
                                        encodeURIComponent(
                                            daily_update_issue_fields
                                        )
                                    }
                                    target="_blank"
                                >
                                    Open field creator {'-->'}
                                </Link>
                            </div>
                        );
                    },
                },
                {
                    render: () => <hr></hr>,
                },
                {
                    key: 'daily_progress_update_mode',
                    label: 'Daily progress update mode',
                    widget: 'select',
                    options: DailyUpdateModes,
                },
                {
                    key: 'daily_update_track_line_item_progress',
                    label: 'Track line item wise progress & assignment?',
                    tooltip:
                        'Updater will be able to update line item wise progress and cummulative progress will be calculated automatically',
                    widget: 'radio-group',
                    options: ['Yes', 'No'],
                    onChange: () => {
                        this.refresh();
                    },
                },
                ...(isTrackingAllLineItemWiseProgress === 'Yes'
                    ? [
                          {
                              key: 'show_line_item_by_selection',
                              widget: (props) => (
                                  <InlineCheckbox
                                      label="Show line items by selection"
                                      tooltip="If enabled, line item progress will be shown only for the selected line items."
                                      value={props.value}
                                      onChange={(e) =>
                                          props.onChange(e.target.checked)
                                      }
                                  />
                              ),
                          },
                      ]
                    : []),
                {
                    key: 'daily_update_dynamic_line_item_wise_files',
                    label: 'Enable line item wise photos/videos ?',
                    tooltip:
                        'The updater will be able to upload photo videos with respect to each line item (in addition to a general photo section)',
                    widget: 'radio-group',
                    options: ['Yes', 'No'],
                },
            ],
        };
        return meta;
    };

    refresh() {
        this.setState({
            render_helper: !this.state.render_helper,
        });
    }

    handleShowFieldsPreviewClick(customFields) {
        this.setState({
            showFormPreview: true,
            formPreviewMeta: customFields,
        });
    }

    getTabularViewFieldsConfigMeta() {
        return {
            formItemLayout: null,
            fields: [
                {
                    label: 'Select table columns',
                    key: 'srvc_type_tabular_view_columns',
                    widget: 'select',
                    options: getSrvcReqStaticAndCustomPossibleFields(
                        this.getPossibleSpecificFields()
                    ),
                    widgetProps: {
                        mode: 'multiple',
                        allowClear: true,
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                },
            ],
        };
    }

    getViewFieldsConfigMeta() {
        return {
            formItemLayout: null,
            fields: [
                {
                    label: 'Select customer request filters',
                    key: 'srvc_type_view_columns',
                    widget: 'select',
                    options: getSrvcReqStaticAndCustomPossibleFieldsFilter(
                        this.getPossibleSpecificFields(),
                        this.getAuthoritiesList()
                    ),
                    widgetProps: {
                        mode: 'multiple',
                        allowClear: true,
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                },
            ],
        };
    }
    getSelectedSpAuthoritiesRoleList = () => {
        var seletcedRoleIds =
            this.formRef?.current?.getFieldValue('authority_id');
        let selectedRoleData = [];
        if (seletcedRoleIds) {
            seletcedRoleIds.forEach((singleRole) => {
                const roleData = this.state.viewData?.role_list.find(
                    (singleAuthority) => singleAuthority.value === singleRole
                );
                if (roleData) {
                    selectedRoleData.push({
                        key: roleData.value,
                        label: roleData.label,
                        value: roleData.value,
                    });
                }
            });
        }
        return selectedRoleData;
    };

    getSpAuthoritiesMeta = () => {
        const initialValues = this.state.editMode
            ? this.state.viewData?.form_data
            : {};

        let roleWiseSpecificFields = [];
        let enableLocGrpFilteration = [];
        let selected_srvc_prvdr_roles_authorities = [];
        let authorities_role_list = this.state.viewData?.role_list;
        var seletcedRoleIds =
            this.formRef?.current?.getFieldValue('authority_id');
        var isFilterationEnable = this.formRef?.current?.getFieldValue(
            'enable_location_group_based_filteration'
        );
        if (seletcedRoleIds) {
            seletcedRoleIds.forEach((singleRole) => {
                let seletcedRoleTitle = authorities_role_list?.filter(
                    (singleAuthority) => singleAuthority.value == singleRole
                )?.[0]?.label;

                let dummyObj = {
                    label: seletcedRoleTitle,
                    key: singleRole + '_enable_cross_visibility_of_authorities',
                    widget: 'checkbox',
                };
                selected_srvc_prvdr_roles_authorities.push(dummyObj);

                let roleWiseSpecificFieldsobj = {
                    label:
                        seletcedRoleTitle +
                        ' Specific Fields (only ' +
                        seletcedRoleTitle +
                        ' of the selected service request can edit these fields)',
                    key: 'srvc_authority_' + singleRole + '_specific_fields',
                    widget: 'select',
                    options: this.getVerticalOrSelectedSrvcTypeSpecificFields(
                        true,
                        [],
                        true
                    ),
                    widgetProps: {
                        mode: 'multiple',
                        allowClear: true,
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                    // rules : [{
                    //     required: true,
                    //     message : 'Please select specific Fields'
                    // }],
                };
                roleWiseSpecificFields.push(roleWiseSpecificFieldsobj);
            });
            if (seletcedRoleIds?.length > 0) {
                let obj = {
                    label: 'Enable location group based filteration',
                    key: 'enable_location_group_based_filteration',
                    widget: 'checkbox',
                    onChange: () => this.refresh(),
                };
                enableLocGrpFilteration.push(obj);
            }

            if (isFilterationEnable && seletcedRoleIds?.length > 0) {
                let data = {
                    label: 'Select authorities to be filtered',
                    key: 'select_authorities_to_be_filtered_fr_loc_grp',
                    widget: 'select',
                    widgetProps: {
                        mode: 'multiple',
                        allowClear: true,
                        optionFilterProp: 'children',
                    },
                    options: this.getSelectedSpAuthoritiesRoleList(),
                };
                enableLocGrpFilteration.push(data);
            }
        }

        let selected_roles_srvc_authorities_seperator = [];
        if (selected_srvc_prvdr_roles_authorities.length > 0) {
            let dummyObj = {
                render: () => {
                    return (
                        <Alert
                            message="Enable cross visibility of selected authorities"
                            type="info"
                        />
                    );
                },
            };
            selected_roles_srvc_authorities_seperator.push(dummyObj);
        }

        const meta = {
            columns: 1,
            formItemLayout: null,
            initialValues: initialValues,
            fields: [
                {
                    key: 'authority_id',
                    label: 'Select roles',
                    widget: 'select',
                    widgetProps: {
                        mode: 'multiple',
                        allowClear: true,
                        optionFilterProp: 'children',
                    },
                    options: this.state.viewData.role_list,
                    placeholder: 'Please select role',
                    onChange: (value) => {
                        const currentSelectedRolesFrAutohrities =
                            this.formRef.current.getFieldValue(
                                'select_authorities_to_be_filtered_fr_loc_grp'
                            ) || [];
                        // Filter the current authorities to only include those that are still selected as auhtorities
                        const updatedSelectedAuthorities =
                            currentSelectedRolesFrAutohrities.filter((auth) =>
                                value.includes(auth)
                            );
                        //update the select_authorities_to_be_filtered_fr_loc_grp
                        this.formRef.current.setFieldsValue({
                            select_authorities_to_be_filtered_fr_loc_grp:
                                updatedSelectedAuthorities,
                        });
                        this.refresh();
                    },
                },
                ...selected_roles_srvc_authorities_seperator,
                ...selected_srvc_prvdr_roles_authorities,
                ...roleWiseSpecificFields,
                ...enableLocGrpFilteration,
            ],
        };
        return meta;
    };

    getVerticalOrSelectedSrvcTypeSpecificFields(
        Files = false,
        selectedCustomFields = [],
        onlyAttachments = false
    ) {
        //Make a dynamic authorities role_access base on selected role
        let brandSrvcTypeDetails =
            this.state.viewData.brand_wise_specific_fields;
        let verticalOrSelectedSrvcTypeSpecificFields = [];
        let seletcedSrvcType =
            this.formRef?.current?.getFieldValue('srvc_type_id');
        if (seletcedSrvcType) {
            seletcedSrvcType.forEach((singleSrvcType) => {
                let singleBrandSrvcTypeDeatils = brandSrvcTypeDetails?.filter(
                    (singleSrvcTypeId) =>
                        singleSrvcTypeId.service_type_id == singleSrvcType
                )?.[0];

                if (singleBrandSrvcTypeDeatils == undefined) {
                    return;
                }
                let org_name = singleBrandSrvcTypeDeatils?.org_name;
                let service_types_name =
                    singleBrandSrvcTypeDeatils?.service_types_name;
                let srvc_type_specific_fields =
                    singleBrandSrvcTypeDeatils?.specific_fields;
                console.log(
                    'srvc_type_specific_fields',
                    srvc_type_specific_fields
                );
                if (srvc_type_specific_fields) {
                    // let dummyVerticalOrSelectedSrvcTypeSpecificFields = this.getVerticalOrSelectedSrvcTypeSpecificFields(customFields,Files=false,selectedCustomFields=[])
                    // verticalOrSelectedSrvcTypeSpecificFields = [...verticalOrSelectedSrvcTypeSpecificFields,...dummyVerticalOrSelectedSrvcTypeSpecificFields];
                    let translatedFields = Files
                        ? decodeFieldsMetaFrmJson(
                              srvc_type_specific_fields,
                              undefined,
                              true
                          )
                        : decodeFieldsMetaFrmJson(srvc_type_specific_fields);
                    if (translatedFields) {
                        translatedFields.forEach((singleTranslatedFields) => {
                            if (
                                singleTranslatedFields?.label &&
                                (!Files ||
                                    (Files &&
                                        !selectedCustomFields?.includes(
                                            singleTranslatedFields?.key
                                        )))
                            ) {
                                let dummyObj = {
                                    label: `${org_name} - ${service_types_name} - ${singleTranslatedFields?.label}`,
                                    value: singleTranslatedFields?.key,
                                };
                                if (!onlyAttachments) {
                                    verticalOrSelectedSrvcTypeSpecificFields.push(
                                        dummyObj
                                    );
                                } else if (
                                    !singleTranslatedFields.cust_component ||
                                    singleTranslatedFields?.cust_component ==
                                        'Files'
                                ) {
                                    verticalOrSelectedSrvcTypeSpecificFields.push(
                                        dummyObj
                                    );
                                }
                            }
                        });
                    }
                }
            });
        }
        //Get service provider specific details
        let srvc_prvdr_specific_fields = this.formRef?.current?.getFieldValue(
            'sp_cust_fields_json'
        );
        if (srvc_prvdr_specific_fields) {
            srvc_prvdr_specific_fields = Files
                ? decodeFieldsMetaFrmJson(
                      srvc_prvdr_specific_fields,
                      undefined,
                      true
                  )
                : decodeFieldsMetaFrmJson(srvc_prvdr_specific_fields);
        }
        if (srvc_prvdr_specific_fields) {
            srvc_prvdr_specific_fields.forEach((singlesrvcPrvdrSpcField) => {
                if (
                    singlesrvcPrvdrSpcField?.label &&
                    (!Files ||
                        (Files &&
                            !selectedCustomFields?.includes(
                                singlesrvcPrvdrSpcField?.key
                            )))
                ) {
                    let srvcPrvdrSpecificFields = {
                        label: `Wify - ${singlesrvcPrvdrSpcField?.label}`,
                        value: singlesrvcPrvdrSpcField?.key,
                    };
                    if (!onlyAttachments) {
                        verticalOrSelectedSrvcTypeSpecificFields.push(
                            srvcPrvdrSpecificFields
                        );
                    } else if (
                        !singlesrvcPrvdrSpcField.cust_component ||
                        singlesrvcPrvdrSpcField?.cust_component == 'Files'
                    ) {
                        verticalOrSelectedSrvcTypeSpecificFields.push(
                            srvcPrvdrSpecificFields
                        );
                    }
                }
            });
        }
        return verticalOrSelectedSrvcTypeSpecificFields;
    }

    getRatingConfigMeta(initialValues) {
        const enableRatingValue =
            this.formRef?.current?.getFieldValue('enable_sp_rating');
        return {
            fields: [
                {
                    key: 'enable_sp_rating',
                    label: 'Enable Ratings',
                    widget: 'checkbox',
                    onChange: () => this.refresh(),
                },
                ...(enableRatingValue
                    ? [
                          {
                              key: `vertical_type_rating_config`,
                              render: () => {
                                  return (
                                      <Tabs
                                          defaultActiveKey="sp_rating"
                                          className="gx-mb-2"
                                      >
                                          <Tabs.TabPane
                                              tab="Authorities"
                                              key="authorities"
                                          >
                                              <FormBuilder
                                                  form={this.formRef}
                                                  meta={getRatingFormMeta(
                                                      initialValues,
                                                      this.getParamsFrSpAuthorityRatings()
                                                  )}
                                              />
                                          </Tabs.TabPane>
                                          (
                                          <Tabs.TabPane
                                              tab="Assignees"
                                              key="sp_assignees"
                                          >
                                              <FormBuilder
                                                  form={this.formRef}
                                                  meta={getRatingFormMeta(
                                                      initialValues,
                                                      this.getParamsFrSpAssigneeRatings()
                                                  )}
                                              />
                                          </Tabs.TabPane>
                                          )
                                      </Tabs>
                                  );
                              },
                          },
                      ]
                    : []),
            ],
        };
    }

    getSpPayoutsConfigMeta() {
        // here
        const enableSpPayouts =
            this.formRef?.current?.getFieldValue('enable_sp_payouts');
        const selectedSPAuthorities =
            this.formRef?.current?.getFieldValue('authority_id');
        const allOrgRoles = this.state.viewData?.role_list;
        const deploymentRoles = this.formRef?.current?.getFieldValue(
            'deployment_possible_roles'
        );
        let spAuthoritiesFrOptions = [];
        if (selectedSPAuthorities && allOrgRoles) {
            spAuthoritiesFrOptions = allOrgRoles.filter((role) =>
                selectedSPAuthorities.includes(role.value)
            );
        }

        let spAuthoritiesFrVendorOptions = [];
        if (selectedSPAuthorities || deploymentRoles) {
            // Combine the selected authorities and deployment roles into a single array
            const combinedAuthorities = [
                ...(selectedSPAuthorities || []),
                ...(deploymentRoles || []),
            ];

            // Create a Set to get distinct values
            const distinctAuthorities = new Set(combinedAuthorities);

            // Filter allOrgRoles to include only those with distinct authorities
            spAuthoritiesFrVendorOptions = allOrgRoles.filter((role) =>
                distinctAuthorities.has(role.value)
            );
        }

        const spPayoutsConfig = this.getConfigFrSpPayouts();

        const addPayoutUserType = this.formRef?.current?.getFieldValue(
            'select_who_can_add_payout'
        );

        let userSelector = {
            key: 'which_static_user_can_add_sp_payout',
            widget: UserSelectorWidget,
            placeholder: 'Search User',
            className: 'wy-pl-1rem',
            required: true,
            widgetProps: {
                mode: 'multiple',
                allowClear: true,
            },
        };

        let authoritySelector = {
            key: 'which_sp_authority_can_add_sp_payout',
            widget: 'select',
            placeholder: 'Select Authority',
            className: 'wy-pl-1rem',
            required: true,
            widgetProps: {
                mode: 'multiple',
                allowClear: true,
                showSearch: true,
                optionFilterProp: 'children',
            },
            options: spAuthoritiesFrOptions,
        };

        const selectAddSPPayoutWidgetRights =
            addPayoutUserType === 'authority'
                ? [authoritySelector]
                : addPayoutUserType === 'static_user'
                  ? [userSelector]
                  : [];
        return {
            fields: [
                {
                    key: 'enable_sp_payouts',
                    label: 'Enable Payout',
                    widget: 'checkbox',
                    onChange: (e) => {
                        this.refresh();
                        if (
                            e.target.checked == true &&
                            this.formRef.current.getFieldValue(
                                'srvc_type_sp_payouts_config'
                            ) == undefined
                        ) {
                            this.formRef.current.setFieldsValue({
                                srvc_type_sp_payouts_config: JSON.stringify({
                                    '1236c271-876d-4352-a044-157acbeee076': {
                                        key: '1236c271-876d-4352-a044-157acbeee076',
                                        label: 'Payouts',
                                        total_field_label: '',
                                    },
                                }),
                            });
                        }
                    },
                },
                ...(enableSpPayouts
                    ? [
                          {
                              label: 'Service type sp payouts config json',
                              key: 'srvc_type_sp_payouts_config',
                              formItemProps: {
                                  style: {
                                      display: 'none',
                                  },
                              },
                          },
                          {
                              key: 'SP Payouts',
                              render: () => (
                                  <>
                                      <LineItemManagementModule
                                          form={this.formRef}
                                          onChange={(newObj) => {
                                              this.formRef.current.setFieldsValue(
                                                  {
                                                      srvc_type_sp_payouts_config:
                                                          JSON.stringify(
                                                              newObj
                                                          ),
                                                  }
                                              );
                                              this.refresh();
                                          }}
                                          currValue={spPayoutsConfig}
                                          isVendorPayouts
                                          hideBtn
                                      />
                                  </>
                              ),
                          },
                          {
                              key: 'seperator_6',
                              render: () => (
                                  <div className="gx-mb-3 gx-bg-grey"></div>
                              ),
                          },
                          {
                              key: 'sp_vendor_roles',
                              label: 'Vendor Roles',
                              widget: 'select',
                              required: true,
                              widgetProps: {
                                  mode: 'multiple',
                                  allowClear: true,
                                  showSearch: true,
                                  optionFilterProp: 'children',
                              },
                              options: spAuthoritiesFrVendorOptions,
                          },
                          {
                              key: 'select_who_can_add_payout',
                              label: 'Select who can add payout',
                              widget: 'radio-group',
                              onChange: () => this.refresh(),
                              required: true,
                              options: [
                                  {
                                      value: 'authority',
                                      label: 'Authority',
                                  },
                                  {
                                      value: 'static_user',
                                      label: 'Static User',
                                  },
                              ],
                          },
                          ...selectAddSPPayoutWidgetRights,
                          {
                              key: 'sp_who_will_not_be_able_to_see_the_sp_payout_tab',
                              label: 'Who will not be able to see the SP payout tab',
                              widget: 'select',
                              required: true,
                              widgetProps: {
                                  mode: 'multiple',
                                  allowClear: true,
                                  showSearch: true,
                                  optionFilterProp: 'children',
                              },
                              options: this.state.viewData?.role_list,
                          },
                      ]
                    : []),
            ],
        };
    }

    getParamsFrSpAuthorityRatings = () => {
        return {
            viewData: this.state.viewData,
            formRef: this.formRef,
            editMode: this.state.editMode,
            message: 'Here, you can configure who will rate which authority.',
            keySelectedAuthoritiesorDeployment: 'authority_id',
            keyRoleList: 'role_list',
            collapseKey: 'sp_rating_authority_',
            tabName: 'authority',
            ratingTypeKey: 'sp_rating_type_',
            refresh: () => this.refresh(),
            authorityIdKey: 'authority_id',
            selectedRatingAuthority: 'sp_authority_',
            staticUserKey: 'sp_static_user_',
            templateKey: 'sp_rating_template_',
        };
    };

    getParamsFrSpAssigneeRatings = () => {
        return {
            viewData: this.state.viewData,
            formRef: this.formRef,
            editMode: this.state.editMode,
            message: this.isProjectNature()
                ? 'Here, you can configure who will rate On Field Users.'
                : 'Here, you can configure who will rate subtask assignees.',
            keySelectedAuthoritiesorDeployment: 'deployment_possible_roles',
            keyRoleList: 'onfield_role_list',
            collapseKey: 'sp_rating_assignee_',
            tabName: 'assignee',
            ratingTypeKey: 'sp_rating_type_fr_deployment_',
            refresh: () => this.refresh(),
            authorityIdKey: 'authority_id',
            selectedRatingAuthority: 'sp_authority_fr_deployment_',
            selectedRatingDynamicUserRole:
                'sp_dynamic_user_role_fr_deployment_',
            staticUserKey: 'sp_static_user_fr_deployment_',
            templateKey: 'sp_rating_template_fr_deployment',
            isProjectBased: this.isProjectNature(),
            selectedRolesToBeRatedKey: 'selected_roles_to_be_rated',
            modeOfRatingFrAssigneesKey: 'mode_of_rating_fr_assignees',
            HasMatchingCustomerAccessKey:
                'has_matching_customer_access_fr_assignee_',
            HasMatchingLocationGroupKey:
                'has_matching_location_group_fr_assignee_',
        };
    };
    isProjectNature() {
        return (
            this.formRef?.current?.getFieldValue('vertical_nature') ==
            PROJECT_BASED_SERVICE_TYPE
        );
    }

    getAutomationMeta = () => {
        let _allSelectedSubTasks = [];
        if (
            this.formRef &&
            this.formRef?.current?.getFieldsValue('select_subtask_fr_readiness')
        ) {
            _allSelectedSubTasks = this.getDynamicSbtskFields();
        }

        return {
            fields: [
                {
                    key: `automation_tabs_readiness`,
                    render: () => {
                        return (
                            <Tabs
                                defaultActiveKey="readiness"
                                className="gx-mb-2"
                            >
                                <Tabs.TabPane tab="Readiness" key="readiness">
                                    <FormBuilder
                                        form={this.formRef}
                                        meta={{
                                            formItemLayout: null,
                                            fields: [
                                                {
                                                    label: 'Select subtasks',
                                                    key: 'select_subtask_fr_readiness',
                                                    widget: 'select',
                                                    options: [
                                                        ...this.state.viewData
                                                            ?.applicable_subtasks_list,
                                                    ],
                                                    widgetProps: {
                                                        mode: 'multiple',
                                                        allowClear: true,
                                                        showSearch: true,
                                                        optionFilterProp:
                                                            'children',
                                                        onChange: () =>
                                                            this.refresh(),
                                                    },
                                                },
                                                ..._allSelectedSubTasks,
                                            ],
                                        }}
                                    />
                                </Tabs.TabPane>
                            </Tabs>
                        );
                    },
                },
            ],
        };
    };

    getDynamicSbtskFields() {
        const returnDynamicSbtskFields = [];
        const selectedSbtsks = this?.formRef?.current?.getFieldValue(
            'select_subtask_fr_readiness'
        );
        selectedSbtsks &&
            selectedSbtsks.map((_eachSubTaskSelected) => {
                const selectedSubTaskName =
                    this.state.viewData.applicable_subtasks_list.filter(
                        (_eachSubTask) =>
                            _eachSubTask.value == _eachSubTaskSelected
                    );
                const mandatoryField = {
                    label: (
                        <>
                            Select mandatory field for{' '}
                            <span style={{ fontWeight: '500', marginLeft: 2 }}>
                                {selectedSubTaskName[0].label}
                            </span>
                        </>
                    ),
                    key: `select_mandatory_field_fr_${selectedSubTaskName[0].value}`,
                    widget: 'select',
                    required: true,
                    options: getSrvcReqStaticAndCustomPossibleFields(
                        this.getPossibleSpecificFields(),
                        [
                            'full_address',
                            'srvc_prvdr',
                            'sbtsks',
                            'vertical_title',
                        ],
                        true
                    ),
                    widgetProps: {
                        mode: 'multiple',
                        allowClear: true,
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                };
                returnDynamicSbtskFields.push(mandatoryField);
            });
        return returnDynamicSbtskFields;
    }

    getMetaFrSubtaskTypesConfiguration() {
        const meta = {
            columns: 1,
            formItemLayout: null,
            fields: [
                {
                    key: `applicable_subtask_types_fr_vertical`,
                    label: 'Applicable subtask types (leave empty if all are applicable)',
                    widget: 'select',
                    widgetProps: {
                        mode: 'multiple',
                        allowClear: true,
                    },
                    options: this.getApplicableSubtaskTypes(),
                    onChange: (value) => {
                        this.onApplicableSubtaskTypesFrVerticalChange(value);
                        this.refresh();
                    },
                },
                {
                    key: `select_default_subtask_type`,
                    label: 'Select default subtask type',
                    widget: 'select',
                    options: this.getDefaultApplicableSubtaskTypesList(),
                    widgetProps: {
                        allowClear: true,
                        onChange: (value) => {
                            handleClearSelect(
                                value,
                                this.formRef,
                                'select_default_subtask_type'
                            );
                        },
                    },
                },
            ],
        };
        return meta;
    }

    getApplicableSubtaskTypes() {
        return this.state.viewData?.applicable_subtasks_list || [];
    }
    getDefaultApplicableSubtaskTypesList() {
        const selectedApplicableSubtaskTypes =
            this.formRef?.current?.getFieldValue(
                'applicable_subtask_types_fr_vertical'
            ) || [];
        let applicableSubtaskTypes =
            this.state.viewData?.applicable_subtasks_list || [];
        if (selectedApplicableSubtaskTypes?.length > 0) {
            applicableSubtaskTypes = applicableSubtaskTypes.filter(
                (singleApplicableSubtaskType) =>
                    selectedApplicableSubtaskTypes.includes(
                        singleApplicableSubtaskType.value
                    )
            );
        }
        const defaultSubtaskTypesList =
            applicableSubtaskTypes?.length > 0
                ? applicableSubtaskTypes
                : this.getApplicableSubtaskTypes();
        return defaultSubtaskTypesList;
    }
    onApplicableSubtaskTypesFrVerticalChange(value) {
        const defaultSubtaskType = this.formRef?.current?.getFieldValue(
            'select_default_subtask_type'
        );
        if (!value.includes(defaultSubtaskType)) {
            handleClearSelect(
                undefined,
                this.formRef,
                'select_default_subtask_type'
            );
        }
    }
    render() {
        const { editorItem } = this.props;
        const {
            isFormSubmitting,
            visible,
            isLoadingViewData,
            error,
            viewData,
            showFormPreview,
            formPreviewMeta,
            profitLossTab,
        } = this.state;

        var editorTitle = editorItem?.title;
        var editMode = true;
        if (editorTitle == undefined) {
            editorTitle = 'Add new custom fields';
            editMode = false;
        } else {
            editorTitle = 'Edit custom fields - ' + editorTitle;
        }

        const initialValues = this.state.editMode
            ? this.state.viewData?.form_data?.form_data
            : {};
        //Set default table columns for srvc_type_tabular_view_columns
        if (
            initialValues?.hasOwnProperty('srvc_type_tabular_view_columns') ==
            false
        ) {
            initialValues['srvc_type_tabular_view_columns'] =
                addDefaultKeysForTabularView();
        }
        return visible ? (
            <Modal
                title={`${editorTitle}`}
                visible={visible}
                onOk={this.handleOk}
                confirmLoading={isFormSubmitting}
                width={1300}
                onCancel={this.handleCancel}
                footer={null}
            >
                {isLoadingViewData ? (
                    <div className="gx-loader-view gx-loader-position">
                        <CircularProgress />
                    </div>
                ) : viewData == undefined ? (
                    <p className="gx-text-red">{error}</p>
                ) : (
                    <Form
                        className="ant-col gx-my-1 ant-col-xs-24 gx-mt-0"
                        layout="vertical"
                        ref={this.formRef}
                        onFinish={(data) => {
                            this.submitForm(data);
                        }}
                        initialValues={this.state.editMode ? initialValues : {}}
                    >
                        <Tabs
                            defaultActiveKey="sp_fields"
                            onChange={(activeKey) => {
                                if (activeKey === 'profitLossTab') {
                                    this.setState({
                                        profitLossTab: true,
                                    });
                                }
                            }}
                        >
                            <Tabs.TabPane tab="SP Fields" key="sp_fields">
                                <FormBuilder
                                    meta={this.getMeta()}
                                    form={this.formRef}
                                />
                            </Tabs.TabPane>

                            <Tabs.TabPane
                                tab="Line Item"
                                key="line_item"
                                forceRender={true}
                            >
                                <FormBuilder
                                    meta={this.getMetaFrLineItemConfiguration()}
                                    form={this.formRef}
                                />
                            </Tabs.TabPane>
                            {!this.isProjectNature() && (
                                <Tabs.TabPane
                                    tab="Subtask Types"
                                    key="subtask_types"
                                    forceRender={true}
                                >
                                    <FormBuilder
                                        meta={this.getMetaFrSubtaskTypesConfiguration()}
                                        form={this.formRef}
                                    />
                                </Tabs.TabPane>
                            )}
                            <Tabs.TabPane
                                tab="Deployment"
                                key="deployment"
                                forceRender={true}
                            >
                                <FormBuilder
                                    meta={this.getDeploymentConfigMeta(
                                        initialValues
                                    )}
                                    form={this.formRef}
                                />
                            </Tabs.TabPane>

                            <Tabs.TabPane
                                tab="Daily updates"
                                key="daily_updates"
                            >
                                <FormBuilder
                                    meta={this.getDailyUpdatesMeta(
                                        initialValues
                                    )}
                                    form={this.formRef}
                                />
                            </Tabs.TabPane>

                            <Tabs.TabPane
                                tab="Pricing Master"
                                key="pricing_master"
                                forceRender={false}
                            >
                                <FormBuilder
                                    meta={this.getPricingMasterConfigMeta(
                                        initialValues
                                    )}
                                    form={this.formRef}
                                />
                            </Tabs.TabPane>

                            <Tabs.TabPane
                                tab="Billing"
                                key="billing"
                                forceRender={true}
                            >
                                <FormBuilder
                                    meta={this.getBillingMasterConfigMeta(
                                        initialValues
                                    )}
                                    form={this.formRef}
                                />
                            </Tabs.TabPane>
                            <Tabs.TabPane
                                tab="SP Payouts"
                                key="sp_payouts"
                                forceRender={true}
                            >
                                <FormBuilder
                                    meta={this.getSpPayoutsConfigMeta()}
                                    form={this.formRef}
                                />
                            </Tabs.TabPane>
                            {!this.isProjectNature() && (
                                <Tabs.TabPane
                                    tab="P&L"
                                    key="profitLossTab"
                                    forceRender={true}
                                >
                                    {profitLossTab && (
                                        <ProfitLossConfig
                                            roleList={
                                                this.state.viewData?.role_list
                                            }
                                            form_data={
                                                this.state.viewData?.form_data
                                            }
                                            form={this.formRef}
                                            editMode={this.props.editMode}
                                        />
                                    )}
                                </Tabs.TabPane>
                            )}
                            {this.isProjectNature() && (
                                <Tabs.TabPane
                                    tab="P&L"
                                    key="profitLossTab"
                                    forceRender={true}
                                >
                                    {profitLossTab && (
                                        <ProjectPLConfig
                                            form={this.formRef}
                                            roleList={
                                                this.state.viewData?.role_list
                                            }
                                            form_data={
                                                this.state.viewData?.form_data
                                            }
                                            isVendorPayouts
                                        />
                                    )}
                                </Tabs.TabPane>
                            )}
                            <Tabs.TabPane
                                tab="Notification"
                                key="notification"
                                forceRender={true}
                            >
                                <FormBuilder
                                    meta={this.getFieldsWiseNotificationConfigMeta(
                                        initialValues
                                    )}
                                    form={this.formRef}
                                />
                            </Tabs.TabPane>

                            <Tabs.TabPane
                                tab="Tabular view"
                                key="tabular_view"
                                forceRender={true}
                            >
                                <FormBuilder
                                    meta={this.getTabularViewFieldsConfigMeta(
                                        initialValues
                                    )}
                                    form={this.formRef}
                                />
                            </Tabs.TabPane>

                            <Tabs.TabPane
                                tab="Views"
                                key="views"
                                forceRender={true}
                            >
                                <FormBuilder
                                    meta={this.getViewFieldsConfigMeta()}
                                    form={this.formRef}
                                />
                            </Tabs.TabPane>

                            <Tabs.TabPane
                                tab="SP Authorities"
                                key="sp_authorities"
                                forceRender={true}
                            >
                                <FormBuilder
                                    meta={this.getSpAuthoritiesMeta()}
                                    form={this.formRef}
                                />
                            </Tabs.TabPane>
                            <Tabs.TabPane
                                tab="Automations"
                                key="automations"
                                forceRender={true}
                            >
                                <FormBuilder
                                    meta={this.getAutomationMeta()}
                                    form={this.formRef}
                                />
                            </Tabs.TabPane>
                            <Tabs.TabPane
                                tab="SP Ratings"
                                key="sp_rating"
                                forceRender={true}
                            >
                                <FormBuilder
                                    meta={this.getRatingConfigMeta(
                                        initialValues
                                    )}
                                    form={this.formRef}
                                />
                            </Tabs.TabPane>
                        </Tabs>

                        <Form.Item className="gx-mt-3">
                            <Button
                                type="primary"
                                htmlType="submit"
                                disabled={isFormSubmitting}
                            >
                                {editMode ? 'Save' : 'Submit'}
                            </Button>
                        </Form.Item>
                        {isFormSubmitting ? (
                            <div className="gx-loader-view gx-loader-position">
                                <CircularProgress />
                            </div>
                        ) : null}
                        {error ? <p className="gx-text-red">{error}</p> : null}
                    </Form>
                )}
                {showFormPreview && (
                    <FormPreviewMeta
                        formPreviewMeta={formPreviewMeta}
                        onCancel={() => {
                            this.setState({ showFormPreview: false });
                        }}
                    />
                )}
            </Modal>
        ) : (
            <></>
        );
    }
}

export default SpConfigEditor;
