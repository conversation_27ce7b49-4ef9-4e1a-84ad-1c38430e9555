import { Alert, Input, Table } from 'antd';
import FormBuilder from 'antd-form-builder';
import Column from 'antd/lib/table/Column';
import React, { Component } from 'react';
import './index.css';

const demoColMeta = [
    {
        key: 'srvc_type',
        label: 'Service type',
        widget: 'select',
        widgetProps: {
            disabled: true,
        },
        options: [
            {
                label: 'Homelane - Tickets',
                value: '13', // Service type id
            },
            {
                label: 'Homelane - Lead',
                value: '14', // Service type id
            },
            {
                label: 'Wify - Tickets',
                value: '15', // Service type id
            },
        ],
    },
    {
        key: 'sbtsk_status_key',
        label: 'Subtask Status',
        widget: 'select',
        options: [
            {
                label: '--Select--',
                value: '0',
            },
            {
                label: 'Assigned',
                value: 'kjbhvfkdf',
            },
            {
                label: 'Reached site',
                value: 'khbfgjhdw',
            },
            {
                label: 'Job complete',
                value: 'closed',
            },
        ],
    },
    {
        key: 'srvc_req_status',
        label: 'Service Request Status',
        widget: 'select',
        options: [
            {
                label: '--Select--',
                value: '',
            },
        ],
        dynamicMeta: (row, original_field_meta) => {
            let dynamicMetaRow = { ...original_field_meta };
            let statusesOptionsBySrvcTypeId = {
                15: [
                    {
                        label: 'Open',
                        value: 'open',
                    },
                    {
                        label: 'Scheduled',
                        value: 'ksdfbksldj',
                    },
                    {
                        label: 'Visited',
                        value: 'iuhdfljlk',
                    },
                    {
                        label: 'Closed',
                        value: 'closed',
                    },
                ],
                14: [
                    {
                        label: 'Open',
                        value: 'open',
                    },
                    {
                        label: 'Visited',
                        value: 'iuhdfljlk',
                    },
                    {
                        label: 'Closed',
                        value: 'closed',
                    },
                ],
                13: [
                    {
                        label: 'Open',
                        value: 'open',
                    },
                    {
                        label: 'Closed',
                        value: 'closed',
                    },
                ],
            };
            let optionsFrCurrSrvcTypeId =
                statusesOptionsBySrvcTypeId[row.srvc_type];
            if (optionsFrCurrSrvcTypeId) {
                dynamicMetaRow['options'] = [
                    ...dynamicMetaRow['options'],
                    ...optionsFrCurrSrvcTypeId,
                ];
            }
            // console.log('dynamicMeta original_field_meta',original_field_meta);
            // console.log('dynamicMeta row',row);
            return dynamicMetaRow;
        },
    },
];

const demoRowData = [
    {
        row_id: '13_kjbhvfkdf',
        srvc_type: '13',
        sbtsk_status_key: 'kjbhvfkdf',
    },
];

export default class InputTable extends Component {
    initState = {
        render_helper: false,
        // visible: false,
        // isFormSubmitting: false,
        // viewData: undefined,
        // isLoadingViewData: false,
        // editMode : this.props.editMode,
        // error: ''
    };

    state = this.initState;

    constructor(props) {
        super(props);
    }

    getDataFrTable = () => {
        let rowData = this.props.demoMode ? demoRowData : this.props.rowData;
        return rowData;
    };

    getColMeta = () => {
        let colMeta = this.props.demoMode ? demoColMeta : this.props.colMeta;
        return colMeta;
    };

    refresh = () => {
        this.setState({
            render_helper: !this.state.render_helper,
        });
    };

    render() {
        let { demoMode, onChange } = this.props;

        return (
            <div className="gx-border-1 gx-border-red gx-py-1 table-responsive-wy">
                {demoMode && <Alert message="Running in DEMO MODE!!" />}
                <Table
                    // columns={columns}
                    pagination={
                        !this.props.removePagination
                            ? {
                                  pageSizeOptions: [
                                      '5',
                                      '10',
                                      '50',
                                      '100',
                                      '200',
                                      '500',
                                  ],
                                  defaultPageSize: 5,
                              }
                            : false
                    }
                    dataSource={this.getDataFrTable()}
                    bordered={true}
                >
                    {this.getColMeta().map((singleColFIeldMeta) => (
                        <Column
                            title={singleColFIeldMeta.label}
                            key={singleColFIeldMeta.key}
                            {...(singleColFIeldMeta.disable_filter
                                ? {}
                                : {
                                      filters: singleColFIeldMeta.options.map(
                                          (singleOption) => {
                                              return {
                                                  ...singleOption,
                                                  text: singleOption.label,
                                              };
                                          }
                                      ),
                                      filterSearch: true,
                                      onFilter: (value, record) =>
                                          record[singleColFIeldMeta.key] ==
                                          value,
                                  })}
                            render={(text, record, index) => {
                                let cellFieldMeta;
                                if (singleColFIeldMeta.dynamicMeta) {
                                    cellFieldMeta =
                                        singleColFIeldMeta.dynamicMeta(
                                            record,
                                            singleColFIeldMeta
                                        );
                                } else {
                                    cellFieldMeta = { ...singleColFIeldMeta };
                                }
                                let original_key = cellFieldMeta.key;

                                cellFieldMeta.key =
                                    cellFieldMeta.key + '_' + record['row_id'];

                                cellFieldMeta.label = null;
                                cellFieldMeta.onChange = (value) => {
                                    console.log('field onchange', value);
                                    record[original_key] = value;
                                    this.refresh();
                                    if (onChange) {
                                        onChange(this.getDataFrTable());
                                    }
                                };
                                // console.log('row index',index)
                                // console.log('cellFieldMeta',cellFieldMeta)
                                // console.log('record',record)

                                let duplicateRecord = { ...record };
                                if (record[original_key]) {
                                    duplicateRecord[cellFieldMeta.key] =
                                        record[original_key];
                                }
                                // console.log('duplicateRecord',duplicateRecord)

                                // <span>{getLabelFrmOptionsValue(singleFieldMeta.options,value)}</span>

                                return (
                                    <FormBuilder
                                        meta={cellFieldMeta}
                                        initialValues={duplicateRecord}
                                        // viewMode
                                    />
                                );
                            }}
                        />
                    ))}
                </Table>
                {demoMode && (
                    <div className="gx-border gx-border-blue gx-p-1">
                        {JSON.stringify(this.getDataFrTable())}
                    </div>
                )}
            </div>
        );
    }
}
